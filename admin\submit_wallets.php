<?php


session_start();

header('Content-Type: application/json');



$config = include 'sextractedconfig.php';

$userId = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

if (empty($userId)) {
    header("Location: ../logout.php"); // Replace '/logout' with your actual logout URL
    exit();
}

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Connect to the database
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['message' => 'Database connection failed: ' . $conn->connect_error]);
    exit;
}

// Create the table if it does not exist
$tableSQL = "CREATE TABLE IF NOT EXISTS bitcoinwalletaddressmag (
    id INT AUTO_INCREMENT PRIMARY KEY,
    wallet_address VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if (!$conn->query($tableSQL)) {
    http_response_code(500);
    echo json_encode(['message' => 'Error creating table: ' . $conn->error]);
    exit;
}

// Remove duplicates from the database table
$removeDuplicatesSQL = "
    DELETE t1
    FROM bitcoinwalletaddressmag t1
    INNER JOIN bitcoinwalletaddressmag t2 
    WHERE t1.wallet_address = t2.wallet_address AND t1.id > t2.id;
";
if (!$conn->query($removeDuplicatesSQL)) {
    http_response_code(500);
    echo json_encode(['message' => 'Error removing duplicates: ' . $conn->error]);
    exit;
}

// Insert submitted wallet addresses
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['wallets'])) {
    $wallets = array_unique($_POST['wallets']); // Remove duplicates from submitted data
    $successfulInserts = [];
    $skippedInserts = [];

    $stmt = $conn->prepare("INSERT IGNORE INTO bitcoinwalletaddressmag (wallet_address) VALUES (?)");

    foreach ($wallets as $wallet) {
        
           if (empty(trim($wallet))) { // Skip empty values
        continue;
    }
        
        
        
        $stmt->bind_param("s", $wallet);
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $successfulInserts[] = $wallet;
            } else {
                $skippedInserts[] = $wallet;
            }
        } else {
            http_response_code(500);
            echo json_encode(['message' => 'Error inserting wallet address: ' . $stmt->error]);
            exit;
        }
    }

    $stmt->close();

    echo json_encode([
        'message' => 'Submission completed.',
        'successful_inserts' => $successfulInserts,
        'skipped_inserts' => $skippedInserts
    ]);
} else {
    http_response_code(400);
    echo json_encode(['message' => 'Invalid request.']);
}

// Close the connection
$conn->close();
?>
