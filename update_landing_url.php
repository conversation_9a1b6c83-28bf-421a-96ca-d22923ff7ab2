<?php
// Start the session to access user info
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database credentials
$host = $config['host'];        
$username = $config['username'];         
$password = $config['password'];             
$dbname = $config['dbname'];  

// Check if the user is signed in (for example, check if user_id is set)
if (!isset($_SESSION['user_id'])) {
    echo "Please sign in to update the landing URL.";
    exit;
}

// Get the user ID from the session (assuming session contains 'user_id')
$user_id = $_SESSION['user_id'];

// Connect to the database using the variables above
$conn = new mysqli($host, $username, $password, $dbname);

// Check for connection errors
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if the 'victim_landing_urls' table exists, and create it if not
$tableCheckQuery = "SHOW TABLES LIKE 'victim_landing_urls'";
$result = $conn->query($tableCheckQuery);

if ($result->num_rows == 0) {
    // Create the 'victim_landing_urls' table if it doesn't exist
    $createTableQuery = "
        CREATE TABLE `victim_landing_urls` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `landing_url` varchar(255) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP(),
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(),
            PRIMARY KEY (`id`),
            FOREIGN KEY (`user_id`) REFERENCES `user_profiles` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ";
    if ($conn->query($createTableQuery) === TRUE) {
        echo "Table 'victim_landing_urls' created successfully.";
    } else {
        echo "Error creating table: " . $conn->error;
    }
}

// Check if the URL is provided via POST to update the landing URL
if (isset($_POST['landing_url'])) {
    $landing_url = $_POST['landing_url'];

    // Sanitize the input to avoid security risks
    $landing_url = filter_var($landing_url, FILTER_SANITIZE_URL);

    if (filter_var($landing_url, FILTER_VALIDATE_URL)) {
        // Update the landing URL in the database
        $updateQuery = "UPDATE victim_landing_urls SET landing_url = ?, updated_at = NOW() WHERE user_id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("si", $landing_url, $user_id);

        // Execute the query and check for errors
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo "Landing URL updated successfully!"; // Return success message
            } else {
                // If no row was updated, insert a new one (this might happen if the URL does not exist for the user)
                $insertQuery = "INSERT INTO victim_landing_urls (user_id, landing_url) VALUES (?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("is", $user_id, $landing_url);
                if ($stmt->execute()) {
                    echo "Landing URL added successfully!";
                } else {
                    echo "Error inserting URL: " . $stmt->error;
                }
            }
        } else {
            echo "Error updating URL: " . $stmt->error; // Return error message
        }

        $stmt->close();
    } else {
        echo "Invalid URL."; // Return invalid URL message
    }
}

$conn->close();
?>
