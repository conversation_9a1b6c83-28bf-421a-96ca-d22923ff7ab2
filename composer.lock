{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "1332b0a8645c386609544cf7503b871d", "packages": [{"name": "phpmailer/phpmailer", "version": "v6.9.1", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "039de174cd9c17a8389754d3b877a2ed22743e18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/039de174cd9c17a8389754d3b877a2ed22743e18", "reference": "039de174cd9c17a8389754d3b877a2ed22743e18", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"decomplexity/SendOauth2": "Adapter for using XOAUTH2 authentication", "ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.9.1"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "time": "2023-11-25T22:23:28+00:00"}, {"name": "plisio/plisio-api-php", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/Plisio/plisio-api-php.git", "reference": "1956fdc0e525d8e60b076bf880184a5a33e3fc0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Plisio/plisio-api-php/zipball/1956fdc0e525d8e60b076bf880184a5a33e3fc0d", "reference": "1956fdc0e525d8e60b076bf880184a5a33e3fc0d", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*"}, "type": "library", "autoload": {"psr-4": {"Plisio\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Plisio API library for PHP", "homepage": "https://plisio.net", "keywords": ["bitcoin", "bitcoin cash", "<PERSON><PERSON><PERSON><PERSON>", "ethereum", "gateway", "litecoin", "merchant", "payment", "plisio", "zcash"], "support": {"issues": "https://github.com/Plisio/plisio-api-php/issues", "source": "https://github.com/Plisio/plisio-api-php/tree/v1.0.2"}, "time": "2020-08-18T12:48:24+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}