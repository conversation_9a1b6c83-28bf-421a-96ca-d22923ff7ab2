<?php
// get_user_data.php

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if username is provided
if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'error' => 'Username parameter is required']);
    exit();
}

$username = $_GET['id']; // Get the username from the parameter

include('config.php'); // Your database connection file

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$db", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Query to get the user_id based on the username
    $stmt = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = :username");
    $stmt->execute(['username' => $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        echo json_encode(['success' => false, 'error' => 'User not found']);
        exit();
    }

    // Get the user_id
    $user_id = $user['user_id'];

    // Query to get the settings data based on user_id
    $stmt = $pdo->prepare("SELECT * FROM uservictimlanding_settings WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $user_id]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$settings) {
        echo json_encode(['success' => false, 'error' => 'Settings not found for this user']);
        exit();
    }

    // Remove the user_id from the settings data
    unset($settings['user_id']);

    // Return the settings data as JSON
    echo json_encode(['success' => true, 'settings' => $settings]);

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
