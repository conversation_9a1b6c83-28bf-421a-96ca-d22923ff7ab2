<?php
   require('php_files/authorizer.php');
    require('php_files/db.php');
    require('php_files/functions.php');
    $user_id = $_SESSION['user_id'];

    global $pdo;

    // 1. Fetch the user profile data
    $stmt_profile = $pdo->prepare("
            SELECT 
                up.user_id,
                up.username,
                up.email,
                up.profile_picture,
                up.bio,
                up.background_image,
                up.user_type,
                up.subscribed,
                up.status,
                up.result_mail,
                up.email_log_unlocked,
                up.domain_change_count,
                up.last_domain_change,
                w.balance
            FROM user_profiles AS up
            LEFT JOIN wallet AS w ON up.user_id = w.user_id
            WHERE up.user_id = :user_id
        ");

    // Bind and execute the user profile query
    $stmt_profile->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt_profile->execute();
    $user_profile = $stmt_profile->fetch(PDO::FETCH_ASSOC);

    // 2. Fetch the user subscriptions (if any)
    $stmt_subscriptions = $pdo->prepare("
            SELECT
                us.plan_id,
                us.subscription_start_date,
                us.subscription_end_date,
                sp.plan_name,
                sp.price,
                sp.duration_days,
                sp.description
            FROM user_subscriptions AS us
            LEFT JOIN subscription_plans AS sp ON us.plan_id = sp.id
            WHERE us.user_id = :user_id
            AND us.subscription_end_date > NOW()
        ");

    // Bind and execute the user subscription query
    $stmt_subscriptions->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt_subscriptions->execute();
    $active_subscriptions = $stmt_subscriptions->fetchAll(PDO::FETCH_ASSOC);

    // Prepare the final data array
    $data = [
        'user_profile' => $user_profile,
        'active_subscriptions' => $active_subscriptions
    ];

    $currentDate = new DateTime();
    $currentDate->setTime(0, 0); // Set current date to midnight

    // Now the $data array contains both the user profile and their active subscriptions
    // You can proceed to use this data as needed, for example:
    // echo '<pre>'; print_r($data); echo '</pre>';

    // Optional: Delete inactive plans for the user
    deleteInactivePlans($user_id);

        // Fetch anti-bot settings for a specific user
        $userSettingsStmt = $pdo->prepare("
        SELECT u.user_id AS user_id, u.username, a.type, us.state
        FROM user_profiles u
        JOIN user_antibot_settings us ON u.user_id = us.user_id
        JOIN antibottoggle_states a ON us.antibot_id = a.id
        WHERE u.user_id = :user_id
    ");
    $userSettingsStmt->execute([':user_id' => $user_id]);
    $userSettings = $userSettingsStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<?php require('assets/header.php') ?>
<style>
    #image {
        height: 100px;
        width: 100px;
        border-radius: 10px;
        object-fit: contain;
        display: block;
    }
    .blur {
        background-color: rgba( 0,0,0,0.4 ) ;
        backdrop-filter: blur(8px);
    }
    /*#previewImage {*/
    /*    height: 200px;*/
    /*    width: 200px;*/
    /*    border-radius: 10px;*/
    /*    object-fit: contain;*/
    /*    display: block;*/
    /*}*/
</style>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="container mt-5">
        <div class="profile-header d-md-flex flex-row justify-content-between align-items-center py-2">
            <div class="">
                <img id="image"
                     src="<?= !empty($user_profile['profile_picture']) ? $user_profile['profile_picture'] : BASE_URL . '/uploads/blankprofile.webp' ?>"
                     alt="Profile Picture"
                     class="profile-picture img-thumbnail">
                <button id="uploadBtn" class="btn btn-primary d-block my-2">Upload Profile Picture</button>
                <input type="file" id="imageInput" accept="image/*" style="display: none;">
            </div>
            <div class="card">
                <div class="card-header">
                    Wallet Balance
                </div>
                <div class="card-body">
                    <blockquote class="blockquote mb-0">
                        <p>$<?= isset($user_profile['balance']) ? htmlspecialchars($user_profile['balance']) : 'null' ?></p>
                    </blockquote>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="mb-3 col-md-6">
                <label for="firstName" class="form-label">Username</label>
                <input disabled readonly type="text" class="form-control" id="firstName" value="<?= isset($user_profile['username']) ? htmlspecialchars($user_profile['username']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-6">
                <label for="lastName" class="form-label">Email</label>
                <input disabled readonly type="text" class="form-control" id="lastName" value="<?= isset($user_profile['email']) ? htmlspecialchars($user_profile['email']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-4">
                <label for="organization" class="form-label">Result Email</label>
                <input disabled readonly type="text" class="form-control" id="organization" value="<?= isset($user_profile['result_mail']) ? htmlspecialchars($user_profile['result_mail']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-4">
    <label for="email" class="form-label">Result email status</label>
    <input disabled readonly type="text" class="form-control" id="email" 
        value="<?= isset($user_profile['email_log_unlocked']) 
            ? ($user_profile['email_log_unlocked'] == 1 ? 'Your result email is active' : 'Your result email is not active') 
            : 'null' ?>">
</div>


            

        </div>

        
        

    <!-- Modal for Image Preview -->
    <div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">Preview Selected Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <img id="previewImage" src="" alt="Selected Image" class="img-thumbnail w-50">
                </div>
                <div class="modal-footer">
                    <button id="cancelUpload" type="button" class="btn btn-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                    <button id="confirmUpload" type="button" class="btn btn-primary">Upload</button>
                </div>
            </div>
        </div>
    </div>

</main>

<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        // Trigger file input when "Upload Profile Picture" button is clicked
        $('#uploadBtn').on('click', function() {
            $('#imageInput').click();  // Open file input
        });

        // Handle file input change
        $('#imageInput').on('change', function(event) {
            const file = event.target.files[0];  // Get the selected file
            if (file) {
                const reader = new FileReader();  // Read the file

                // Load the selected image into the modal for preview
                reader.onload = function(e) {
                    $('#previewImage').attr('src', e.target.result);  // Set image preview source
                    $('#previewModal').modal('show');  // Show the modal
                };

                reader.readAsDataURL(file);  // Read the image file as a data URL
            }
        });

        // Handle Upload confirmation
        $('#confirmUpload').on('click', function() {
            // You can now submit the form or upload the file via AJAX
            // Example of submitting via AJAX:
            const formData = new FormData();
            const imageFile = $('#imageInput')[0].files[0];

            formData.append('profilePicture', imageFile);
            formData.append('action', 'updateProfilePicture');

            $.ajax({
                url: '<?= BASE_URL?>/php_files/user_actions.php',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('#image').attr('src', URL.createObjectURL(imageFile));
                    $('#previewModal').modal('hide');  // Hide modal
                },
                error: function(err) {
                    console.error('Upload error:', err);
                }
            });
        });

        // Handle Cancel
        $('#cancelUpload').on('click', function() {
            $('#imageInput').val('');  // Clear the file input if canceled
        });
    });

    

</script>