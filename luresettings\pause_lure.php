<?php
// Database connection and initialization
// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Auto-create tables if not existing
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS lure_pauses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL UNIQUE,
            date_paused DATETIME NOT NULL,
            duration INT NOT NULL,
            unpause_time DATETIME NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        );
    ");
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database connection or initialization failed: ' . $e->getMessage()]);
    exit;
}

// Mock user authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}
$user_id = $_SESSION['user_id'];

// Get input data
$input = json_decode(file_get_contents('php://input'), true);
if (!$input || !isset($input['duration'], $input['date_paused'], $input['unpause_time'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
    exit;
}

$duration = intval($input['duration']);
$date_paused = $input['date_paused'];
$unpause_time = $input['unpause_time'];

try {
    // Check if the user_id exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM lure_pauses WHERE user_id = :user_id");
    $stmt->execute([':user_id' => $user_id]);
    $exists = $stmt->fetchColumn();

    if ($exists) {
        // Update the existing record
        $stmt = $pdo->prepare("
            UPDATE lure_pauses
            SET date_paused = :date_paused, duration = :duration, unpause_time = :unpause_time
            WHERE user_id = :user_id
        ");
        $stmt->execute([
            ':user_id' => $user_id,
            ':date_paused' => $date_paused,
            ':duration' => $duration,
            ':unpause_time' => $unpause_time
        ]);
        echo json_encode(['success' => true, 'message' => 'Record updated successfully']);
    } else {
        // Insert a new record
        $stmt = $pdo->prepare("
            INSERT INTO lure_pauses (user_id, date_paused, duration, unpause_time)
            VALUES (:user_id, :date_paused, :duration, :unpause_time)
        ");
        $stmt->execute([
            ':user_id' => $user_id,
            ':date_paused' => $date_paused,
            ':duration' => $duration,
            ':unpause_time' => $unpause_time
        ]);
        echo json_encode(['success' => true, 'message' => 'Record inserted successfully']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Failed to save pause record: ' . $e->getMessage()]);
}
?>
