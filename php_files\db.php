<?php



// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];



// Auto-detect the domain
if (!defined('DOMAIN')) {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
    $domain = $protocol . $_SERVER['HTTP_HOST'] . '/';
    define('DOMAIN', $domain);
}

// Auto-detect the base URL
if (!defined('BASE_URL')) {
    $base_url = rtrim(DOMAIN, '/'); // Remove the trailing slash if needed
    define('BASE_URL', $base_url);
}


//define('PLISIO_API_KEY', 'QKpFcZjWtlCA2N9PgWMQE2sGLcKYKJxb44pK65Y6WkFICUihQs7qRKpZ8jD59O21');

try {
    // Create a new PDO instance
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);

//     // Super admin details
//     // Define super admin details
//     $superAdminEmail = '<EMAIL>';
//     $superAdminUsername = 'Superadmin';
//     $superAdminPassword = password_hash('!iamtheadmin', PASSWORD_BCRYPT);

// // Database connection using PDO
//     try {
//         // Check if super admin already exists
//         $query = "SELECT COUNT(*) AS count FROM user_profiles WHERE username = :username OR email = :email";
//         $stmt = $pdo->prepare($query);
//         $stmt->execute([
//             ':username' => $superAdminUsername,
//             ':email' => $superAdminEmail
//         ]);
//         $result = $stmt->fetch(PDO::FETCH_ASSOC);
//         $count = $result['count'];

//         if ($count == 0) {
//             // Insert the super admin
//             $insertQuery = "INSERT INTO user_profiles (username, email, password, user_type) VALUES (:username, :email, :password, 'superadmin')";
//             $stmt = $pdo->prepare($insertQuery);
//             $success = $stmt->execute([
//                 ':username' => $superAdminUsername,
//                 ':email' => $superAdminEmail,
//                 ':password' => $superAdminPassword
//             ]);
//         }
//     } catch (PDOException $e) {
//         echo "Error: " . $e->getMessage();
//     }

    // Connection successful
    // echo 'Connected to the database successfully.';
} catch (PDOException $e) {
    // Handle connection errors
    echo 'Connection failed: ' . $e->getMessage();
    exit; // Ensure that script execution stops on failure
}



/*// Check if we should run the renewal script (e.g., once every 24 hours)
function shouldRunSubscriptionRenewal() {
    global $pdo;

    // Get the last run time from the database
    $stmt = $pdo->query("SELECT last_run FROM settings WHERE setting_key = 'subscription_renewal_last_run'");
    $lastRun = $stmt->fetch(PDO::FETCH_ASSOC);

    // If last run is more than 24 hours ago, return true
    if (!$lastRun || (strtotime($lastRun['last_run']) <= strtotime('-24 hours'))) {
        return true;
    }

    return false;
}

// Update the last run time after the renewal script runs
function updateLastRunTime() {
    global $pdo;

    // Update the last run time to now
    $stmt = $pdo->prepare("UPDATE settings SET last_run = NOW() WHERE setting_key = 'subscription_renewal_last_run'");
    $stmt->execute();
}

// Trigger the subscription renewal process
if (shouldRunSubscriptionRenewal()) {
    include 'admin/auto_renew_subscriptions.php';
    updateLastRunTime();
}*/
