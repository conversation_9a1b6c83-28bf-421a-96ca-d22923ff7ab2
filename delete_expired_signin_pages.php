<?php
session_start();

header('Content-Type: application/json');

$config = include 'sextractedconfig.php';

$userId = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

if (empty($userId)) {
    header("Location: ../logout.php"); // Replace with your actual logout URL
    exit();
}

$host     = $config['host'];
$dbname   = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Connect to the database
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['message' => 'Database connection failed: ' . $conn->connect_error]);
    exit;
}

// Adjusted Query: Select the record with an expiration date in the future,
// ordered by the soonest expiration date.
$query = "SELECT expired, signinpageurl 
          FROM signinpagemanager 
          WHERE user_id = ? AND expired > NOW() 
          ORDER BY expired ASC 
          LIMIT 1";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $userId); // Bind user_id as integer
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows > 0) {
    $stmt->bind_result($expiredDate, $signinpageurl);
    $stmt->fetch();

    // Debug logs
   // error_log("User ID: " . $userId);
    //error_log("Expired Date from DB: " . $expiredDate);

    // Convert the expired date to a DateTime object.
    $currentDate   = new DateTime(); // Current date
    $expiredDateObj = new DateTime($expiredDate);
  //  error_log("Current Date: " . $currentDate->format('Y-m-d'));
   // error_log("Expired Date Object: " . $expiredDateObj->format('Y-m-d'));

    // Calculate the difference
    $interval    = $currentDate->diff($expiredDateObj);
    $totalDays   = (int)$interval->format('%a');  // total number of days difference
    $approxMonths = (int) floor($totalDays / 30);    // force the value to be an integer

    //error_log("Total Days left: $totalDays, Approximate Months left: $approxMonths");

    // Helper function to delete records and update user_profiles
    function deleteSigninPage($conn, $userId, $signinpageurl) {
        // Delete from signinpagemanager
        $deleteSigninPageQuery = "DELETE FROM signinpagemanager WHERE user_id = ? AND signinpageurl = ?";
        $stmtDelete = $conn->prepare($deleteSigninPageQuery);
        $stmtDelete->bind_param("is", $userId, $signinpageurl);
        $stmtDelete->execute();
        $rowsDeleted = $stmtDelete->affected_rows;
        if ($rowsDeleted > 0) {
           // error_log("Deleted signinpagemanager entry for user_id: $userId");
        } else {
            //error_log("No signinpagemanager entries deleted for user_id: $userId");
        }
        $stmtDelete->close();

        // Update user_profiles
        $deleteUserProfileQuery = "UPDATE user_profiles SET signinpageurl = NULL WHERE user_id = ?";
        $stmtDeleteUserProfile = $conn->prepare($deleteUserProfileQuery);
        $stmtDeleteUserProfile->bind_param("i", $userId);
        $stmtDeleteUserProfile->execute();
        if ($stmtDeleteUserProfile->affected_rows > 0) {
            //error_log("Updated user_profiles for user_id: $userId");
        } else {
            //error_log("No user_profiles updated for user_id: $userId");
        }
        $stmtDeleteUserProfile->close();
    }

    // Check if the session has expired (should not be the case if expired > NOW())
    if ($expiredDateObj < $currentDate) {
        deleteSigninPage($conn, $userId, $signinpageurl);
        //echo json_encode(['message' => 'Your session has expired. The sign-in page URL has been deleted.']);
    } else {
        // Adjust your thresholds as needed.
        // Here, if the session expires in 30 days or less, or approximately 1, 2, or 3 months,
        // we delete the record.
        if ($totalDays <= 30) { // Less than or equal to 30 days left
            deleteSigninPage($conn, $userId, $signinpageurl);
           // echo json_encode(['message' => 'The session is nearing expiration (<= 30 days). Sign-in page URL has been deleted.']);
        } elseif ($approxMonths === 1) { // Approximately 1 month left
            deleteSigninPage($conn, $userId, $signinpageurl);
            //echo json_encode(['message' => 'Your session will expire in about 1 month. The sign-in page URL has been deleted.']);
        } elseif ($approxMonths === 2) { // Approximately 2 months left
            deleteSigninPage($conn, $userId, $signinpageurl);
           // echo json_encode(['message' => 'Your session will expire in about 2 months. The sign-in page URL has been deleted.']);
        } elseif ($approxMonths === 3) { // Approximately 3 months left
            deleteSigninPage($conn, $userId, $signinpageurl);
           // echo json_encode(['message' => 'Your session will expire in about 3 months. The sign-in page URL has been deleted.']);
        } else {
            // If none of the thresholds match, no deletion happens.
           // echo json_encode(['message' => 'Session is active and not near expiration.']);
        }
    }
} else {
    // If no records were found in signinpagemanager, make a GET request to another URL.
    $currentDomain = $_SERVER['HTTP_HOST'];
    $url = "https://$currentDomain/admin/signinpagemanagerapi.php";

    // Initialize cURL session
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    // Execute the GET request
    $response = curl_exec($ch);
    if ($response === false) {
        http_response_code(500);
        echo json_encode(['message' => 'Failed to make request: ' . curl_error($ch)]);
    } else {
        echo json_encode(['message' => '']);
    }

    // Close cURL session
    curl_close($ch);
}

$stmt->close();
$conn->close();
?>
