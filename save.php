<?php
// Start session to access session variables
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./logout.php");
    exit();
}

// Set the content type to JSON
header('Content-Type: application/json');

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php';

// Validate configuration
if (!isset($config['host'], $config['dbname'], $config['username'], $config['password'])) {
    http_response_code(500);
    echo json_encode(['error' => 'Invalid configuration file']);
    exit();
}

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create database connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed', 'details' => htmlspecialchars($conn->connect_error)]);
    exit();
}

// Create table if it doesn't exist
$tableCreationQuery = "
    CREATE TABLE IF NOT EXISTS mailautograb (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        input_value VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
";

if (!$conn->query($tableCreationQuery)) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create table', 'details' => htmlspecialchars($conn->error)]);
    $conn->close();
    exit();
}

$userId = intval($_SESSION['user_id']);

// Handle GET request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $selectQuery = "SELECT input_value FROM mailautograb WHERE user_id = '$userId' ORDER BY created_at DESC LIMIT 1";
    $result = $conn->query($selectQuery);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo json_encode(['success' => true, 'inputValue' => htmlspecialchars($row['input_value'])]);
    } else {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '']);
    }
    $conn->close();
    exit();
}

// Handle POST request
// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $inputValue = $conn->real_escape_string(trim($_POST['inputValue']));
    $userId = intval($_SESSION['user_id']);

    // Check if the record already exists
    $checkQuery = "SELECT id FROM mailautograb WHERE user_id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        // Record exists, update it
        $updateQuery = "UPDATE mailautograb SET input_value = ?, created_at = NOW() WHERE user_id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("si", $inputValue, $userId);

        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Updated successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to update record', 'details' => htmlspecialchars($stmt->error)]);
        }
    } else {
        // Record does not exist, insert a new one
        $insertQuery = "INSERT INTO mailautograb (user_id, input_value) VALUES (?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("is", $userId, $inputValue);

        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'saved successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to save record', 'details' => htmlspecialchars($stmt->error)]);
        }
    }

    // Close the statement
    $stmt->close();
    $conn->close();
    exit();
}


// Unsupported method
http_response_code(405);
echo json_encode(['error' => '']);
$conn->close();
exit();
?>
