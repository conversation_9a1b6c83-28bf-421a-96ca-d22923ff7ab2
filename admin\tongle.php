<?php
// Enable CORS (if needed)
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json");

// Database configuration
include('../database/RaccoonO365BotdatabaseConfig.php');
require '../php_files/db.php';

// Create a connection to the database
$conn = new mysqli($host, $user, $pass, $db);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// List of all toggle types to check
$toggleTypes = ['isCrawler', 'isProxy', 'isVpn', 'isAbuser', 'isBogon', 'isTor', 'isVisitLimit'];

// Array to store the response
$response = [];



if ( isset($_GET['']) && $_GET['username'] !== ""  ) {
    getUserSettingsByUsername( $_GET['username'] );
}


// Loop through each toggle type and get the corresponding cookie or database value
foreach ($toggleTypes as $type) {
    $cookieName = $type . 'Toggle';
    if (isset($_COOKIE[$cookieName])) {
        // Use the cookie value
        $response[$type] = $_COOKIE[$cookieName]; // 'on' or 'off'
    } else {
        // Fallback to database query
        $query = "SELECT state FROM antibottoggle_states WHERE type = '$type' LIMIT 1";
        $result = $conn->query($query);

        if ($result->num_rows > 0) {
            // Return the state if found in the database
            $row = $result->fetch_assoc();
            $response[$type] = $row['state'];
        } else {
            // Default to 'off'
            $response[$type] = 'off';
        }
    }
}

// Fetch the max visit limit from the database
$maxVisitLimitQuery = "SELECT state FROM antibottoggle_states WHERE type = 'maxVisitLimit' LIMIT 1";
$maxVisitLimitResult = $conn->query($maxVisitLimitQuery);

if ($maxVisitLimitResult->num_rows > 0) {
    $maxVisitLimitRow = $maxVisitLimitResult->fetch_assoc();
    $response['maxVisitLimit'] = (int)$maxVisitLimitRow['state']; // Convert to integer
} else {
    $response['maxVisitLimit'] = 20; // Default value if not set
}




$landingPageQuery = "SELECT url FROM landingpageurl WHERE id = 1 LIMIT 1";
$landingPageResult = $conn->query($landingPageQuery);

if ($landingPageResult->num_rows > 0) {
    $landingPageRow = $landingPageResult->fetch_assoc();
    $response['cookieslinklandingpageurl'] = $landingPageRow['url'];
} else {
    $response['cookieslinklandingpageurl'] = '';
}

$ispBlockingQuery = "SELECT state FROM antibottoggle_states WHERE type = 'ispBlocking' LIMIT 1";
$ispBlockingResult = $conn->query($ispBlockingQuery);

if ($ispBlockingResult->num_rows > 0) {
    $ispBlockingRow = $ispBlockingResult->fetch_assoc();
    $response['ispBlocking'] = $ispBlockingRow['state'];
} else {
    $response['ispBlocking'] = 'off';
}

$landingPageToggleQuery = "SELECT is_enabled FROM blockedlandingpageredirect WHERE id = 1 LIMIT 1";
$landingPageToggleResult = $conn->query($landingPageToggleQuery);

if ($landingPageToggleResult->num_rows > 0) {
    $landingPageToggleRow = $landingPageToggleResult->fetch_assoc();
    $response['blockedaccesslanding_pageToggle'] = $landingPageToggleRow['is_enabled'];
} else {
    $response['blockedaccesslanding_pageToggle'] = 'off';
}

$ispNamesQuery = "SELECT isp_name FROM blockedispslist";
$ispNamesResult = $conn->query($ispNamesQuery);
$ispNames = [];

while ($row = $ispNamesResult->fetch_assoc()) {
    $ispNames[] = $row['isp_name'];
}

$response['ispNames'] = $ispNames;

$fetchLandingPageQuery = "SELECT url FROM landing_page WHERE id = 1 LIMIT 1";
$landingPageQueryResult = $conn->query($fetchLandingPageQuery);

if ($landingPageQueryResult->num_rows > 0) {
    $landingPageDataRow = $landingPageQueryResult->fetch_assoc();
    $response['blockedaccesslanding_page'] = $landingPageDataRow['url'];
} else {
    $response['blockedaccesslanding_page'] = '';
}



function getUserSettingsByUsername( $username ) {
    global $pdo;
    // Step 1: Retrieve user_id based on the provided username
    $userQuery = "SELECT user_id FROM user_profiles WHERE username = :username";
    $stmt = $pdo->prepare($userQuery);
    $stmt->execute(['username' => $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        echo json_encode(['status' => 'error', 'message' => 'User not found']);
        exit;
    }

    $user_id = $user['user_id'];

    // Step 2: Retrieve all settings for this user_id
    $settingsQuery = "
        SELECT ats.type, uas.state
        FROM user_antibot_settings uas
        JOIN antibottoggle_states ats ON uas.antibot_id = ats.id
        WHERE uas.user_id = :user_id
    ";
    $stmt = $pdo->prepare($settingsQuery);
    $stmt->execute(['user_id' => $user_id]);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!$settings) {
        echo json_encode(['status' => 'error', 'message' => 'No settings found for this user']);
        exit;
    }

    echo json_encode(['status' => 'success', 'username' => $username, 'settings' => $settings]);
    exit;
}





echo json_encode($response);


// Close the connection
$conn->close();
exit;
