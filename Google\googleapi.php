<?php


session_start();
// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a connection to the MySQL database
$conn = new mysqli($host, $username, $password, $dbname);

// Check if the connection was successful
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set the character set to UTF-8 for proper handling of special characters
$conn->set_charset("utf8");

// Optionally, you can define a function to close the connection when done
function closeDbConnection() {
    global $conn;
    $conn->close();
}


$user_id = $_SESSION['user_id']; // Get signed-in user ID from session
$user_wallet_balance = getUserWalletBalance($user_id); // Get the user's wallet balance

// Ensure necessary tables exist
createTableIfNotExists();

// Show renewal or subscription button
showRenewalButton($user_id);

// Display available plans based on user's current plan
displayAvailablePlans($user_id);



error_log("Debug info: " . var_export($user_id, true));






// Define actions for AJAX calls
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'subscribe') {
        $plan_name = $_POST['plan_name'];
        $price = $_POST['price'];

        storeSubscription($user_id, $plan_name);
        // echo "Successfully subscribed to $plan_name.";
        
     
        
        exit;
    }

    if ($action === 'renew') {
        renewSubscription($user_id);
        exit;
    }
}



// Functions

/**
 * Check and display renewal/subscription buttons based on subscription status.
 */
function showRenewalButton($user_id) {
    global $conn;

    $query = "SELECT * 
              FROM subscribeopenredirect 
              WHERE user_id = $user_id 
              ORDER BY subscription_end_date DESC LIMIT 1";
    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        // Check if the subscription is expired
        if ($row['subscription_end_date'] < date('Y-m-d')) {
        
        
        
        } else {
            echo "Your subscription is active until " . $row['subscription_end_date'] . ".";
        }
    } else {
        // No subscription found
       
       
    }
}

/**
 * Create necessary tables if they do not exist.
 */
function createTableIfNotExists() {
    global $conn;

    // Create `subscribeopenredirect` table
    $query = "CREATE TABLE IF NOT EXISTS subscribeopenredirect (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        subscription_start_date DATE NOT NULL,
        subscription_end_date DATE NOT NULL,
        subscribeopenredirectplan VARCHAR(255) NOT NULL
    )";
    $conn->query($query);

    // Create `openredirectplans` table for subscription plans
    $query = "CREATE TABLE IF NOT EXISTS openredirectplans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_name VARCHAR(255) NOT NULL,
        duration INT NOT NULL,
        price DECIMAL(10, 2) NOT NULL
    )";
    $conn->query($query);

    // Only add unique constraint if it doesn't already exist
    $query = "SHOW INDEX FROM openredirectplans WHERE Key_name = 'plan_name'";
    $result = $conn->query($query);
    if ($result->num_rows == 0) {
        $query = "ALTER TABLE openredirectplans ADD UNIQUE (plan_name)";
        $conn->query($query);
    }

    
// Step 1: Retrieve data from the `subscription_plans` table
$query = "SELECT plan_name, duration_days FROM subscription_plans";
$result = $conn->query($query);

// Step 2: Check if any rows were returned
if ($result->num_rows > 0) {
    // Step 3: Prepare the query to insert into `openredirectplans` with default price of 0
    $insertQuery = "INSERT INTO openredirectplans (plan_name, duration) VALUES ";

    // Collect plan data
    $values = [];
    while ($row = $result->fetch_assoc()) {
        $planName = $conn->real_escape_string($row['plan_name']);
        $duration = (int)$row['duration_days'];
        $values[] = "('$planName', $duration)";
    }

    // Add the collected values to the query
    $insertQuery .= implode(', ', $values);

    // Step 4: Add the ON DUPLICATE KEY UPDATE part to the query
    $insertQuery .= " ON DUPLICATE KEY UPDATE plan_name=VALUES(plan_name), duration=VALUES(duration)";

    // Step 5: Execute the query
    if ($conn->query($insertQuery) === TRUE) {
      //  echo "Records inserted/updated successfully.";
    } else {
       // echo "Error: " . $conn->error;
    }
} else {
   // echo "No data found in subscription_plans table.";
}

}



/**
 * Get the user's wallet balance.
 */
function getUserWalletBalance($user_id) {
    global $conn;
    $query = "SELECT balance FROM wallet WHERE user_id = $user_id";
    $result = $conn->query($query);
    $row = $result->fetch_assoc();
    return $row['balance'];
}

/**
 * Charge the user's wallet balance and record the transaction.
 */
function chargeUserBalance($user_id, $amount) {
    global $conn;
    $balance = getUserWalletBalance($user_id);
    if ($balance >= $amount) {
        $newBalance = $balance - $amount;
        $query = "UPDATE wallet SET balance = $newBalance WHERE user_id = $user_id";
        $conn->query($query);
        storeTransaction($user_id, $amount);
        return true;
    }
    return false;
}

/**
 * Store a wallet transaction record.
 */
function storeSubscription($user_id, $plan_name) {
    global $conn;

    $plan = getPlanDetails($plan_name);
    if (!$plan) {
        echo "Failed to create subscription. Plan not found.";
        return;
    }

    $startDate = date('Y-m-d');
    $endDate = calculateBusinessEndDate($startDate, $plan['duration']);

    // Check if user has an existing subscription (active or expired)
    $query = "SELECT id FROM subscribeopenredirect 
              WHERE user_id = $user_id 
              AND subscribeopenredirectplan = '{$plan['plan_name']}'
              ORDER BY subscription_end_date DESC 
              LIMIT 1";  // Get the most recent subscription

    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        // Update the existing subscription (even if expired)
        $row = $result->fetch_assoc();
        $subscriptionId = $row['id'];

        $query = "UPDATE subscribeopenredirect 
                  SET price = {$plan['price']}, 
                      subscription_start_date = '$startDate', 
                      subscription_end_date = '$endDate',
                      paused_at = NULL
                  WHERE id = $subscriptionId";

        if ($conn->query($query) === TRUE) {
            echo "Subscription updated successfully!";
        } else {
            echo "Error updating subscription: " . $conn->error;
        }
    } else {
        // Insert new subscription if no previous record exists
        $query = "INSERT INTO subscribeopenredirect (user_id, price, subscription_start_date, subscription_end_date, subscribeopenredirectplan)
                  VALUES ($user_id, {$plan['price']}, '$startDate', '$endDate', '{$plan['plan_name']}')";

        if ($conn->query($query) === TRUE) {
            echo "Subscription successfully created!";
        } else {
            echo "Error: " . $conn->error;
        }
    }
}



/**
 * Calculate the subscription end date based on business days.
 */
function calculateBusinessEndDate($startDate, $businessDays) {
    $currentDate = strtotime($startDate);
    $daysAdded = 0;

    while ($daysAdded < $businessDays) {
        $currentDate = strtotime("+1 day", $currentDate);

        // Skip Saturdays and Sundays
        $dayOfWeek = date('N', $currentDate); // 1 (Monday) to 7 (Sunday)
        if ($dayOfWeek < 6) { // Monday to Friday
            $daysAdded++;
        }
    }

    return date('Y-m-d', $currentDate);
}

/**
 * Fetch the details of a plan from `openredirectplans`.
 */
function getPlanDetails($plan_name) {
    global $conn;
    $query = "SELECT * FROM openredirectplans WHERE plan_name = '$plan_name'";
    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    return null;
}


function displayAvailablePlans($user_id) {
    global $conn;

    // Fetch the user's current subscription plan from `user_subscriptions`
    // Join with subscription_plans to get the correct plan name
    $query = "SELECT us.plan_id, sp.plan_name, sp.price, sp.duration_days
              FROM user_subscriptions us
              JOIN subscription_plans sp ON us.plan_id = sp.id
              WHERE us.user_id = $user_id
              ORDER BY us.subscription_end_date DESC LIMIT 1";

    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        // Fetch the plan details
        $row = $result->fetch_assoc();
        $currentPlan = $row['plan_name']; // Correct plan name from subscription_plans
        $price = $row['price'];
        $duration = $row['duration_days'];

        
        
         // Return the active subscription details as an array
        return [
            'plan_name' => $currentPlan,
            'price' => $price,
            'duration_days' => $duration
        ];
        
    } else {
        // No subscription found
        echo "No subscription found.";
       
    }
}





// Query to retrieve all plans from the openredirectplans table
$query = "SELECT plan_name, duration, price FROM openredirectplans";
$result = $conn->query($query);

$currentPlanDetails = displayAvailablePlans($user_id);

// If no active subscription, return and exit
if (!$currentPlanDetails) {
    echo "No active subscription found.";
    return; // Exit if no active subscription is found
}

// Extract the current subscription details from the returned array
$currentPlanName = $currentPlanDetails['plan_name'];
$currentPlanDuration = $currentPlanDetails['duration_days'];


// Query to check if the user has an active subscription
$activeSubscriptionQuery = "SELECT subscribeopenredirectplan, subscription_end_date 
                            FROM subscribeopenredirect 
                            WHERE user_id = $user_id AND subscription_end_date >= CURDATE() 
                            LIMIT 1";
$activeSubscriptionResult = $conn->query($activeSubscriptionQuery);

$activeSubscription = null;
if ($activeSubscriptionResult->num_rows > 0) {
    $activeSubscription = $activeSubscriptionResult->fetch_assoc();
}

// If there is no active subscription, show the specific plan format
if (!$activeSubscription) {
    


// Check if any rows are returned
if ($result->num_rows > 0) {
    // Loop through the results and store values in variables
    while ($row = $result->fetch_assoc()) {
        $planName = $row['plan_name'];
        $planDuration = $row['duration'];
        $planPrice = $row['price'];


        // Check if the plan name and duration match with the active subscription
        if (stripos($planName, $planName) !== false && $currentPlanDuration == $planDuration) {
            
    
            
            
            echo "<div>";
            echo "<strong>" . $planName . "</strong><br>";
            echo "Price: $" . $planPrice . "<br>";
            echo "Duration: " . $planDuration . " Business Days<br>";
                      echo "<button onclick='subscribe(plan)'>Subscribe To Redirect</button>";
            echo "</div><br>";
        }
    }
} else {
    echo "No plans found.";
}

} else {
    // If an active subscription exists, display a message
    echo "You are currently subscribed to the Google redirect <strong>" . $activeSubscription['subscribeopenredirectplan'] . "</strong> until " . $activeSubscription['subscription_end_date'] . ".";
    
    
    
}



?>



 