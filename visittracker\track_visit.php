<?php

// Allow CORS for all origins
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header("Content-Type: application/json");

$config = include 'extractedconfig.php';
$dbName = $config['dbname'];

function initializeDatabase($config) {
    $host = $config['host'];
    $username = $config['username'];
    $password = $config['password'];
    global $dbName;

    $mysqli = new mysqli($host, $username, $password);

    if ($mysqli->connect_error) {
        error_log('Database connection error: ' . $mysqli->connect_error);
        echo json_encode(['status' => 'error', 'message' => 'Database connection failed']);
        exit;
    }

    $dbName = $mysqli->real_escape_string($dbName);
    $result = $mysqli->query("SHOW DATABASES LIKE '$dbName'");
    if ($result->num_rows == 0) {
        $mysqli->query("CREATE DATABASE `$dbName`");
    }
    $mysqli->select_db($dbName);

    return $mysqli;
}

$mysqli = initializeDatabase($config);

// Ensure visitors table exists
$mysqli->query("CREATE TABLE IF NOT EXISTS visitors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    fingerprint VARCHAR(255) NOT NULL,
    ip VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,
    region VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    countrycode VARCHAR(10) NOT NULL,
    zip VARCHAR(20) NOT NULL,
    timezone VARCHAR(100) NOT NULL,
    session_start DATETIME NOT NULL,
    session_end DATETIME NULL,
    pages_visited TEXT NOT NULL,
    session_duration INT NOT NULL,
    visit_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_returning BOOLEAN NOT NULL DEFAULT FALSE,
    os VARCHAR(100) NOT NULL,
    browser VARCHAR(100) NOT NULL,
    browser_engine VARCHAR(100) NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    device_model VARCHAR(100) NOT NULL,
    flag_url VARCHAR(255) NOT NULL,
    device_manufacturer VARCHAR(100) NOT NULL
)");







$inputData = file_get_contents('php://input');
$data = json_decode($inputData, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid JSON data received']);
    exit;
}

$fingerprint = $data['fingerprint'] ?? '';
$ip = $data['ip'] ?? '';
$userAgent = $data['user_agent'] ?? '';
$pagesVisited = is_array($data['pages_visited'] ?? []) ? implode(', ', $data['pages_visited']) : '';
$username = $data['username'] ?? '';
$region = $data['Region'] ?? '';
$city = $data['City'] ?? '';

$countryCode = $data['ecountryCode'] ?? '';
$zip = $data['Zip'] ?? '';
$timezone = $data['Timezone'] ?? '';



if (!empty($countryCode)) {
    // Call the function with the validated country code
    $countryDetails = getCountryDetails($countryCode);
    error_log("Flag URL: " . print_r($countryDetails, true));  // Log the generated flag URL
} else {
    error_log("Country code is empty or not set");
}


file_put_contents('post_data_log.txt', print_r($data, true) . PHP_EOL, FILE_APPEND);

$query = "SELECT user_id FROM user_profiles WHERE username = ?";
$stmt = $mysqli->prepare($query);
$stmt->bind_param('s', $username);
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows > 0) {
    $stmt->bind_result($userId);
    $stmt->fetch();
    logVisitor($mysqli, $userId, $fingerprint, $ip, $userAgent, $region, $city, $countryCode, $zip, $timezone, $pagesVisited);
    
    
     // Call monitorSecurity function with IP
    monitorSecurity($mysqli, $userId, $fingerprint, $ip); 
 
     
     
    echo json_encode(['status' => 'success', 'message' => 'Visitor data logged.']);
    
    
    
    
} else {
    echo json_encode(['status' => 'error', 'message' => 'User not found.']);
}


function logVisitor($mysqli, $userId, $fingerprint, $ip, $userAgent, $region, $city, $countryCode, $zip, $timezone, $pagesVisited) {
    
   // Assuming getDeviceDetails can return NULL for flagUrl
$deviceDetails = getDeviceDetails($userAgent, $countryCode);
$flagUrl = $deviceDetails['flagUrl'] ?? 'default_flag_url';
    
    
    
$country = getCountryName($countryCode);  // Call the function to get the country name
    if (!$country) {
        $country = 'Unknown';  // Provide a default value if country is null
    }

error_log("Country Name: $country");

    // Session start and end
    $sessionStart = date('Y-m-d H:i:s');
    $sessionEnd = date('Y-m-d H:i:s');
    $duration = strtotime($sessionEnd) - strtotime($sessionStart);
    
    
$isReturning = isReturningVisitor($mysqli, $userId, $fingerprint);


// Ensure $isReturning is not null by setting a default value if it is
if ($isReturning === NULL) {
    $isReturning = 0;
}







    // Prepare the query
    $query = "INSERT INTO visitors (user_id, fingerprint, ip, user_agent, region, city, country, countrycode, zip, timezone, session_start, session_end, pages_visited, session_duration, visit_time, is_returning, os, browser, browser_engine, device_type, device_model, flag_url, device_manufacturer) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";


    // Prepare statement
    $stmt = $mysqli->prepare($query);
    if (!$stmt) {
        // Handle error
        die('Query prepare failed: ' . $mysqli->error);
    }



    // Bind the parameters
$currentDateTime = date('Y-m-d H:i:s');
$stmt->bind_param(
    'issssssssssssiissssssss',
    $userId, $fingerprint, $ip, $userAgent, $region, $city, $country, $countryCode, $zip, $timezone, 
    $sessionStart, $sessionEnd, $pagesVisited, $duration, $currentDateTime, 
    $isReturning, $deviceDetails['os'], $deviceDetails['browser'], 
    $deviceDetails['browser_engine'], $deviceDetails['device_type'], 
    $deviceDetails['device_model'], $flagUrl, $deviceDetails['device_manufacturer']
);

    // Execute statement
    if (!$stmt->execute()) {
        // Handle error
        die('Query execution failed: ' . $stmt->error);
    }

// Query to select records where session_end is NULL and other required fields have values
$checkQuery = "
    SELECT id FROM visitors 
    WHERE session_end IS NULL
    AND fingerprint IS NOT NULL
    AND user_id IS NOT NULL
    AND id IS NOT NULL
    AND session_start IS NOT NULL
    AND session_duration IS NOT NULL
    AND visit_time IS NOT NULL
    AND is_returning IS NOT NULL
";

// Prepare and execute the query
$checkStmt = $mysqli->prepare($checkQuery);
if (!$checkStmt) {
    die('Query prepare failed: ' . $mysqli->error);
}
$checkStmt->execute();

// Get result set and delete matching records
$result = $checkStmt->get_result();
while ($record = $result->fetch_assoc()) {
    // Delete the record if session_end is NULL
    $deleteQuery = "DELETE FROM visitors WHERE id = ?";
    $deleteStmt = $mysqli->prepare($deleteQuery);
    $deleteStmt->bind_param("i", $record['id']);
    if (!$deleteStmt->execute()) {
        die('Failed to delete record: ' . $deleteStmt->error);
    }
}

$checkStmt->close();

    
    $stmt->close();
}




 

function isReturningVisitor($mysqli, $userId, $fingerprint) {
    // Count the number of visits for the given fingerprint and user_id
    $query = "SELECT COUNT(*) AS visit_count FROM visitors WHERE fingerprint = ? AND user_id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("si", $fingerprint, $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    // Get the visit count for the fingerprint
    $visitCount = $row['visit_count'];
    
     // Log the fingerprint and its visit count to a file for debugging
    $logMessage = "Fingerprint: $fingerprint - Visits: $visitCount for user_id $userId" . PHP_EOL;
  

    // Update the 'is_returning' column with the visit count for the specific fingerprint
    $updateQuery = "UPDATE visitors SET is_returning = ? WHERE fingerprint = ? AND user_id = ?";
    $updateStmt = $mysqli->prepare($updateQuery);
    $updateStmt->bind_param("isi", $visitCount, $fingerprint, $userId);
    $updateStmt->execute();
    $updateStmt->close();
}
   





// Function to detect device details
function getDeviceDetails($userAgent, $countryCode) {
    if (empty($userAgent)) {
        return [
            'os' => 'Bot',
            'browser' => 'Bot',
            'browser_engine' => 'Bot',
            'device_type' => 'Bot',
            'device_model' => 'Bot',
            'device_manufacturer' => 'Bot',
            'flagUrl' => null // No flag for bot
        ];
    }

    $osPlatform = "Unknown OS";
    $browser = "Unknown Browser";
    $browserEngine = "Unknown Engine";
    $deviceType = "Unknown Device";
    $deviceManufacturer = "Unknown Manufacturer";
    $deviceModel = "Unknown Model";
    
// Extract device manufacturer
if (preg_match('/(Apple|Samsung|Xiaomi|Huawei|Oppo|OnePlus|LG|Nokia|Sony|Asus|Motorola|Realme|Vivo|Dell|HP|Lenovo|Acer|Microsoft|Razer|MSI|Alienware|Google|Toshiba|Panasonic|Fujitsu|Sharp|HTC|BlackBerry|Meizu|ZTE|Alcatel|Honor|Sharp|Kyocera|Fairphone|Wiko|Lava|Micromax|Infinix|Tecno|Itel|BLU|Gionee|Cubot|Ulefone|Doogee|HomTom|LeEco|Lumia|Rikomagic|Nextbit)/i', $userAgent, $matches)) {
    $deviceManufacturer = $matches[1];
} elseif (preg_match('/\((.*?)\)/', $userAgent, $matches)) {  // Capture the part inside parentheses (typically for unknown manufacturers)
    $deviceManufacturer = $matches[1];
}


    

    $osArray = [
        '/windows nt 10/i'     => 'Windows 10',
        '/windows nt 6.3/i'    => 'Windows 8.1',
        '/macintosh|mac os x/i'=> 'Mac OS X',
        '/linux/i'             => 'Linux',
        '/iphone/i'            => 'iPhone',
        '/android/i'           => 'Android',
        '/blackberry/i'        => 'BlackBerry',
        '/webos/i'             => 'Mobile'
    ];

    foreach ($osArray as $regex => $value) {
        if (preg_match($regex, $userAgent)) {
            $osPlatform = $value;
            break;
        }
    }

    $browserArray = [
        '/firefox/i'   => 'Firefox',
        '/chrome/i'    => 'Chrome',
        '/safari/i'    => 'Safari',
        '/edge/i'      => 'Edge',
        '/msie/i'      => 'Internet Explorer',
        '/opera/i'     => 'Opera'
    ];

    foreach ($browserArray as $regex => $value) {
        if (preg_match($regex, $userAgent)) {
            $browser = $value;
            break;
        }
    }

    // Browser engine detection
    if (preg_match('/Trident/i', $userAgent)) {
        $browserEngine = 'Trident (Internet Explorer)';
    } elseif (preg_match('/Edg/i', $userAgent)) {
        $browserEngine = 'Blink (Edge)';
    } elseif (preg_match('/Gecko/i', $userAgent)) {
        $browserEngine = 'Gecko (Firefox)';
    } elseif (preg_match('/WebKit/i', $userAgent)) {
        $browserEngine = 'WebKit (Safari, Chrome)';
    }

    // Device type and model detection
    if (preg_match('/(iPhone|iPod|iPad)/i', $userAgent)) {
        $deviceType = 'Mobile';
        $deviceModel = 'Apple';
    } elseif (preg_match('/Android/i', $userAgent)) {
        $deviceType = 'Mobile';
        $deviceModel = 'Android Device';
    } elseif (preg_match('/Windows Phone/i', $userAgent)) {
        $deviceType = 'Mobile';
        $deviceModel = 'Windows Phone';
    } elseif (preg_match('/Macintosh/i', $userAgent)) {
        $deviceType = 'Desktop';
        $deviceModel = 'Mac';
    } elseif (preg_match('/Windows/i', $userAgent)) {
        $deviceType = 'Desktop';
        $deviceModel = 'Windows';
    }

    // Get country details and flag URL
    $countryDetails = getCountryDetails($countryCode);
    
   

    return [
        'os' => $osPlatform,
        'browser' => $browser,
        'browser_engine' => $browserEngine,
        'device_type' => $deviceType,
        'device_model' => $deviceModel,
        'device_manufacturer' => $deviceManufacturer,
        'flagUrl' => $countryDetails['flagUrl'] // Use the flag URL from country details
    ];
}

// Function to get country details (flag URL)
function getCountryDetails($countryCode) {
    global $countryCodes;
    
    // Ensure country code is lowercase
    $countryCode = strtolower(trim($countryCode));

    // Log the country code being passed
    error_log("Country Code: " . $countryCode);
    
    // Auto-detect the base path
    $basePath = __DIR__; // Current directory path
    
    // Check if the country code exists in the $countryCodes array
    // If found, log success and construct the flag URL
    error_log("Found country code: " . $countryCode);
    $flagUrl = sprintf("%s/flags/%s.png", $basePath, $countryCode); // Correct flag URL

    // Get the document root path
    $docRoot = $_SERVER['DOCUMENT_ROOT']; 

    // Remove the document root part from the full file path to make it relative
    $flagUrl = str_replace($docRoot, "", $flagUrl);

    // Log the final flag URL value (relative URL)
    error_log("Generated flag URL: " . $flagUrl);
    
    return ['flagUrl' => $flagUrl]; 
}






function getCountryName($countryCode) {
       // Fallback for $countryNames if not available
    global $countryNames;
    $countryNames = $countryNames ?? [
        'af' => 'Afghanistan',
        'us' => 'United States',
        'nl' => 'Netherlands',
        'uy' => 'Uruguay',
        'uz' => 'Uzbekistan'
    ];
    
    
    // Log the raw input before processing
    //error_log("Raw Country Code: '$countryCode'");

    // Ensure country code is lowercase and trimmed
    $countryCode = strtolower(trim($countryCode));

    // Log after cleaning the country code
   // error_log("Processed Country Code: '$countryCode'");

    // Check if the array is valid
    if (!is_array($countryNames)) {
       // error_log("Country Names is not an array!");
        return 'Unknown';
    }

    // Debug log for countryCodes
    //error_log("Country Codes Array: " . print_r($countryNames, true));

    // Check if country code exists explicitly
    if (!array_key_exists($countryCode, $countryNames)) {
     //   error_log("Country code '$countryCode' not found in the array.");
    }

    // Return the country name or 'Unknown' if not found
    return $countryNames[$countryCode] ?? 'Unknown';
}





function monitorSecurity($mysqli, $userId, $fingerprint, $ip) {
    $query = "SELECT COUNT(*) AS visit_count, ip FROM visitors WHERE fingerprint = ? AND user_id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("si", $fingerprint, $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    if ($row['visit_count'] > 10) {
        // Ensure the 'alerts' table exists
        $mysqli->query("CREATE TABLE IF NOT EXISTS alerts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            ip VARCHAR(255) NOT NULL,
            fingerprint VARCHAR(255) NOT NULL,
            alert_time DATETIME NOT NULL,
            message TEXT NOT NULL
        )");

        // Ensure 'fingerprint' and 'ip' columns are present
        $alterQuery = "SHOW COLUMNS FROM alerts LIKE 'fingerprint'";
        $checkColumn = $mysqli->query($alterQuery);

        if ($checkColumn->num_rows == 0) {
            $addColumnQuery = "ALTER TABLE alerts ADD COLUMN fingerprint VARCHAR(255) NULL";
            $mysqli->query($addColumnQuery);
        }

        $checkIpColumn = $mysqli->query("SHOW COLUMNS FROM alerts LIKE 'ip'");
        if ($checkIpColumn->num_rows == 0) {
            $addIpColumnQuery = "ALTER TABLE alerts ADD COLUMN ip VARCHAR(255) NULL";
            $mysqli->query($addIpColumnQuery);
        }

        // Insert the alert into the 'alerts' table
        $stmt = $mysqli->prepare("INSERT INTO alerts (user_id, ip, fingerprint, alert_time, message) VALUES (?, ?, ?, NOW(), ?)");
        $message = "Unusual traffic detected from fingerprint $fingerprint";
        $stmt->bind_param("isss", $userId, $ip, $fingerprint, $message);  // Use the passed $ip here
        $stmt->execute();
        $stmt->close();
    }
}






