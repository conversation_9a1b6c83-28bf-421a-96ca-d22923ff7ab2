<?php
session_start();
$user_id = $_SESSION['user_id'];



// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}


// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 



// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];



try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get subscription details for the logged-in user
    $stmt = $pdo->prepare("SELECT subscription_end_date FROM user_subscriptions WHERE user_id = :user_id LIMIT 1");
    $stmt->execute(['user_id' => $user_id]);
    $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($subscription) {
        $current_date = new DateTime();
        $end_date = new DateTime($subscription['subscription_end_date']);
        $interval = $current_date->diff($end_date);

        $response = [
            'subscription' => true,
            'subscription_end_date' => $subscription['subscription_end_date'],
            'days_remaining' => $interval->days
        ];
    } else {
        $response = [
            'subscription' => false
        ];
    }
    
    echo json_encode($response);

} catch (PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
