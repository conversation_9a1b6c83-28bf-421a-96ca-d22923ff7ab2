<?php
require('db.php');
require('functions.php');

// Set secure session cookie parameters
session_set_cookie_params([
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);
session_start();

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    header("Location: https://www.google.com");
    exit;
}

// Define your encryption key
$encryptionKey = 'RaccoonO365';

// Encryption function
function encryptMessage($message, $key) {
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));
    $encrypted = openssl_encrypt($message, 'aes-256-cbc', $key, 0, $iv);
    return $encrypted . '::' . bin2hex($iv);
}

// Function to create the chats table
function createChatsTable($db) {
    try {
        $db->exec("CREATE TABLE IF NOT EXISTS chats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            username VARCHAR(255) NOT NULL,
            support_agent_id VARCHAR(255) DEFAULT NULL,
            status ENUM('open', 'closed', 'pending') DEFAULT 'open',
            last_message TEXT NOT NULL,
            support_agentmessage TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            closed_at TIMESTAMP DEFAULT NULL
        )");
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    global $pdo;
    
    // Validate input
    $username = isset($_POST['chat_id']) ? htmlspecialchars($_POST['chat_id'], ENT_QUOTES, 'UTF-8') : '';
    $support_agentmessage = isset($_POST['message']) ? htmlspecialchars($_POST['message'], ENT_QUOTES, 'UTF-8') : '';
    
    if (empty($username) || empty($support_agentmessage)) {
        echo json_encode(['status' => 'error', 'message' => 'Invalid input.']);
        exit;
    }

    try {
        // Retrieve user_id from chats table using the username
        $stmt = $pdo->prepare("SELECT user_id FROM chats WHERE username = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$username]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            $user_id = $result['user_id'];
        } else {
            echo json_encode(['status' => 'error', 'message' => 'User not found.']);
            exit;
        }

        // Encrypt the message
        $encryptedMessage = encryptMessage($support_agentmessage, $encryptionKey);

        // Assign support agent ID
        $support_agent_id = "RaccoonO3652FA/MFA";
        $status = 'open';
        $is_read = true;

        // Insert the message into the chats table
        $stmt = $pdo->prepare("INSERT INTO chats (user_id, username, support_agent_id, support_agentmessage, status, is_read) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$user_id, $username, $support_agent_id, $encryptedMessage, $status, $is_read]);

        echo json_encode(['status' => 'success']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
