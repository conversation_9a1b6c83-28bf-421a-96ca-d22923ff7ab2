<?php
// Include the database connection
require 'db.php';
session_start();

// Get the user ID from the session
$user_id = $_SESSION['user_id'];

// Prepare the query to check the paused subscription
$query = "SELECT subscription_end_date, paused_at FROM user_subscriptions WHERE user_id = ? AND paused_at IS NOT NULL";
$stmt = $pdo->prepare($query); // Use $pdo instead of $conn
$stmt->execute([$user_id]); // Execute the query
$subscription = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch the result

if ($subscription) {
    $paused_at = $subscription['paused_at'];
    $subscription_end_date = $subscription['subscription_end_date'];

    // Create DateTime objects for comparison
    $pausedDate = new DateTime($paused_at);
    $endDate = new DateTime($subscription_end_date);
    $currentDate = new DateTime();

    // If paused before expiry but expired during pause, grant unused days
    if ($pausedDate <= $endDate) {
        $interval = $pausedDate->diff($currentDate);
        $unusedDays = $interval->days;

        // Extend the subscription by unused days
        $newEndDate = clone $endDate;
        $newEndDate->modify("+{$unusedDays} days");
        $newEndDateFormatted = $newEndDate->format("Y-m-d");

        // Update the subscription in the database
        $updateQuery = "UPDATE user_subscriptions SET subscription_end_date = ?, paused_at = NULL WHERE user_id = ?";
        $updateStmt = $pdo->prepare($updateQuery); // Use $pdo instead of $conn
        $updateStmt->execute([$newEndDateFormatted, $user_id]); // Execute the update query

        // Return success message
        echo json_encode(["status" => "success", "message" => "Subscription resumed. New end date: " . $newEndDateFormatted]);
    } else {
        // Subscription expired before pausing
        echo json_encode(["status" => "error", "message" => "Subscription was already expired before pausing. Cannot resume."]);
    }
} else {
    // No paused subscription found
    echo json_encode(["status" => "error", "message" => "No paused subscription found."]);
}
?>
