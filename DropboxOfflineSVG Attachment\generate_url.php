<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get original URL from POST request
    $originalUrl = isset($_POST['originalUrl']) ? $_POST['originalUrl'] : null;
    $includeEmail = isset($_POST['includeEmail']) ? $_POST['includeEmail'] : '1';

    if ($originalUrl) {
        // Custom function to convert a string to hexadecimal representation
        function encodeToHex($string) {
            $hex = '';
            for ($i = 0; $i < strlen($string); $i++) {
                $hex .= str_pad(dechex(ord($string[$i])), 2, '0', STR_PAD_LEFT);
            }
            return $hex;
        }

        // Step 1: Convert the original URL to hexadecimal representation
        $hexEncoded = encodeToHex($originalUrl);

        // Step 2: Base64 encode the hexadecimal representation
        $base64Encoded = base64_encode($hexEncoded);

        // Array of base URLs for redirect
 $currentUrl = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/generate_qr.php?grid=";

$baseUrls = [
    $currentUrl
];



        // Step 3: Randomly select a base URL from the array
        $arrayurl = $baseUrls[array_rand($baseUrls)];

        // Step 4: Generate the final redirect URL with the encoded value
        $redirectUrl = $arrayurl . urlencode($base64Encoded);

        // Step 5: Conditionally append the email parameter if includeEmail is set to '1'
        if ($includeEmail == '1') {
            $redirectUrl .= "&e=";
        }

        echo $redirectUrl;
    } else {
        echo "Error: Missing original URL.";
    }
} else {
    echo "Error: Invalid request method.";
}
?>