<?php

// Start session securely
session_start();

session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: ../logout.php");  // Redirects to Google
    exit;
}




// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create database connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die(json_encode(['success' => false, 'message' => 'Connection failed: ' . $conn->connect_error]));
}

// Ensure the user is authenticated

if (!isset($_SESSION['user_id'])) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized. Please log in.']));
}

$user_id = $_SESSION['user_id'];

// Handle GET request to fetch selected logo
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $sql_fetch = "SELECT backgroundselected_logo FROM user_profiles WHERE user_id = $user_id";
    $result = $conn->query($sql_fetch);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo json_encode(['success' => true, 'selected_logo' => $row['backgroundselected_logo']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'No logo found for the user.']);
    }
    $conn->close();
    exit;
}

// Handle POST request to save the selected logo
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $logo = $conn->real_escape_string($_POST['logo']);

    // Check if backgroundselected_logo column exists
    $checkColumnQuery = "SHOW COLUMNS FROM user_profiles LIKE 'backgroundselected_logo'";
    $result = $conn->query($checkColumnQuery);

    if ($result->num_rows == 0) {
        $addColumnQuery = "ALTER TABLE user_profiles ADD COLUMN backgroundselected_logo VARCHAR(255) DEFAULT NULL";
        if (!$conn->query($addColumnQuery)) {
            die(json_encode(['success' => false, 'message' => 'Failed to add column backgroundselected_logo: ' . $conn->error]));
        }
    }

    // Update the user's preferred logo
    $sql_update = "UPDATE user_profiles SET backgroundselected_logo = '$logo' WHERE user_id = $user_id";
    if ($conn->query($sql_update) === TRUE) {
        echo json_encode(['success' => true, 'message' => 'Logo updated successfully!']);
    } else {
       
       
    }
    $conn->close();
    exit;
}
?>