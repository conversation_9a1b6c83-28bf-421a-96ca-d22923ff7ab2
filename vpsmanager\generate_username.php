<?php

// Set the Content-Type header to application/json
header('Content-Type: application/json');

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    // Establishing the PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION); // Enable exception mode for error handling
} catch (PDOException $e) {
    // If the connection fails, display an error
    echo "Connection failed: " . $e->getMessage();
    exit;
}

// Function to generate a random username
function generateRandomUsername($length = 8, $pdo) {
    $prefix = 'r365'; // The username prefix
    $suffix = 'vps';  // The username suffix
    
    // Exclude 'o' and '0' from the character pool
    $characters = 'abcdefghijklmnpqrstuvwxyzABCDEFGHIJKLMNPQRSTUVWXYZ123456789';
    
    // Calculate how many random characters to generate (excluding the prefix and suffix)
    $randomLength = $length - strlen($prefix) - strlen($suffix);
    
    // Ensure there's space for the suffix
    if ($randomLength < 0) {
        return 'Error: Length is too short for the prefix and suffix.';
    }
    
    do {
        // Generate the random username
        $username = $prefix;
        for ($i = 0; $i < $randomLength; $i++) {
            $username .= $characters[random_int(0, strlen($characters) - 1)];
        }
        $username .= $suffix; // Append the suffix

        // Check if the generated username exists in the database
        $query = "SELECT COUNT(*) FROM user_profiles WHERE username = :username";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
    } while ($count > 0); // If the username exists, generate a new one
    
     // Return the generated username as a JSON response
    return json_encode(['username' => $username]);
}

// Example usage: Generate a random username with 12 characters (including prefix and suffix)
echo generateRandomUsername(12, $pdo);
?>