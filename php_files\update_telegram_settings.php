<?php
// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);
session_regenerate_id(true);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 'error', 'message' => 'User not authenticated.']);
    exit;
}

// Database configuration
$servername = $config['host'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    echo json_encode(['status' => 'error', 'message' => 'Database connection failed: ' . $conn->connect_error]);
    exit;
}

// Ensure the unique key constraint exists (if needed)
$checkConstraintSQL = "
    SELECT COUNT(1)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = '{$dbname}'
    AND TABLE_NAME = 'telegram_settings'
    AND INDEX_NAME = 'unique_user_telegram';
";
$constraintExists = $conn->query($checkConstraintSQL)->fetch_row()[0];

if (!$constraintExists) {
    $createUniqueConstraintSQL = "
        ALTER TABLE telegram_settings 
        ADD UNIQUE KEY unique_user_telegram (user_id, telegram_id);
    ";
    if (!$conn->query($createUniqueConstraintSQL)) {
        echo json_encode(['status' => 'error', 'message' => 'Failed to create unique constraint: ' . $conn->error]);
        exit;
    }
}

// Get the user ID from the session
$user_id = $_SESSION['user_id'];

// Retrieve and sanitize POST data (if any)
$telegram_id = isset($_POST['telegram_id']) ? htmlspecialchars(trim($_POST['telegram_id']), ENT_QUOTES, 'UTF-8') : null;
$telegram_token = isset($_POST['telegram_token']) ? htmlspecialchars(trim($_POST['telegram_token']), ENT_QUOTES, 'UTF-8') : null;

if ($telegram_id && $telegram_token) {
    // If both Telegram ID and Token are provided, update the settings
    $sql = "INSERT INTO telegram_settings (user_id, telegram_id, telegram_token)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
            telegram_id = VALUES(telegram_id),
            telegram_token = VALUES(telegram_token)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        echo json_encode(['status' => 'error', 'message' => 'Statement preparation failed: ' . $conn->error]);
        exit;
    }

    $stmt->bind_param("iss", $user_id, $telegram_id, $telegram_token);

    if ($stmt->execute()) {
        echo json_encode(['status' => 'success', 'message' => 'Settings updated successfully!']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Error updating settings: ' . $stmt->error]);
    }
}

// Retrieve the user's Telegram settings from the database
$sql = "SELECT telegram_id, telegram_token FROM telegram_settings WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->store_result();

// Check if settings exist for the user
if ($stmt->num_rows > 0) {
    $stmt->bind_result($telegram_id, $telegram_token);
    $stmt->fetch();
    
    // Return the user's Telegram ID and Token as JSON response
    echo json_encode([
        'status' => 'success',
        'telegram_id' => $telegram_id,
        'telegram_token' => $telegram_token
    ]);
} else {
    echo json_encode(['status' => 'error', 'message' => 'Telegram settings not found.']);
}

// Close connection
$stmt->close();
$conn->close();
?>
