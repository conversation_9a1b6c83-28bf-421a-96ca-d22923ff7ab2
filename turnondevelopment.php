<?php
// Database connection

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database credentials
$host = $config['host'];        
$username = $config['username'];         
$password = $config['password'];             
$dbname = $config['dbname'];  

// Create a connection
$mysqli = new mysqli($host, $username, $password, $dbname);

// Check the connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

// Step 1: Get all domain requests
$query = "SELECT dr.domain_name, up.cloudflareemail, up.cloudflareapikey
          FROM dnsdomain_requests dr
          JOIN user_profiles up ON dr.user_id = up.user_id"; // No status filter

$result = $mysqli->query($query);

if (!$result) {
   
}

// Loop through all users and perform the Cloudflare operations
while ($row = $result->fetch_assoc()) {
    $domain = $row['domain_name'];
    $email = $row['cloudflareemail'];
    $tkn = $row['cloudflareapikey'];

    if (empty($email) || empty($tkn)) {
        continue; // Skip if email or token is missing
    }

    // Step 2: If no domain is specified, fetch all domains from the user's Cloudflare account
    $domains_to_process = [];

    if (empty($domain)) {
        // Fetch all zones (domains) for the user's account
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.CloudFlare.com/client/v4/zones?page=1&per_page=50");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "X-Auth-Email: $email",
            "X-Auth-Key: $tkn",
            "Content-Type: application/json",
        ]);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
           
            curl_close($ch);
            continue; // Skip this user if an error occurs
        }

        curl_close($ch);
        $data = json_decode($response, true);
        if (isset($data['result'])) {
            foreach ($data['result'] as $zone) {
                $domains_to_process[] = $zone['name'];
            }
        } else {
            
            continue; // Skip this user if no domains are found
        }
    } else {
        // If a domain is provided, add it to the array
        $domains_to_process[] = $domain;
    }

    // Step 3: Perform operations for each domain
    foreach ($domains_to_process as $domain_to_process) {
        // Step 3a: Fetch Zone ID
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.CloudFlare.com/client/v4/zones?name=$domain_to_process&status=active&page=1&per_page=20&order=status&direction=desc&match=all");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "X-Auth-Email: $email",
            "X-Auth-Key: $tkn",
            "Content-Type: application/json",
        ]);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
           
            curl_close($ch);
            continue; // Skip if an error occurs
        }

        curl_close($ch);
        $data = json_decode($response, true);
        $zone = $data['result'][0]['id'] ?? null;

        if (!$zone) {
            
            continue; // Skip if zone is not found and move to the next domain/user
        }

        // Step 3b: Purge Cache
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.CloudFlare.com/client/v4/zones/$zone/purge_cache");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "X-Auth-Email: $email",
            "X-Auth-Key: $tkn",
            "Content-Type: application/json",
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(["purge_everything" => true]));

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            echo "Curl error while purging cache for domain $domain_to_process: " . curl_error($ch) . "\n";
            curl_close($ch);
            continue; // Skip if purging cache fails
        }

        curl_close($ch);

        // Step 3c: Enable Development Mode
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.CloudFlare.com/client/v4/zones/$zone/settings/development_mode");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "X-Auth-Email: $email",
            "X-Auth-Key: $tkn",
            "Content-Type: application/json",
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(["value" => "on"]));

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
           
            curl_close($ch);
            continue; // Skip if enabling development mode fails
        }

        curl_close($ch);

       
    }
}

// Close the database connection
$mysqli->close();

unset($email, $tkn, $domain, $zone);
?>
