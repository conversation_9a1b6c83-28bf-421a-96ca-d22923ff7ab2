<?php
// Database connection settings
// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get current page from GET parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;  // Set a reasonable number of results per page
$offset = ($page - 1) * $limit;

// Query for the history with limit and offset for pagination
$query = $pdo->prepare("SELECT * FROM newsletter_history ORDER BY id DESC LIMIT :limit OFFSET :offset");
$query->bindParam(':limit', $limit, PDO::PARAM_INT);
$query->bindParam(':offset', $offset, PDO::PARAM_INT);
$query->execute();

// Fetch all results
$history = $query->fetchAll(PDO::FETCH_ASSOC);

// Query to get the total number of records for pagination
$totalQuery = $pdo->prepare("SELECT COUNT(*) FROM newsletter_history");
$totalQuery->execute();
$totalRecords = $totalQuery->fetchColumn();
$totalPages = ceil($totalRecords / $limit);  // Calculate total pages

// Return the email history data
if ($history) {
    foreach ($history as $entry) {
        echo "<div class='history-entry'>";
        echo "<h4>{$entry['subject']}</h4>";
        echo "<p>{$entry['content']}</p>";
        echo "<p>Status: {$entry['status']}</p>";

        if ($entry['status'] === 'failed') {
            // Correct class 'resendBtn' added to button
            echo "<button class='resendBtn' data-id='{$entry['id']}'>Resend</button>";
        }

        echo "</div>";
    }

    // Add totalPages information in the response (for pagination)
    echo "<script>
        $('#history-container').data('total-pages', {$totalPages});
    </script>";
} else {
    echo "<p>No email history found.</p>";
}
?>
