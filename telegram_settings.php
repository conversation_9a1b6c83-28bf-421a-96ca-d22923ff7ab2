<?php require 'assets/header.php'; ?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h5>Update Telegram Settings</h5>
    </div>
    <div>
        <form class="row g-3" id="telegram-settings-form" method="post"
              enctype="multipart/form-data">
            <div class="col-md-6">
                <label for="telegram_id" class="form-label">Telegram ID</label>
                <input type="digit" class="form-control" name="telegram_id" required id="telegram_id">
            </div>
            <div class="col-md-6">
                <label for="telegram_token" class="form-label">Telegram Token</label>
                <input type="text" class="form-control" name="telegram_token" required id="telegram_token">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary"> Update Settings </button>
            </div>
        </form>
    </div>
    
     <div id="telegram-settings">
        <h2>Your Telegram Settings</h2>
        <p><strong>Telegram ID:</strong> <span id="telegram-id">Loading...</span></p>
        <p><strong>Telegram Token:</strong> <span id="telegram-token">Loading...</span></p>
    </div>
    
</main>
</div>
</div>

<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
 $(document).ready(function() {
    // Handle the form submission to update Telegram settings
    $('#telegram-settings-form').on('submit', function(event) {
        event.preventDefault(); // Prevent the default form submission

        $.ajax({
            url: 'php_files/update_telegram_settings.php',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                try {
                    // Clean the response if it contains multiple concatenated JSON objects
                    if (response.indexOf('}{') > -1) {
                        response = response.replace('}{', ',');
                    }

                    // Attempt to parse the cleaned response as JSON
                    const jsonResponse = JSON.parse(response);

                    // Handle success response
                    if (jsonResponse.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: jsonResponse.message,
                            confirmButtonText: 'OK'
                        });

                        // Fetch the updated Telegram settings after successful update
                        
                         // Clear the form input fields after success
                        $('#telegram-settings-form')[0].reset();


                        fetchTelegramSettings();
                    } else {
                        // Handle failure response
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: jsonResponse.message,
                            confirmButtonText: 'OK'
                        });
                    }
                } catch (e) {
                    // Handle case where response is not valid JSON
                    Swal.fire({
                        icon: 'warning',
                        title: 'Unexpected Response',
                        text: 'The response format is invalid: ' + response,
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                // Handle AJAX request errors
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred. Please try again.',
                    confirmButtonText: 'OK'
                });
                console.log('Error details:', status, error);
            }
        });
    });

    // Function to fetch the Telegram settings
    function fetchTelegramSettings() {
        $.ajax({
            url: 'php_files/update_telegram_settings.php', // The PHP file to fetch data
            type: 'GET',
            success: function(response) {
                try {
                    // Clean the response if it contains multiple concatenated JSON objects
                    if (response.indexOf('}{') > -1) {
                        response = response.replace('}{', ',');
                    }

                    // Attempt to parse the cleaned response as JSON
                    const jsonResponse = JSON.parse(response);

                    if (jsonResponse.status === 'success') {
                        // Display the Telegram settings if found
                        $('#telegram-id').text(jsonResponse.telegram_id);
                        $('#telegram-token').text(jsonResponse.telegram_token);
                    } else {
                        // Handle case where settings are not found
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: jsonResponse.message,
                            confirmButtonText: 'OK'
                        });
                    }
                } catch (e) {
                    // Handle case where the response is not valid JSON
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Unexpected response format: ' + response,
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                // Handle AJAX error
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred while fetching the settings. Please try again later.',
                    confirmButtonText: 'OK'
                });
                console.log('Error details:', status, error);
            }
        });
    }

    // Fetch the Telegram settings when the page loads
    fetchTelegramSettings();
});

</script>

</body>
</html>