<?php require '../assets/admin_header.php';
require('../php_files/db.php');
require('../php_files/functions.php');

    $unread_sql = "
        SELECT chats.*, user_profiles.email
        FROM chats
        JOIN user_profiles ON chats.user_id = user_profiles.user_id
        WHERE chats.status = 'unread'
        ORDER BY chats.created_at DESC
    ";
    $stmt = $pdo->prepare($unread_sql);
    $stmt->execute();
     $unread_chats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $read_sql = "
        SELECT chats.*, user_profiles.email
        FROM chats
        JOIN user_profiles ON chats.user_id = user_profiles.user_id
        WHERE chats.status = 'read'
        ORDER BY chats.created_at DESC
    ";
    $stmt = $pdo->prepare($read_sql);
    $stmt->execute();
    $read_chats = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">

    <h2> All Messages </h2>
    <div class="table-responsive small">
        <h5> Unread chats </h5>
        <?php
        if ( $unread_chats ) {
        echo '
            <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">Email</th>
                <th scope="col">Last message</th>
                <th scope="col"></th>
            </tr>
            </thead>
            <tbody>
        ';

                foreach ( $unread_chats as $unread_chat ) {
                    $badge = '<span class="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle">';
                    echo '
                        <tr>
                            <td> '. $unread_chat['id'] .' </td>
                            <td> '. $unread_chat['email'] .' </td>
                            <td> '. $unread_chat['last_message'] .' </td>
                            <td class="position-relative d-inline-block"> <a href="message.php?chat_id='.
                        $unread_chat['id'] .'" class="btn btn-success btn-sm"> View Message </a> 
                            '. $badge .'
                            </td>
                        </tr>
                    ';
                }
            } else {
                echo '<p class="my-3">You have read all the chats.</p>';
            }

            ?>
            </tbody>
        </table>
    </div>
    <div class="table-responsive small">
        <h5> Read chats </h5>
        <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">Email</th>
                <th scope="col">Last message</th>
                <th scope="col"></th>
            </tr>
            </thead>
            <tbody>
            <?php
            foreach ( $read_chats as $read_chat ) {
                $badge = '<span class="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle">';
                echo '
                        <tr>
                            <td> '. $read_chat['id'] .' </td>
                            <td> '. $read_chat['email'] .' </td>
                            <td> '. $read_chat['last_message'] .' </td>
                            <td class="position-relative d-inline-block"> <a href="message.php?chat_id='.
                    $read_chat['id'] .'" class="btn btn-success btn-sm"> View Message </a> 
                            </td>
                        </tr>
                    ';
            }
            ?>
            </tbody>
        </table>
    </div>
</main>
</div>
</div>
<script src="<?= BASE_URL ?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script></body>
</html>