<?php
header('Content-Type: application/json');

// Include the configuration file
$config = include 'extractedconfig.php';

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION); // Exception mode for better error handling
} catch (PDOException $e) {
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Get and sanitize the username from the query parameter
$username = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_STRING);

// Check if the username exists in user_profiles and fetch the user_id
$stmt = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = ?");
$stmt->execute([$username]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if ($user) {
    // If the username exists, fetch the generated URL and path using the user_id
    $user_id = $user['user_id'];
    $stmt = $pdo->prepare("SELECT generated_url, path FROM usergeneratedpath WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo json_encode($result);
    } else {
        echo json_encode(['error' => 'No generated URL found for this user']);
    }
} else {
    echo json_encode(['error' => 'No user found with that username']);
}
?>