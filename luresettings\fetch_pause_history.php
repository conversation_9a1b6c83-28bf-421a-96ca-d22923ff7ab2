<?php

// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Example: Fetch user_id from a session, or predefined variable
// For instance, if user_id is stored in a session
session_start();  // Start session
$user_id = $_SESSION['user_id'] ?? null;

if (!$user_id) {
    header('Content-Type: application/json', true, 400);
    echo json_encode(['error' => 'User ID is missing.']);
    exit;
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Ensure the table exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS lure_pauses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            date_paused DATETIME NOT NULL,
            duration INT NOT NULL,
            unpause_time DATETIME NOT NULL
        );
    ");

    // Fetch user-specific pause history
    $stmt = $pdo->prepare("SELECT date_paused, duration, unpause_time FROM lure_pauses WHERE user_id = :user_id ORDER BY date_paused DESC");
    $stmt->execute([':user_id' => $user_id]);
    $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($history);
} catch (PDOException $e) {
    // Return error response
    header('Content-Type: application/json', true, 500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    // Return validation error if user_id is not provided
    header('Content-Type: application/json', true, 400);
    echo json_encode(['error' => $e->getMessage()]);
}

?>
