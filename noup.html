<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f4f4f4;
        }
        .form-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 400px;
        }
        .form-container h2 {
            margin-bottom: 20px;
        }
        .form-container label {
            display: block;
            margin-bottom: 8px;
        }
        .form-container input[type="text"],
        .form-container input[type="email"],
        .form-container input[type="password"],
        .form-container input[type="url"],
        .form-container textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-container input[type="file"] {
            margin-bottom: 12px;
        }
        .form-container button {
            background-color: #007bff;
            color: #fff;
            border: none;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .form-container button:hover {
            background-color: #0056b3;
        }
        .form-container .error {
            color: red;
            font-size: 14px;
            margin-top: -10px;
            margin-bottom: 10px;
        }
    </style>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="form-container">
        <h2>Sign Up</h2>
        <form id="signupForm" action="php_files/signup.php" method="post" enctype="multipart/form-data">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
            <span class="error" id="usernameError"></span>

            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
            <span class="error" id="emailError"></span>

            <label for="user_profiles_link">Profile Link:</label>
            <input type="url" id="user_profiles_link" name="user_profiles_link">

            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
            <span class="error" id="passwordError"></span>

            <label for="profile_picture">Profile Picture:</label>
            <input type="file" id="profile_picture" name="profile_picture" accept="image/*">

            <label for="bio">Bio:</label>
            <textarea id="bio" name="bio" rows="4"></textarea>

            <button type="submit">Sign Up</button>
        </form>
    </div>
    <script>
        $(document).ready(function() {
            $('#signupForm').on('submit', function(event) {
                event.preventDefault(); // Prevent form from submitting

                // Clear previous errors
                $('.error').text('');

                // Collect form data
                var formData = new FormData(this);

                // Validate form fields
                var isValid = true;
                var username = $('#username').val().trim();
                var email = $('#email').val().trim();
                var password = $('#password').val().trim();

                if (username.length < 3) {
                    $('#usernameError').text('Username must be at least 3 characters long.');
                    isValid = false;
                }

                if (!validateEmail(email)) {
                    $('#emailError').text('Please enter a valid email address.');
                    isValid = false;
                }

                if (password.length < 6) {
                    $('#passwordError').text('Password must be at least 6 characters long.');
                    isValid = false;
                }

                if (isValid) {
                    // AJAX request to check if username or email already exists
                    $.ajax({
                        url: 'php_files/check_user.php',
                        type: 'POST',
                        data: {
                            username: username,
                            email: email
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.status === 'error') {
                                if (response.field === 'username') {
                                    $('#usernameError').text(response.message);
                                } else if (response.field === 'email') {
                                    $('#emailError').text(response.message);
                                }
                            } else if (response.status === 'success') {
                                // Submit the form if everything is valid and username/email are not taken
                                $.ajax({
                                    url: 'php_files/signup.php',
                                    type: 'POST',
                                    data: formData,
                                    contentType: false,
                                    processData: false,
                                    dataType: 'json',
                                    success: function(response) {
                                        alert(response.message);
                                        if (response.status === 'success') {
                                            $('#signupForm')[0].reset();
                                        }
                                    }
                                });
                            }
                        }
                    });
                }
            });

            function validateEmail(email) {
                var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return re.test(email);
            }
        });
    </script>
</body>
</html>
