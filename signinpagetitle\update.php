<?php
// Set session cookie parameters first
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict'   // Mitigate CSRF attacks
]);

// Start session to manage user authentication
session_start();

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    header("Location: logout.php");  // Redirects to Google login or similar page
    exit;
}

// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a new MySQLi connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check database connection
if ($conn->connect_error) {
    die(json_encode(["success" => false, "message" => "Database connection failed: " . $conn->connect_error]));
}

// Create table if not exists
$sql_create_table = "
CREATE TABLE IF NOT EXISTS signinpagetitle (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    text TEXT NOT NULL
)";

// Execute the table creation query
if ($conn->query($sql_create_table) === FALSE) {
    die(json_encode(["success" => false, "message" => "Error creating table: " . $conn->error]));
}

// Ensure default value exists for user if not already present
$sql_check_default = "SELECT * FROM signinpagetitle WHERE user_id = " . $_SESSION['user_id'];
$result = $conn->query($sql_check_default);

if ($result->num_rows == 0) {
    $default_text = "";
    $default_text_escaped = $conn->real_escape_string($default_text);  // Escape special characters properly
    $sql_insert_default = "INSERT INTO signinpagetitle (user_id, text) VALUES ('" . $_SESSION['user_id'] . "', '$default_text_escaped')";
    
    if ($conn->query($sql_insert_default) === FALSE) {
        die(json_encode(["success" => false, "message" => "Error inserting default text: " . $conn->error]));
    }
}

// Handle AJAX request to update text
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    if (isset($_SESSION['user_id'])) {
        $text = $conn->real_escape_string($_POST["text"]);  // Escape user input to prevent SQL injection
        $userId = $_SESSION['user_id'];
        $sql_update = "UPDATE signinpagetitle SET text = '$text' WHERE user_id = $userId";
        
        if ($conn->query($sql_update) === TRUE) {
            echo json_encode(["success" => true, "message" => "Updated successfully!"]);
        } else {
            echo json_encode(["success" => false, "message" => "Error updating text: " . $conn->error]);
        }
    } else {
        echo json_encode(["success" => false, "message" => ""]);
    }
    exit;
}
?>
