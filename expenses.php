<?php
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');
$userId = $_SESSION['user_id'];

function getTotalWalletFunding($userId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT SUM(amount) as total_funding FROM wallet_transactions WHERE user_id = :user_id AND status = 'completed' AND type = 'credit'");
    $stmt->execute(['user_id' => $userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['total_funding'] ?? 0;
}

function getTotalExpenses($userId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT SUM(balance) as total_expenses FROM wallet WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['total_expenses'] ?? 0;
}


$totalFunding = getTotalWalletFunding($userId);
$totalExpenses = getTotalExpenses($userId);



?>
<?php require('assets/header.php') ?>


<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5"> Wallet Summary </h1>
    </div>
    <div class="row">
        <div class="card border-dark mb-3 col-md-4">
            <div class="card-header">Total Funded:</div>
            <div class="card-body">
                <h5 class="card-title"> $<?= number_format($totalFunding, 2) ?> </h5>
            </div>
        </div>

        <div class="card border-dark mb-3 col-md-4">
            <div class="card-header"> Total Expenses: </div>
            <div class="card-body">
                <h5 class="card-title"> $<?= number_format($totalExpenses, 2) ?> </h5>
            </div>
        </div>
    </div>
</main>
</div>
</div>
<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous">
    
</script><script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

</script>
</body>
</html>
