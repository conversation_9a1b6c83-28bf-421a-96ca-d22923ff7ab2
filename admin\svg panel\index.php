<?php



// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Use the values from the config array
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a new PDO instance
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if the table exists, create if not
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'svgurlbase'");
    if ($tableCheck->rowCount() == 0) {
        $createTableQuery = "
        CREATE TABLE svgurlbase (
            id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            url VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($createTableQuery);
    }

    $duplicateAlert = false; // Flag for duplicate alert

    // Insert URL into the table when the form is submitted
    if (isset($_POST['submit']) && !empty($_POST['url'])) {
        $url = $_POST['url'];

        // Check if the URL already exists in the database
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM svgurlbase WHERE url = :url");
        $stmt->bindParam(':url', $url);
        $stmt->execute();
        $urlExists = $stmt->fetchColumn();

        if ($urlExists) {
            // Set flag to true if duplicate URL is found
            $duplicateAlert = true;
        } else {
            // Insert the URL if it does not exist
            $stmt = $pdo->prepare("INSERT INTO svgurlbase (url) VALUES (:url)");
            $stmt->bindParam(':url', $url);
            $stmt->execute();
        }
    }

    // Delete URL if delete request is made
    if (isset($_GET['delete']) && !empty($_GET['delete'])) {
        $id = $_GET['delete'];
        $stmt = $pdo->prepare("DELETE FROM svgurlbase WHERE id = :id");
        $stmt->bindParam(':id', $id);
        $stmt->execute();
    }

    // Update URL if update request is made
    if (isset($_POST['update']) && !empty($_POST['url']) && isset($_POST['id'])) {
        $id = $_POST['id'];
        $url = $_POST['url'];
        $stmt = $pdo->prepare("UPDATE svgurlbase SET url = :url WHERE id = :id");
        $stmt->bindParam(':url', $url);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        // Redirect to avoid form resubmission
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }

    // Fetch all URLs from the database
    $stmt = $pdo->query("SELECT * FROM svgurlbase ORDER BY created_at DESC");
    $urls = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store SVG URL</title>
        <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        /* styles.css */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f4f8;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .container {
            max-width: 700px;
            margin: 50px auto;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #4CAF50;
            font-size: 2em;
            margin-bottom: 30px;
        }

        label {
            font-size: 16px;
            margin-bottom: 8px;
            display: block;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-sizing: border-box;
        }

        button {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }

        .url-list {
            margin-top: 40px;
        }

        .url-item {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .url-item span {
            flex: 1;
            font-size: 16px;
        }

        .url-item a {
            color: #007bff;
            text-decoration: none;
            margin-right: 10px;
        }

        .url-item a:hover {
            text-decoration: underline;
        }

        .url-item button {
            background-color: #e74c3c;
            border-radius: 6px;
            padding: 8px 12px;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        .url-item button:hover {
            background-color: #c0392b;
        }

        .edit-form {
            display: block;
            margin-top: 30px;
        }

        .edit-form input[type="text"] {
            width: 80%;
            margin-right: 10px;
        }

        .edit-form button {
            width: 18%;
            padding: 12px;
            font-size: 16px;
            background-color: #3498db;
            border-radius: 6px;
        }

        .edit-form button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Store SVG URL</h1>
        <form action="" method="post">
            <label for="url">Enter URL:</label>
            <input type="text" id="url" name="url" placeholder="Enter your SVG URL" required>
            <button type="submit" name="submit">Store URL</button>
        </form>

        <div class="url-list">
            <h2>Stored URLs</h2>
            <?php foreach ($urls as $url): ?>
            <div class="url-item">
                <span><?php echo htmlspecialchars($url['url']); ?></span>
                <div>
                    <a href="?edit=<?php echo $url['id']; ?>">Edit</a>
                    <a href="?delete=<?php echo $url['id']; ?>" id="delete-<?php echo $url['id']; ?>">
                        <button>Delete</button>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>

            <?php
            if (isset($_GET['edit'])):
                $id = $_GET['edit'];
                $stmt = $pdo->prepare("SELECT * FROM svgurlbase WHERE id = :id");
                $stmt->bindParam(':id', $id);
                $stmt->execute();
                $urlToEdit = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($urlToEdit):
            ?>
            <h2>Edit URL</h2>
            <form action="" method="post" class="edit-form">
                <input type="hidden" name="id" value="<?php echo $urlToEdit['id']; ?>">
                <input type="text" name="url" value="<?php echo htmlspecialchars($urlToEdit['url']); ?>" required>
                <button type="submit" name="update">Update URL</button>
            </form>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>
    <script>
    // Handle the delete action with SweetAlert2 confirmation
    const deleteButtons = document.querySelectorAll('.url-item a[href*="delete"]');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function (e) {
            e.preventDefault(); // Prevent the link from being followed immediately
            
            const urlId = this.getAttribute('href').split('=')[1]; // Get the ID from the URL

            // Show SweetAlert confirmation
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // If confirmed, redirect to the delete URL
                    window.location.href = "?delete=" + urlId;
                }
            });
        });
    });

    // Handle form submission for updates
    <?php if (isset($_POST['update'])): ?>
        Swal.fire({
            title: 'Success!',
            text: 'URL has been updated successfully.',
            icon: 'success',
            confirmButtonText: 'Okay'
        });
    <?php endif; ?>
    
    
    
</script>

<script>
    // Set the PHP flag for duplicate alert to a JavaScript variable
    var showDuplicateAlert = <?php echo json_encode($duplicateAlert); ?>;

    // If the flag is true, show the SweetAlert
    if (showDuplicateAlert) {
        Swal.fire({
            title: 'Duplicate URL!',
            text: 'This URL already exists in the database.',
            icon: 'error',
            confirmButtonText: 'Okay'
        });
    }
</script>



</body>
</html>