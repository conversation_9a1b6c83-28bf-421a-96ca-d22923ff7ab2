<?php
// post_handler.php

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Check the content type
    $contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';
    
    // Initialize a flag to determine if notification should be triggered
    $shouldNotify = false;
    
    if (strpos($contentType, 'application/json') !== false) {
        // JSON POST detected
        $shouldNotify = true;
    } elseif (strpos($contentType, 'text/plain') !== false) {
        // Plain text POST detected
        $shouldNotify = true;
    } else {
        // Unsupported content type
        http_response_code(415);
        echo "Unsupported";
        exit;
    }
    
    if ($shouldNotify) {
        // Trigger the notification by making a GET request to another PHP script
        $notificationUrl = '/notify.php';
        $response = file_get_contents($notificationUrl);
        
        // Check the response from the notification script
        if ($response === false) {
            // Handle error in the GET request
            http_response_code(500);
            echo "Telegram Notification failed";
        } else {
            // Successful notification
            http_response_code(200);
            echo "Telegram Notification sent";
        }
    }

} else {
    // If the request method is not POST, return a 405 Method Not Allowed response
    http_response_code(405);
    echo "Not Allowed";
}
