<?php
    require_once 'php_files/db.php';
    require_once 'php_files/functions.php';
 if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


    // Function to verify the verification code and update the email
function verifyVerificationCode($userId, $enteredCode, $email) {
    global $pdo;

    // Check if the verification code is stored in the session
    if (!isset($_SESSION['verification_code'])) {
        echo "<script>alert('Verification code not found. Please request a new code.');</script>";
        return false;
    }

    // Compare the entered code with the stored session code
    $storedCode = $_SESSION['verification_code'];

    if ($enteredCode == $storedCode) {
        // If the codes match, proceed to update the email in the user_profiles table
        try {
            // Update the email and set email_log_unlocked to true (or 1) in the database
            $updateEmail = $pdo->prepare("
                UPDATE user_profiles 
                SET result_mail = :email, email_log_unlocked = 1 
                WHERE user_id = :userId
            ");
            
            // Execute the update query with the email and user ID
            $updateEmail->execute([
                'email' => $email,
                'userId' => $userId
            ]);
        
            // Remove the verification code from the session after successful verification
            unset($_SESSION['verification_code']);

                // Assuming $pdo is your PDO connection and $userId is the new user's ID
            $stmt = $pdo->query("SELECT id, state FROM antibottoggle");
            $defaultSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $insertStmt = $pdo->prepare("
                INSERT INTO user_antibot_settings (user_id, antibot_id, state)
                VALUES (:user_id, :antibot_id, :state)
            ");

            foreach ($defaultSettings as $setting) {
                $insertStmt->execute([
                    ':user_id' => $userId,
                    ':antibot_id' => $setting['id'],
                    ':state' => $setting['state']
                ]);
            }
        
            // Notify the user
            echo "<script>alert('Email verified and updated successfully!');</script>";
            return true;
        
        } catch (Exception $e) {
            // Handle any errors that occur during the update
            echo "Error updating email: " . $e->getMessage();
            return false;
        }        
    } else {
        // If the codes do not match, notify the user
        echo "<script>alert('Invalid verification code. Please try again.');</script>";
        return false;
    }
}




    if (isset($_GET['token'])) {
        $token = $_GET['token'];

        $password = generateRandomPassword();
        $hashedPassword = password_hash($password , PASSWORD_BCRYPT);
   
       
        $stmt = $pdo->prepare("
           SELECT user_id FROM email_verifications WHERE token = :token
       ");
        $stmt->execute(['token' => $token]);
   
        if ($stmt->rowCount() === 1) {
            $user = $stmt->fetch();
            $userId = $user['user_id'];
   
            // Update the user's password and activate the account
            $updateStmt = $pdo->prepare("
               UPDATE user_profiles 
               SET password = :password, status = 'active' 
               WHERE user_id = :user_id
           ");
            $updateStmt->execute([
                'password' => $hashedPassword,
                'user_id' => $userId
            ]);
   
            // Insert wallet for the user with balance zero
            $walletStmt = $pdo->prepare("
               INSERT INTO wallet (user_id, balance)
               VALUES (:user_id, :balance)
           ");
   
            $walletStmt->execute([
                'user_id' => $userId,
                'balance' => 0
            ]);
   
   
            // Delete the token after it's used
            $deleteStmt = $pdo->prepare("
               DELETE FROM email_verifications WHERE token = :token
           ");
            $deleteStmt->execute(['token' => $token]);
   
            // Fetch user details for email
            $userStmt = $pdo->prepare("
               SELECT username, email FROM user_profiles WHERE user_id = :user_id
           ");
            $userStmt->execute(['user_id' => $userId]);
            $userData = $userStmt->fetch();
            $username = $userData['username'];
            $email = $userData['email'];
   
   
$browserIcon = "👤";  // Globe icon for browser
$osIcon = "🔑";  // Laptop icon for OS


// Get the domain name
$domain = $_SERVER['HTTP_HOST'];  // This will get the domain like www.example.com

// Construct the full URL by appending /index.html
$fullUrl = $protocol . '' . $domain . '/signin.htm';



            // Send login details to the user
            $subject = "You are all set - your account is ready! Let's get started!";
            $message = "
    <head>
        <title>$subject</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
            .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }
            .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }
            .email-header img { width: 30px; }
            .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }
            h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }
            .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }
            .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }
            .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }
        </style>
    </head>
    <body>
        <div class='email-container'>
            <div class='email-header'>
                <img src='https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png' alt='Log in'>
            </div>
            <div class='email-content'>
                <h1>Sign-in Alert</h1>
                
                <div class='details'>
                <p>Your registration was successful, and your account is now active. We are thrilled to have you with us and look forward to supporting your journey!.</p>
                    <p><strong>$browserIcon Username:</strong><br>$username</p>
                    <p><strong>$osIcon Password:</strong><br>$password</p>
                     <p><strong>$osIcon Admin Control:</strong><br><a href='$fullUrl'>$fullUrl</a></p>
                     
                    <p> If this was you, no further action is needed. If you don't recognize this activity, please secure your account immediately.</p>
                </div>
            </div>
        </div>
    </body>
</html>
               ";
   
            sendMail($email, $subject, $message , 'system_mail');
   
           
   
           echo '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Successful!</title>
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: "Poppins", sans-serif;
      background-color: #edf2f7;
      padding: 0;
      margin: 0;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .container {
      width: 100%;
      max-width: 600px;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.05);
      padding: 20px;
      text-align: center;
    }
    .header__title h1 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 20px;
    }
    .header__text {
      font-size: 1.25rem;
      margin-bottom: 20px;
    }
    .header__button {
      background-color: #1a202c;
      color: #ffffff;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    .header__button:hover {
      background-color: #2d3748;
    }
  </style>
</head>
<body>
  <main class="container">
    <div class="content">
      <div class="header">
        <div class="header__title">
          <h1 class="header__h1">Successful!</h1>
        </div>
        <p class="header__text">Your account has been successfully verified! <br>  Your login details have been sent to your registered email. Please check your inbox (and spam folder, just in case) for the details. <br> Welcome aboard!</p>
        <a class="header__button" href="javascript:history.back()">Back</a>
      </div>
    </div>
  </main>
  <script src="https://cdn.jsdelivr.net/npm/tsparticles-preset-fountain@2/tsparticles.preset.fountain.bundle.min.js"></script>
</body>
</html>';

global $pdo;

    }

 } else if (isset( $_SESSION['verification_code']) && isset($_GET['verification_code']) ) {
    $enteredCode = $_GET['verification_code'];
    $email = $_SESSION['result_mail']; 
    $userId = $_SESSION['user_id']; 

    // Call the function to verify the code and update the email
    if (verifyVerificationCode($userId, $enteredCode, $email)) {
        // Success: Redirect to a success page or display success message
        
      
       echo '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Successful!</title>
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: "Poppins", sans-serif;
      background-color: #edf2f7;
      padding: 0;
      margin: 0;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .container {
      width: 100%;
      max-width: 600px;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.05);
      padding: 20px;
      text-align: center;
    }
    .header__title h1 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 20px;
    }
    .header__text {
      font-size: 1.25rem;
      margin-bottom: 20px;
    }
    .header__button {
      background-color: #1a202c;
      color: #ffffff;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    .header__button:hover {
      background-color: #2d3748;
    }
  </style>
</head>
<body>
  <main class="container">
    <div class="content">
      <div class="header">
        <div class="header__title">
          <h1 class="header__h1">Successful!</h1>
        </div>
        <p class="header__text">Email Verification update successful!</p>
        <a class="header__button" href="javascript:history.back()">Back</a>
      </div>
    </div>
  </main>
  <script src="https://cdn.jsdelivr.net/npm/tsparticles-preset-fountain@2/tsparticles.preset.fountain.bundle.min.js"></script>
</body>
</html>';

    } else {

   echo '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verification failed!</title>
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: "Poppins", sans-serif;
      background-color: #edf2f7;
      padding: 0;
      margin: 0;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .container {
      width: 100%;
      max-width: 600px;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.05);
      padding: 20px;
      text-align: center;
    }
    .header__title h1 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 20px;
    }
    .header__text {
      font-size: 1.25rem;
      margin-bottom: 20px;
    }
    .header__button {
      background-color: #1a202c;
      color: #ffffff;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    .header__button:hover {
      background-color: #2d3748;
    }
  </style>
</head>
<body>
  <main class="container">
    <div class="content">
      <div class="header">
        <div class="header__title">
          <h1 class="header__h1">Verification failed!</h1>
        </div>
        <p class="header__text">Verification failed!</p>
        <a class="header__button" href="javascript:history.back()">Back</a>
      </div>
    </div>
  </main>
  <script src="https://cdn.jsdelivr.net/npm/tsparticles-preset-fountain@2/tsparticles.preset.fountain.bundle.min.js"></script>
</body>
</html>';


    }
 } else {
    //  echo 'No token provided';
     //var_dump($_SESSION);
 }
