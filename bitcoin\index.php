<?php
require('../php_files/authorizer.php');
require('../php_files/db.php');
require('../php_files/functions.php');
require('../assets/header.php');

session_start();
$config = include 'sextractedconfig.php';

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    // Create PDO instance
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get the user ID from the session
    $user_id = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

    if (empty($user_id)) {
        // Redirect to logout or login page if user_id is not present
        header("Location: ../logout"); // Replace '/logout' with your actual logout URL
        exit();
    }

    // Query to get transactionHash for the signed-in user
    $sql = "SELECT transactionHash FROM usercurrenttransactionHash WHERE user_id = :user_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);

    // Execute the query
    $stmt->execute();
    $transactionHash = $stmt->fetchColumn(); // Fetch the first result

    // Query to get bitcoin_wallet for the signed-in user
    $sql = "SELECT bitcoin_wallet FROM user_profiles WHERE user_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user_id]);
    $bitcoin_wallet = $stmt->fetchColumn();

    // Assign the value to a globally accessible variable
    $GLOBALS['bitcoin_wallet'] = $bitcoin_wallet;

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
    die();
}

// Close the connection
$pdo = null;
?>




<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Payment</title>
    <style>
       
        .icontainer {
         
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
            max-width: 400px;
            width: 90%;
            height: 100vh;
  background: transparent;
        }
        .icontainer h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .icontainer p {
            margin: 10px 0;
        }
        .wallet {
          background: transparent;
  padding: 0px;
  border-radius: 4px;
  display: inline-block;
  cursor: pointer;
        }
        .wallet:hover {
            background: transparent;
        }
        .qr-code {
            margin: 20px 0;
        }
        .confirm-message {
            color: green;
            font-weight: bold;
        }
        #transaction-form {
            display: none;
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        input {
            width: calc(100% - 20px);
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            border: none;
            background-color: #28a745;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #218838;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="icontainer">
        
        
        <h1>Buy RaccoonO365 Coin</h1>
        
        <div id="ctair">
            
            
        <p>Your RaccoonO365 Panel Bitcoin Wallet Address:</p>
        <div class="wallet" onclick="copyToClipboard()" title="Click to copy">
            <span id="wallet-address"></span>
        </div>
        <div class="qr-code">
            <img id="qr-code-img" src="" alt="QR Code for Bitcoin Wallet" />
            <button id="download-qr-btn">Download QR Code</button>
        </div>
        
        </div>
        <p class="confirm-message">
            This page auto-confirms Bitcoin transactions and amounts sent. 
            Make sure to provide your transaction hash after making the payment, your transaction hash is important to confirm the payment.
        </p>
        <div class="form-group" id="ctr">
            <label for="confirm-payment">Click this button if you have made a payment:</label>
            <button id="confirm-payment">I have made the payment</button>
        </div>
        <div id="transaction-form">
            <div class="form-group">
                <label for="transaction-hash">Please provide the Transaction Hash:</label>
                <input type="text" id="transaction-hash" value="<?php echo htmlspecialchars($transactionHash); ?>" placeholder="Enter transaction hash">
            </div>
            <div class="form-group">
                <button id="submit-transaction">Submit Transaction</button>
            </div>
        </div>
    </div>

   <script>
   
   
  

$(document).ready(function () {
    $('#download-qr-btn').on('click', function () {
        const qrCodeImg = document.getElementById("qr-code-img");
        const qrCodeSrc = qrCodeImg.src;
        const link = document.createElement('a');
        
        // Set the attributes for the link
        link.href = qrCodeSrc;
        link.download = 'bitcoin-qr-code.png'; // Set the filename
        
        document.body.appendChild(link); // Append to the body
        link.click(); // Trigger the click event
        document.body.removeChild(link); // Remove the link after download
    });
});




   
    let cryptoType = "bitcoin"; // Default to Bitcoin
    let walletAddress = "";
    let autoCheckInterval; // Variable to store the interval ID

    // Get wallet address based on the crypto type
    function getWalletAddress(cryptoType) {
        const wallets = {
            bitcoin: "<?php echo $GLOBALS['bitcoin_wallet']; ?>"
        };
        return wallets[cryptoType];
    }

   function startAutoCheck(transactionHash) {
    const confirmationsNeeded = 6; // Typical for BTC to consider a transaction confirmed
    const intervalMinutes = 2; // Check every 2 minutes
    const intervalMilliseconds = intervalMinutes * 60 * 1000; // Convert to milliseconds
    
    let checkCount = 0;
    
    autoCheckInterval = setInterval(function () {
        checkPaymentStatus(transactionHash);
        checkCount++;
        
        if (checkCount >= confirmationsNeeded) {
            clearInterval(autoCheckInterval); // Stop after sufficient confirmations
            
        }
    }, intervalMilliseconds);
}


    // Function to check payment status
    function checkPaymentStatus(transactionHash) {
        const apiEndpoint = "payment.php";

        $.ajax({
            url: apiEndpoint,
            type: "GET",
            headers: {
                "Crypto-Type": cryptoType,
                "Wallet-Address": walletAddress,
                "Transaction-Hash": transactionHash
            },
            success: function (response) {
                if (response.success && (response.data.total_received_crypto > 0 || response.data.total_received_usd > 0)) {
                    Swal.fire({
                        icon: "success",
                        title: "Payment Confirmed",
                        text: `${response.success} Total received: ${response.data.total_received_crypto} ${response.data.crypto_type} ($${response.data.total_received_usd.toFixed(2)})`
                    });
                    clearInterval(autoCheckInterval); // Stop auto-checking on confirmation
                } else {
                    Swal.fire({
                        icon: "info",
                        title: "Waiting for Payment",
                        text: "Transaction has not been confirmed yet. Please wait..."
                    });
                }
            },
            error: function (xhr) {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: xhr.responseText
                });
                clearInterval(autoCheckInterval); // Stop auto-checking on error
            }
        });
    }

    // Initialize wallet address and QR code
    $(document).ready(function () {
        walletAddress = getWalletAddress(cryptoType);
        $("#wallet-address").text(walletAddress);

        const qrCodeImg = document.getElementById("qr-code-img");
        qrCodeImg.src = `https://api.qrserver.com/v1/create-qr-code/?data=bitcoin:${walletAddress}&size=200x200`;

        $("#confirm-payment").on("click", function () {
            $("#transaction-form").show();
            $("#ctair").hide();
            $("#ctr").hide();
        });
        
        
        

        $("#submit-transaction").on("click", function () {
            const transactionHash = $("#transaction-hash").val();

            if (!transactionHash) {
                Swal.fire({
                    icon: "warning",
                    title: "Oops...",
                    text: "Please provide the transaction hash."
                });
                return;
            }

            const apiEndpoint = "payment.php";

            $.ajax({
                url: apiEndpoint,
                type: "GET",
                headers: {
                    "Crypto-Type": cryptoType,
                    "Wallet-Address": walletAddress,
                    "Transaction-Hash": transactionHash
                },
                success: function (response) {
                    if (response.error) {
                        Swal.fire({
                            icon: "info",
                            title: "Info",
                            text: response.error
                        });
                    } else if (response.success && (response.data.total_received_crypto > 0 || response.data.total_received_usd > 0)) {
     Swal.fire({
        icon: "success",
        title: "Payment Confirmed",
        text: `${response.success} Total received: ${response.data.total_received_crypto} ${response.data.crypto_type} ($${response.data.total_received_usd.toFixed(2)})`
    }).then(() => {
        window.location.href = "../wallet"; // Redirect to the /wallet path
    });
    } else {
                        Swal.fire({
                            icon: "info",
                            title: "Waiting for Payment",
                            text: "Transaction has not been confirmed yet. Please wait..."
                        });
                        // Start auto-checking for payment if not confirmed
                        startAutoCheck(transactionHash);
                    }
                },
                error: function (xhr) {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: xhr.responseText
                    });
                }
            });
        });
    });

    // Copy wallet address to clipboard
    function copyToClipboard() {
        navigator.clipboard.writeText(walletAddress)
            .then(() => {
                Swal.fire({
                    icon: "success",
                    title: "Copied!",
                    text: "Bitcoin wallet address copied to clipboard.",
                    timer: 2000,
                    showConfirmButton: false
                });
            })
            .catch(() => {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Failed to copy the wallet address.",
                    timer: 2000,
                    showConfirmButton: false
                });
            });
    }
</script>


</body>
</html>