<?php


// Start session securely
session_start();

// Include database connection
require_once 'db.php';
require_once 'functions.php';




session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);


session_regenerate_id(true);


 // Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); 
    exit(); 
}


function transferFund() {
    global $pdo;

    $receiverEmail = $_POST['username'];
    $amountToSend = $_POST['amount']; // The normal amount to send
    $totalAmount = $_POST['totalAmount']; // The total amount including fees

    $senderId = $_SESSION['user_id']; 
    $email = $_SESSION['email']; 
    $username = $_SESSION['username']; 

    // Check if the sender is trying to transfer to themselves
    if ($receiverEmail === $username) {
        return ['status' => 'error', 'message' => 'Cannot transfer to yourself.'];
    }

    // Fetch the sender's wallet balance
    $stmt = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = ?");
    $stmt->execute([$senderId]);
    $walletBalance = $stmt->fetchColumn();

    // Check if the sender has sufficient balance for the total amount
    if ($walletBalance < $totalAmount) {
        return ['status' => 'error', 'message' => 'Insufficient wallet balance.'];
    }

    // Check if the receiver exists and get their account status
    $stmt = $pdo->prepare("SELECT user_id, status, username FROM user_profiles WHERE username = ?");
    $stmt->execute([$receiverEmail]);
    $receiverData = $stmt->fetch(PDO::FETCH_ASSOC);

    // If the receiver does not exist
    if (!$receiverData) {
        return ['status' => 'error', 'message' => 'Receiver does not exist.'];
    }

    // Check if the receiver's account is active
    if ($receiverData['status'] !== 'active') {
        return ['status' => 'error', 'message' => 'Receiver account is not active.'];
    }

    // Proceed with the transfer logic
    try {
        // Begin transaction
        $pdo->beginTransaction();

        // Deduct amount from sender's wallet
        $stmt = $pdo->prepare("UPDATE wallet SET balance = balance - ? WHERE user_id = ?");
        $stmt->execute([$totalAmount, $senderId]); // Deduct total amount including fee

        // Add amount to receiver's wallet
        $stmt = $pdo->prepare("UPDATE wallet SET balance = balance + ? WHERE user_id = ?");
        $stmt->execute([$amountToSend, $receiverData['user_id']]); // Add only the amount to send

        // Commit transaction
        $pdo->commit();

        // Generate a unique transaction ID
        $transactionId = uniqid('txn_');

        // Save notifications for both users
        saveNotification($senderId, 'Debit', "Transferred coins to " . $receiverData['username']);
        saveNotification($receiverData['user_id'], 'Credit', "Received coins from " . $username);

        // Log the wallet transactions
        saveWalletTransaction($senderId, 'Transfer', $amountToSend, $transactionId, 'Debit', 'Completed');
        saveWalletTransaction($receiverData['user_id'], 'Received', $amountToSend, $transactionId, 'Credit', 'Completed');

        return ['status' => 'success', 'message' => 'Coins transferred successfully.']; 
    } catch (Exception $e) {
        // Roll back the transaction if an error occurs
        if ($pdo->inTransaction()) {
            $pdo->rollBack(); // Rollback only if a transaction is active
        }
        return ['status' => 'error', 'message' => 'An error occurred during the transfer: ' . $e->getMessage()];
    }
}


// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

if (isset($_POST['username']) && isset( $_POST['amount'] ) ) {
    $response = transferFund();
}

// Send a JSON response
header('Content-Type: application/json');
echo json_encode($response);