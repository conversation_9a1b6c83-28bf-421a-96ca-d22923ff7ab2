<?php
require('db.php');

    echo 'Callback called';

// Get the callback data
$data = json_decode(file_get_contents('php://input'), true);

if ($data['status'] === 'completed') {
    $user_id = $data['user_id'];
    $amount = $data['amount'];

    // Update the user's wallet balance
    $stmt = $pdo->prepare("UPDATE wallet SET balance = balance + :amount WHERE user_id = :user_id");
    $stmt->bindParam(':amount', $amount, PDO::PARAM_STR);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();

    echo "Callback done";
}
?>
