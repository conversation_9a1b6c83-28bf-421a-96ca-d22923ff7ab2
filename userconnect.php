<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Include the content of nameservers.php again
include('nameservers.php');  // or use require('nameservers.php');


// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if user_profiles table exists and is using InnoDB
    $checkUserProfilesTableQuery = "SHOW TABLE STATUS WHERE name = 'user_profiles';";
    $stmt = $pdo->query($checkUserProfilesTableQuery);
    $userProfilesTable = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($userProfilesTable && $userProfilesTable['Engine'] !== 'InnoDB') {
        // Convert user_profiles table to InnoDB if it's not already
        $pdo->exec("ALTER TABLE user_profiles ENGINE = InnoDB;");
    }

    // Create the dnsdomain_requests table with foreign key constraint referencing user_profiles
      $createTableQuery = "
    CREATE TABLE IF NOT EXISTS QRCodeSettingsdnsdomain_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            domain_name VARCHAR(255) NOT NULL,
            status VARCHAR(255) DEFAULT 'Pending',
            has_updated TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
        ) ENGINE=InnoDB;
        ";
        
    $createTableQuery = "
    CREATE TABLE IF NOT EXISTS dnsdomain_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        domain_name VARCHAR(255) NOT NULL,
        status VARCHAR(255) DEFAULT 'Pending',
        has_updated TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
    ) ENGINE=InnoDB;
";
    $pdo->exec($createTableQuery);
    
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
    exit();
}




// Fetch domain requests for the signed-in user
$userId = $_SESSION['user_id'];
$stmt = $pdo->prepare("SELECT * FROM dnsdomain_requests WHERE user_id = :user_id");
$stmt->execute(['user_id' => $userId]);
$domainRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);


// Initialize $idomain as empty
$idomain = "";
// Extract the domain name (including extension)
$idomain = $domainRequests[0]['domain_name'];

$response = ""; // Initialize response variable

// Validate the domain now
if (!empty($idomain) && filter_var($idomain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
    // Get NS records using dns_get_record
    $nsRecords = dns_get_record($idomain, DNS_NS);

    if ($nsRecords) {
        $response .= "Detected Name Server Records for $idomain:\n";
        $cloudflareCount = 0;
        $nonCloudflareRecords = [];

        foreach ($nsRecords as $ns) {
            $response .= $ns['target'] . "\n";
            if (stripos($ns['target'], ".ns.cloudflare.com") !== false) {
                $cloudflareCount++;
            } else {
                $nonCloudflareRecords[] = $ns['target'];
            }
        }

        // Evaluate the situation and update the domain status if Cloudflare name servers are correctly configured
        if ($cloudflareCount === 2 && count($nonCloudflareRecords) === 0) {
            // $response .= "\nContent delivery network name servers are correctly configured!";
            
            // // Update the status of the domain to 'connected'
            // $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
           
            
            
            
            // if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
               
               
                
                
            // } else {
            //     $response .= "\nFailed to update domain status.";
            // }
        } elseif ($cloudflareCount === 2 && count($nonCloudflareRecords) > 0) {
            $response .= "\nContent delivery network name servers are detected, but there are additional non-content delivery network name servers present. Please remove the following non-content delivery network name servers to make it work:\n";
            
             echo "<script>
                    setTimeout(function() {
                        window.location.href = window.location.href.split('?')[0] + '?' + new Date().getTime();

                    }, 50000); // 1-second delay before refresh
                  </script>";
                  
            foreach ($nonCloudflareRecords as $record) {
                $response .= $record . "\n";
            }
            
            // // Update the status of the domain to 'connected'
            // $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
            
            







// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    //var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}








            
            
            
            
            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
            }
            
        } elseif ($cloudflareCount === 1) {
            $response .= "\nOnly one content delivery network name server is detected. Please update the second name server to be a content delivery network name server and remove any non-Cloudflare name servers.";
            
             echo "<script>
                    setTimeout(function() {
                        window.location.href = window.location.href.split('?')[0] + '?' + new Date().getTime();

                    }, 50000); // 1-second delay before refresh
                  </script>";
                  
                  
            //          // Update the status of the domain to 'connected'
            // $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
            
            
            
// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}


            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
            }
            
        } elseif ($cloudflareCount === 0) {
            $response .= "\nNo content delivery network name servers detected. Please update both name servers to the provided content delivery network records above.";
            
             echo "<script>
                    setTimeout(function() {
                        window.location.href = window.location.href.split('?')[0] + '?' + new Date().getTime();

                    }, 50000); // 1-second delay before refresh
                  </script>";
                  
                  
            //          // Update the status of the domain to 'connected'
            // $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
            
                    
// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}

            
            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
            }
        } else {
            $response .= "\nAn unexpected configuration is detected. Please ensure only two content delivery network name servers exist.";
            
             echo "<script>
                    setTimeout(function() {
                        window.location.href = window.location.href.split('?')[0] + '?' + new Date().getTime();

                    }, 50000); // 1-second delay before refresh
                  </script>";
                  
                  
            //          // Update the status of the domain to 'connected'
            // $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
                    
// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}

            
            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
                
            //       // Update the status of the domain to 'connected'
            // $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
            
                    
// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}

            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
            }
            }
        }
    } else {
        $response .= "\nNo name servers found for $idomain.";
        
        //   // Update the status of the domain to 'connected'
        //     $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
        
        
                
// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}


            
            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
            }
    }
} else {
    $response .= "\nInvalid domain name or no domain provided. Please check the domain format.";
    
    //   // Update the status of the domain to 'connected'
    //         $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = 'Not Connected' WHERE domain_name = :domain AND user_id = :user_id");
            
            
            
            
                    
// Fetch the current value of has_updated and status from the database
$stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
$stmt->execute(['domain' => $idomain, 'user_id' => $userId]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    $has_updated = $result['has_updated'];
    $status = $result['status'];

    // Debug fetched data
    var_dump($has_updated, $status);

    // Update has_updated to 0 if it's 1
    if ($has_updated == 1) {
        $updateHasUpdatedStmt = $pdo->prepare("
            UPDATE dnsdomain_requests 
            SET has_updated = 0 
            WHERE domain_name = :domain AND user_id = :user_id
        ");
        if (!$updateHasUpdatedStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
            // print_r($updateHasUpdatedStmt->errorInfo());
        } else {
            // echo "has_updated successfully updated to 0.";
        }
    }

    // Update status_message
    $status_message = "Not Connected"; // Default value
    $updateStmt = $pdo->prepare("
        UPDATE dnsdomain_requests 
        SET status = :status_message 
        WHERE domain_name = :domain AND user_id = :user_id
    ");
    if (!$updateStmt->execute(['status_message' => $status_message, 'domain' => $idomain, 'user_id' => $userId])) {
       // print_r($updateStmt->errorInfo());
    } else {
        // echo "Status updated successfully.";
    }
}


            
            if ($updateStmt->execute(['domain' => $idomain, 'user_id' => $userId])) {
                $response .= "\nDomain status updated to 'Not Active'.";
                
                
                
            } else {
                $response .= "\nFailed to update domain status.";
            }
}



function getResponseStatus($url) {
    // Get headers from the URL
    $headers = @get_headers($url);

    // If headers were retrieved successfully
    if ($headers) {
        // Get the HTTP status code (first element in the response headers)
        $statusCode = substr($headers[0], 9, 3);
        
        // Return the status code
        return $statusCode;
    } else {
        return false; // Failed to retrieve headers, domain might be unreachable
    }
}

function checkDNSRecord($domain) {
    // Check if the domain has a valid DNS record of any type (A, CNAME, MX, TXT, etc.)
    $recordTypes = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SOA'];

    foreach ($recordTypes as $type) {
        if (checkdnsrr($domain, $type)) {
            return true; // If any valid DNS record type is found, return true
        }
    }
    
    return false; // No valid DNS records found
}

// Example usage
$statusdomina = $domainRequests[0]['domain_name']; // Replace with the domain you want to check
$statusCode = getResponseStatus("https://$statusdomina");



// Get the signed-in user's ID from the session
$signedInUserId = $_SESSION['user_id'];


// Ensure the domain is provided (either from a query parameter or a variable)
$freemain = "$idomain"; // Domain can be passed as a query parameter

// // If freemain is not provided or is empty, show an error
// if (empty($freemain)) {
//     die('freemain is not specified.');
// }

// Fetch the nameservers for the specified freemain using PDO
$query = $pdo->prepare("SELECT nameserver FROM nameservers WHERE domain = :freemain");
$query->bindParam(':freemain', $freemain, PDO::PARAM_STR);
$query->execute();
$nameservers = $query->fetchAll(PDO::FETCH_ASSOC);




?>

<!DOCTYPE html>
<html>
<head>
    <title>Submit Domain Request</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(to right, #3a7bd5, #00d2ff);
            color: #fff;
            min-height: 100vh;
        }
        h1, h2, h3 {
            margin: 0;
            padding: 0;
            font-weight: bold;
        }
        h1 {
            color: #fff;
           
            padding: 20px;
            font-size: 36px;
        }
        h2, h3 {
            color: #f7f7f7;
            margin-top: 20px;
           
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0 20px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f4f4f4;
            color: #333;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #ff6a00, #ff3a00);
            border: none;
            color: white;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: linear-gradient(45deg, #ff3a00, #ff6a00);
            transform: translateY(-2px);
        }

        .status {
            padding: 20px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .status p {
            font-size: 16px;
            margin: 10px 0;
        }

        .success {
            color: white;
            font-weight: bold;
        }

        .redirect {
            color: white;
            font-weight: bold;
        }

        .client-error {
            color: white;
            font-weight: bold;
        }

        .server-error {
            color: white;
            font-weight: bold;
        }

        .offline {
            color: white;
            font-weight: bold;
        }

        .dns-error {
            color:white;
            font-weight: bold;
        }

    </style>
    
</head>
<body>
    <div class="container">
    <h2>Update the Nameservers for: <?php echo htmlspecialchars($freemain); ?> to the content delivery network records below</h2>

<?php if (empty($nameservers)): ?>
    <div class="message error">
        <p>We have detected that your domain, <?php echo htmlspecialchars($freemain); ?>, already has Cloudflare nameserver records that were not issued by us. To ensure the smooth operation of our services and prevent any potential issues, we kindly request that you change your domain to one that does not belong to Cloudflare or does not have any Cloudflare nameserver records that is not issued by us.</p>
    </div>
    
 <?php
 
 
// Function to fetch Cloudflare email and API token from the database for the current user
function getCloudflareCredentials() {
    global $pdo; // Use the global PDO connection
    $user_id = $_SESSION['user_id']; // Get the user_id from the session

    // Prepare and execute the SQL query
    $sql = "SELECT cloudflareemail, cloudflareapikey FROM user_profiles WHERE user_id = :user_id LIMIT 1";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();

    // Fetch the result
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($row) {
        // Assign the values to global variables
        global $cloudflareEmail, $cloudflareApiKey;
        $cloudflareEmail = $row['cloudflareemail'];
        $cloudflareApiKey = $row['cloudflareapikey'];
    } else {
        echo "No records found for the current user.";
    }
}

// Call the function to get the Cloudflare credentials for the current user
getCloudflareCredentials();

// Correctly set the email and API key
$email = $cloudflareEmail; // Use the global variable directly
$apiKey = $cloudflareApiKey; // Use the global variable directly

function checkCloudflareNameserverStatus($sdkhedomain, $apiKey, $email) {
    $apiUrl = "https://api.cloudflare.com/client/v4/zones?name=" . urlencode($sdkhedomain);

    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Auth-Email: $email",     // Correct header for global API key authentication
        "X-Auth-Key: $apiKey",      // Global API Key
        "Content-Type: application/json"
    ]);

    // Execute API request
    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        return "cURL Error: " . curl_error($ch);
    }

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        return "Failed to connect to Cloudflare API. HTTP Status Code: $httpCode. Response: $response";
    }

    $data = json_decode($response, true);

    if (!$data['success']) {
        return "Error: " . implode(", ", array_column($data['errors'], 'message'));
    }

    $zone = $data['result'][0] ?? null;

    if ($zone) {
        $status = $zone['status'];
        if ($status === "active") {
            
            // Fetch the current value of has_updated and status from the database
            global $pdo;
            $stmt = $pdo->prepare("SELECT has_updated, status FROM dnsdomain_requests WHERE domain_name = :domain AND user_id = :user_id");
            $stmt->execute(['domain' => $sdkhedomain, 'user_id' => $_SESSION['user_id']]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                $has_updated = $result['has_updated'];
                $status = $result['status'];

                // Initialize status_message with a default value
                $status_message = $status; // Retain current status by default
                
            

                // Update the status_message based on conditions
                if ($has_updated == 1 && $status == "Connected") {
                    $status_message = "Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.";
                } elseif ($has_updated == 0) {
                    $status_message = "Connected";
                }

                // Update the status in the database only if there's a valid status_message
                if (!empty($status_message)) {
                    $updateStmt = $pdo->prepare("UPDATE dnsdomain_requests SET status = :status_message WHERE domain_name = :domain AND user_id = :user_id");
                    $updateStmt->execute(['status_message' => $status_message, 'domain' => $sdkhedomain, 'user_id' => $_SESSION['user_id']]);
                }
            }

            // Custom CSS for success status
            $css = "
            <style>
                #redirect-button {
                   display:block !important;
                }
            </style>";

            // Echo the CSS
            echo $css;

            return "The nameservers for $sdkhedomain are correctly set and the domain is active.";
        } elseif ($status === "pending") {
            return "The nameservers for $sdkhedomain are pending. Current status: $status.";
        } else {
            return "The nameservers for $sdkhedomain are not correctly set. Current status: $status.";
        }
    }

    return "No record found for the domain: $sdkhedomain.";
}

// Example usage
$sdkhedomain = "$freemain"; // Ensure $freemain is set before this

$statusMessage = checkCloudflareNameserverStatus($sdkhedomain, $apiKey, $email);

if (strpos($statusMessage, 'active') !== false) {
    echo "<span class='success'>$statusMessage</span>";
} elseif (strpos($statusMessage, 'pending') !== false) {
    echo "<span class='pending'>$statusMessage</span>";
} elseif (strpos($statusMessage, 'not correctly set') !== false) {
    echo "<span class='error'>$statusMessage</span>";
} else {
    echo $statusMessage;
}
?>



<?php else: ?>
    <ul class="nameservers">
        <?php foreach ($nameservers as $index => $nameserver): ?>
            <li><?php echo 'NameServer ' . ($index + 1) . ': ' . htmlspecialchars($nameserver['nameserver']); ?></li>
        <?php endforeach; ?>
    </ul>
<?php endif; ?>



<?php
        if ($statusCode) {
            
            if ($statusCode >= 200 && $statusCode < 300) {
                echo "<p class='success'>$statusdomina your domain is online, and not dead.</p>";
            } elseif ($statusCode >= 300 && $statusCode < 400) {
                echo "<p class='redirect'>$statusdomina your domain is online, and not dead.</p>";
            } elseif ($statusCode >= 400 && $statusCode < 500) {
                echo "<p class='client-error'>$statusdomina your domain is online, and not dead.</p>";
            } elseif ($statusCode >= 500 && $statusCode < 600) {
                echo "<p class='server-error'>$statusdomina your domain is online, and not dead.</p>";
            }
        } else {
            if (checkDNSRecord($statusdomina)) {
                echo "<p class='dns-error'>Your domain ($statusdomina) is active but not yet fully connected to your panel.</p>";
            } else {
                echo "<p class='offline'>Your RaccoonO365 cookies link ($statusdomina) is offline or unreachable. No DNS record exists.</p>";
            }
        }
        ?>
        
        
        <!-- Show domain requests submitted by the logged-in user -->
        <?php if ($domainRequests): ?>
          
            <ul>
                <?php foreach ($domainRequests as $request): ?>
                    <li><?php echo htmlspecialchars($request['domain_name']); ?> (Status: <?php echo $request['status']; ?>)</li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

 <button id="redirect-button" style="display:none;">Apply Nameserver Configuration</button>
        
        
        <h1>Current Connected Domain NameServers</h1>
        <div>
            <?php echo nl2br(htmlspecialchars($response)); ?>
            
             <?php
// Replace this with the actual domain you want to check
// $idomain = 'example.com'; 

// Fetch the NS records for the domain using dns_get_record
$nsRecords = dns_get_record($idomain, DNS_NS);

// Extract the dsecords from the NS records
$dsecords = [];
foreach ($nsRecords as $dsecord) {
    $dsecords[] = $dsecord['target'];
}

// Assuming $nameservers is populated from the previous code (you can pass it or fetch it dynamically)
// Dynamic fetch of required dsecords from the nameservers array
$requiredDsecords = [];
if (isset($nameservers) && is_array($nameservers)) {
    foreach ($nameservers as $nameserver) {
        $requiredDsecords[] = $nameserver['nameserver'];
    }
}

// Function to compare current and required dsecords
function compareDsecords($dsecords, $requiredDsecords) {
    $responseMessage = '';

    // Check if the current dsecords match the required ones
    if (count($dsecords) == count($requiredDsecords) && empty(array_diff($dsecords, $requiredDsecords))) {
        // No action needed if they match
        $responseMessage = "<p class='success'>Your domain NameServers are correctly configured! No changes required.</p>";
    } else {
        // If dsecords are incorrect, point out the mismatched ones
        $responseMessage = "<p class='warning'>Your domain Current NameServers does not match the required configuration.</p>";

        // Collect the dsecords to delete (those that are not part of required dsecords)
        $deleteDsecords = [];
        foreach ($dsecords as $dsecord) {
            if (!in_array($dsecord, $requiredDsecords)) {
                $deleteDsecords[] = $dsecord;
            }
        }

        // If there are dsecords to delete, show the delete message
        if (count($deleteDsecords) > 0) {
            $responseMessage .= "<p class='warning'>Delete the following NameServers from your domain:</p>";
            $responseMessage .= "<ul>";
            foreach ($deleteDsecords as $dsecord) {
                $responseMessage .= "<li>$dsecord</li>";
            }
            $responseMessage .= "</ul>";
        }

        // Check if the required dsecords are already present in the current dsecords
        $missingDsecords = array_diff($requiredDsecords, $dsecords);

        // If there are missing dsecords, show the message to add them
        if (count($missingDsecords) > 0) {
            $responseMessage .= "<p class='info'>Add both of your assigned RaccoonO365 NameServers:</p>";
            $responseMessage .= "<ul>";
            foreach ($missingDsecords as $dsecord) {
                $responseMessage .= "<li>$dsecord</li>";
            }
            $responseMessage .= "</ul>";
        } else {
            // If no dsecords are missing, hide the add dsecord message
            $responseMessage .= "<p class='success'>All required assigned RaccoonO365 NameServers are already added.</p>";
        }

        // Show the "Save your changes" message
        $responseMessage .= "<p class='save-instruction'>Save your changes.</p>";
    }

    return $responseMessage;
}

// Call the function and display the result
echo compareDsecords($dsecords, $requiredDsecords);
?>
            
        </div>
        
     
        <div class="preloader" id="preloader">
        <div class="loader"></div>
    </div>
        
      <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
 <script>
 
  // Function to create and inject CSS
        function injectCSS() {
            const css = `
                .preloader {
                    display: none; /* Hidden by default */
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(255, 255, 255, 0.8);
                    z-index: 9999;
                    text-align: center;
                    justify-content: center;
                    align-items: center;
                }
                .preloader.active {
                    display: flex; /* Show when active */
                }
                .preloader .loader {
                    border: 12px solid #f3f3f3;
                    border-radius: 50%;
                    border-top: 12px solid #3498db;
                    width: 60px;
                    height: 60px;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            const styleSheet = document.createElement("style");
            styleSheet.type = "text/css";
            styleSheet.innerText = css;
            document.head.appendChild(styleSheet);
        }

        // Call the function to inject CSS
        injectCSS();
        
        document.getElementById("redirect-button").addEventListener("click", function() {
            var preloader = document.getElementById("preloader");
            preloader.classList.add("active");

            fetch('../connector.php')
                .then(response => response.text())
                .then(data => {
                    preloader.classList.remove("active");
                    Swal.fire({
                        title: 'Connection Successful',
                        text: data,
                        icon: 'success'
                    }).then(() => {
                        // Reload the page when "OK" is clicked
                        location.reload();
                    });
                })
                .catch(error => {
                    preloader.classList.remove("active");
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'An error occurred',
                        text: error,
                        icon: 'error'
                    });
                });
        });
    </script>
    
    
        
</body>
</html>

