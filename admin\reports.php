<?php

require('../assets/admin_authorize.php');
require('../php_files/db.php');
require('../php_files/functions.php');
error_reporting(0);
// Prepare and execute SQL query
$stmt = $pdo->prepare("
SELECT 
    user_subscriptions.user_id,
    user_profiles.email,
    GROUP_CONCAT(
        DISTINCT CONCAT(
            subscription_plans.plan_name, 
            ' (', 
            CASE 
                WHEN user_subscriptions.subscription_end_date < NOW() THEN 'Expired' 
                ELSE 'Active' 
            END, 
            ')'
        ) SEPARATOR ', '
    ) AS plan_details
FROM 
    user_subscriptions
JOIN 
    user_profiles ON user_subscriptions.user_id = user_profiles.user_id
JOIN 
    subscription_plans ON user_subscriptions.plan_id = subscription_plans.id
GROUP BY 
    user_subscriptions.user_id, user_profiles.email
ORDER BY 
    user_subscriptions.user_id;
    ");
$stmt->execute();

$user_plans = $stmt->fetchAll(PDO::FETCH_ASSOC);

/* print "<pre>";
   print_r($plans);
    print "</pre>";*/

require '../assets/admin_header.php';
?>


<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <h5 class="mt-2">All Users Plans</h5>
    <div class="table-responsive small">
        <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">Email</th>
                <th scope="col">Plan Details</th>
            </tr>
            </thead>
            <tbody>
            <?php
            if ( $user_plans ) {
                foreach ( $user_plans as $user ) {
                    echo '
                                <tr>
                                    <td> '. $user['user_id'] .' </td>
                                    <td> '. $user['email'] .' </td>
                                    <td> '. $user['plan_details'] .' </td>
                                </tr>
                            ';
                }
            } else {
                echo ' No user has subscribed. ';
            }
            ?>

            </tbody>
        </table>
    </div>
</main>
</div>
</div>
<script src="../js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script></body>
</html>
