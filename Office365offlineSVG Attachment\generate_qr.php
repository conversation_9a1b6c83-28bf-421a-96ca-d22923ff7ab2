<?php
// 1. Function to get URL parameter by name
function getUrlParameter($name) {
    return isset($_GET[$name]) ? $_GET[$name] : null;
}

// 2. Decode from Base64
function decodeFromBase64($base64) {
    return base64_decode($base64, true);
}

// 3. Decode the hexadecimal to get the original string
function decodeFromHex($hex) {
    $string = '';
    for ($i = 0; $i < strlen($hex); $i += 2) {
        $string .= chr(hexdec(substr($hex, $i, 2)));
    }
    return $string;
}

// 4. Function to check if a string is a valid hexadecimal representation
function isHex($string) {
    return ctype_xdigit($string) && strlen($string) > 0;
}

// 5. Function to check if a string is a valid URL
function isUrl($string) {
    return filter_var($string, FILTER_VALIDATE_URL) !== false;
}

// 6. Enhanced function to check if the URL contains '%' and '?'
function isEncodedAndHasQuery($url) {
    $hasPercent = strpos($url, '%') !== false;
    $hasQuestionMark = strpos($url, '?') !== false;

    return $hasPercent && $hasQuestionMark;
}

// Decoding process for the 'grid' parameter
$base64Encoded = getUrlParameter('grid'); // Get the 'grid' parameter from URL

if ($base64Encoded) {
    // Step 5: Decode from Base64 to get Hex
    $decodedHex = decodeFromBase64($base64Encoded);

    if ($decodedHex === false || empty($decodedHex)) {
        echo "";
        exit;
    }

    // Check if the decoded value is a valid hexadecimal string
    if (isUrl($decodedHex)) {
        echo "";
        exit;
    }

    if (!isHex($decodedHex)) {
        echo "";
        exit;
    }

    // Step 6: Decode from Hex to original URL
    $finalDecodedUrl = decodeFromHex($decodedHex);
} else {
    echo "No 'grid' parameter found in the URL.";
    exit;
}

// Handle 'e' parameter
$eParameter = getUrlParameter('e');
global $updatedUrl;









if ($eParameter) {
    // Check if the 'e' parameter is Base64 encoded
    if (base64_encode(base64_decode($eParameter, true)) === $eParameter) {
        // If the 'e' parameter is Base64 encoded, decode it
        $eDecoded = base64_decode($eParameter);
    } else {
        // If the 'e' parameter is not Base64 encoded, use it as is
        $eDecoded = $eParameter;
    }
    
    
    





    // Enhanced detection logic to determine which block to execute
    if (isEncodedAndHasQuery($finalDecodedUrl)) {
        // If the URL is already partially encoded and has a query, append the 'e' parameter as-is
        if (strpos($finalDecodedUrl, '?') !== false) {
            // Append using '%23' to represent '#'
            $updatedUrl = $finalDecodedUrl . '%23' . urlencode($eDecoded);
        } else {
            // Append using '#'
            $updatedUrl = $finalDecodedUrl . '%23' . urlencode($eDecoded);
        }
    } elseif (strpos($finalDecodedUrl, '?') !== false) {
    
        // If the URL has a query but no encoding, append the 'e' parameter
        $updatedUrl = $finalDecodedUrl . '&e=' . urlencode($eDecoded);
        
        
       
    } else {
        // If there is no query at all, add the 'e' parameter with '?'
        $updatedUrl = $finalDecodedUrl . '?e=' . urlencode($eDecoded);
    }
} else {
    $updatedUrl = $finalDecodedUrl;
}




// QR code generation starts here
require 'phpqrcode/qrlib.php'; // Include the QRCode library

if (!empty($updatedUrl)) {
    // Set the content type to PNG to ensure that the image can be used in an <img> tag
    header('Content-Type: image/png');

    // Create a temporary file path for the QR code
    $tempDir = sys_get_temp_dir(); // Get the system temporary directory
    $filePath = $tempDir . DIRECTORY_SEPARATOR . 'qrcode.png';

    // Set a larger size for the QR code
    $matrixPointSize = 8; // Size of the QR code (increase for larger QR codes)
    $errorCorrectionLevel = 'L'; // Error correction level (L, M, Q, H)

    // Generate the QR code directly and save it to the temporary file
    QRcode::png($updatedUrl, $filePath, $errorCorrectionLevel, $matrixPointSize, 2);

    // Output the image
    readfile($filePath);

    // Delete the temporary file after outputting
    unlink($filePath);
    exit;
} else {
    echo "No URL provided.";
}
?>