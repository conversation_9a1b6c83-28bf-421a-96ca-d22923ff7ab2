<?php
require '../assets/admin_header.php';
// require '../php_files/db.php';



// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 



  // Step 1: Database configuration and connection
$host = $config['host']; // Database host
$dbname = $config['dbname']; // Database name
$username = $config['username']; // Database username
$password = $config['password']; // Database password





// Step 2: Create PDO connection
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // If connection fails, show an error message
    die("Connection failed: " . $e->getMessage());
}

// Step 3: Define the queries to fetch users and subscription plans
$users_query = "SELECT * FROM user_profiles WHERE user_type = 'regular'";
$subscribers_query = "SELECT * FROM subscription_plans";

// Step 4: Execute the queries and handle errors
try {
    // Fetch users with user_type = 'regular'
    $stmt = $pdo->prepare($users_query);
    $stmt->execute();
    $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

   

    // Fetch all subscription plans
    $stmt_subs = $pdo->prepare($subscribers_query);
    $stmt_subs->execute();
    $all_subscriptions = $stmt_subs->fetchAll(PDO::FETCH_ASSOC);

    

} catch (PDOException $e) {
    // Handle any query execution errors
    echo 'Query failed: ' . $e->getMessage();
}
?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
    pt-3
    pb-2 mb-3 border-bottom">
        <h2>All Users</h2>
        <button type="button" class="btn btn-secondary btn-sm open-modal-extra"
                data-bs-toggle="modal" data-bs-target="#addExtraDaysToAll"> Add extra days to all users </button>
    </div>


    <div class="table-responsive small" style="height: 450px">
        <table class="table table-striped table-sm h-full">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">Username</th>
                <th scope="col">Email</th>
                <th scope="col">Status</th>
                <th scope="col">Subscribed</th>
                <th scope="col"></th>
            </tr>
            </thead>
            <tbody>
            <?php
            if ( $all_users ) {
                foreach ( $all_users as $user ) {
                    if ( $user['status'] == 'locked' ) {
                        $button_action = '<li><button id="key-'. $user['user_id'] .'" onclick="unlockUser('. $user['user_id']
                            .')" class="dropdown-item" type="button"> Unlock User </button></li>';
                    } else {
                        $button_action = '<li><button id="key-'. $user['user_id'] .'" onclick="lockUser('. $user['user_id'] .')" class="dropdown-item" type="button"> Lock User </button></li>';
                    }
                    echo '
                                <tr>
                                    <td> '. $user['user_id'] .' </td>
                                    <td> '. $user['username'] .' </td>
                                    <td> '. $user['email'] .' </td>
                                    <td> '. $user['status'] .' </td>
                                    <td>' . ($user['subscribed'] > 0 ? 'Subscribed' : 'Not Subscribed') . '</td>
                                    <td>
                                        <div class="btn-group">
                                              <button class="btn btn-secondary bg-transparent border-0 btn-sm dropdown-toggle" type="button" id="dropdownMenuButton" aria-expanded="false">
        <i class="bi bi-three-dots-vertical"></i>
                                          </button>
                                         <ul class="dropdown-menu" id="dropdownMenu" style="display: none;">
                                             <li>
                                                <button class="dropdown-item" type="button">
                                                    <a href="'. BASE_URL . '/admin/view_profile.php?user_id=' . $user['user_id'] . '"> View Profile </a>
                                                </button>
                                            </li>
                                            ' . $button_action;

                                            // Only show "Add extra days" button if the user has at least one active subscription
                                            if ($user['subscribed'] > 0) {
                                                echo    '<li><button type="button" class="open-modal-extra dropdown-item" data-bs-toggle="modal" data-user-id="' . $user['user_id'] . '" data-bs-target="#addExtraDays">Add extra days</button></li>
                                                        <li><button class="dropdown-item" type="button" onclick="cancelAllSubsForUser('. $user['user_id'] .')">Cancel subscriptions</button></li>
                                                        <li><button class="dropdown-item" type="button" onclick="resetSubscription('. $user['user_id'] .')">Reset subscriptions</button></li>
                                                ';
                                            }
                                            echo '
                                                        <li><button class="dropdown-item" type="button" onclick="resetPassword('. $user['user_id'] .')">Reset password</button></li>
                                                        <li><button type="button" class="open-modal-extra open-email-model dropdown-item" data-bs-toggle="modal" data-user-id="' . $user['user_id'] . '" data-email="'. $user['email'] .'" data-bs-target="#emailUpdate">Update email</button></li>
                                                        <li><button type="button" class="open-modal-extra open-wallet-model dropdown-item" data-bs-toggle="modal" data-user-id="' . $user['user_id'] . '" data-bs-target="#walletUpdate">Fund user wallet</button></li>
                                                        <li><button type="button" class="open-modal-extra open-subscribe-model dropdown-item" data-bs-toggle="modal" data-user-id="' . $user['user_id'] . '" data-bs-target="#subscribeModal">Subscribe user</button></li>
                                        </ul>
                                        </div>
                                    </td>
                                </tr>
                            ';
                }
            } else {
                echo ' No user has been created. Click <a href="create_user.php">here</a> to create a user ';
            }
            ?>

            </tbody>
        </table>
        <!-- Modal -->
        <div class="modal fade" id="addExtraDays" aria-hidden="true"
             aria-labelledby="exampleModalToggleLabel" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <form id="addDaysForm" class="w-100">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add extra days
                            </h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                                <div class="mb-3">
                                    <label for="exampleFormControlInput1" class="form-label"> Add extra
                                        subscription days</label>
                                    <input type="number" required class="form-control"
                                           id="exampleFormControlInput1" name="days"
                                           placeholder="add days">
                                    <input type="hidden" readonly value="" id="user_id"
                                           name="user_id">
                                </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" id="submitForm"> Save </button>
                            <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="modal fade" id="addExtraDaysToAll" aria-hidden="true"
             aria-labelledby="exampleModalToggleLabel" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <form id="addDaysToAllForm" class="w-100">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add extra
                                days for all subscribers
                            </h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="exampleFormControlInput1" class="form-label"> Add
                                    extra subscription days to all</label>
                                <input type="number" required class="form-control"
                                       id="exampleFormControlInput1" name="days"
                                       placeholder="add days">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" id="submitFormToAll"> Save </button>
                            <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="modal fade" id="emailUpdate" aria-hidden="true"
             aria-labelledby="exampleModalToggleLabel" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <form id="emailUpdateForm" class="w-100">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Update email
                            </h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <input type="email" required class="form-control"
                                       id="updateEmail" name="email"
                                       placeholder="account email">
                                <input id="email-update-user-id" type="hidden" name="user-id">

                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" id="submitForEmail"> Save </button>
                            <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="modal fade" id="walletUpdate" aria-hidden="true"
             aria-labelledby="exampleModalToggleLabel" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <form id="walletUpdateForm" class="w-100">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalToggleLabel">Fund user
                                wallet
                            </h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="exampleInputEmail1" class="form-label">Enter amount
                                    (USD)</label>
                                <input type="number" required name="amount" class="form-control"
                                       id="walletInput"
                                       aria-describedby="emailHelp">
                                <input id="wallet-user-id" type="hidden" name="user-id">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" id="submitWalletForm"> Save </button>
                            <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="modal fade" id="subscribeModal" aria-hidden="true"
             aria-labelledby="exampleModalToggleLabel" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <form id="subscribeForm" class="w-100">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalToggleLabel">Fund user
                                wallet
                            </h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="exampleInputEmail1" class="form-label"> Choose a
                                    subscription plan </label>
                                <select name="plan_id" class="form-select" aria-label="Default
                                select
                                example">
                                    <?php
                                        foreach (  $all_subscriptions as $sub ) {
                                            echo '
                                                <option value="'. $sub['id'] .'">'. $sub['plan_name']
                                                .'</option>  
                                            ';
                                        }
                                    ?>
                                </select>
                                <input id="sub-user-id" type="hidden" name="user_id">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary"
                                    id="submitSubscribeForm">Subscribe</button>
                            <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </div>
</main>
</div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/5.1.0/js/bootstrap.bundle.min.js"></script>

<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>


<script src="../js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Script to handle dropdown actions -->
<script>
// Select the dropdown button and menu
const dropdownButton = document.getElementById('dropdownMenuButton');
const dropdownMenu = document.getElementById('dropdownMenu');

// Function to toggle the visibility of the dropdown menu
dropdownButton.addEventListener('click', function (event) {
    event.stopPropagation(); // Prevent the click event from bubbling up to the document
    if (dropdownMenu.style.display === 'none' || dropdownMenu.style.display === '') {
        dropdownMenu.style.display = 'block'; // Show the menu
    } else {
        dropdownMenu.style.display = 'none'; // Hide the menu
    }
});

// Close the dropdown menu when clicking outside of it
document.addEventListener('click', function (event) {
    // Check if the clicked element is neither the dropdown button nor the dropdown menu
    if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
        dropdownMenu.style.display = 'none'; // Hide the menu if clicked outside
    }
});





document.addEventListener("DOMContentLoaded", function() {
    document.querySelectorAll('.open-modal-extra').forEach(button => {
        button.addEventListener('click', function() {
            let userId = this.getAttribute('data-user-id');
            console.log("User ID passed to modal:", userId);  // Debugging
            document.querySelector("#addExtraDays input[name='user_id']").value = userId;
        });
    });
});



document.addEventListener('DOMContentLoaded', function () {
    // For opening the email update modal
    const emailModalTriggers = document.querySelectorAll('.open-email-model');
    emailModalTriggers.forEach(function (trigger) {
        trigger.addEventListener('click', function () {
            const userId = trigger.getAttribute('data-user-id');
            const email = trigger.getAttribute('data-email');
            
            // Set the user ID and email into the modal form fields
            document.getElementById('email-update-user-id').value = userId;
            document.getElementById('updateEmail').value = email;
        });
    });

    // For opening the wallet update modal
    const walletModalTriggers = document.querySelectorAll('.open-wallet-model');
    walletModalTriggers.forEach(function (trigger) {
        trigger.addEventListener('click', function () {
            const userId = trigger.getAttribute('data-user-id');
            const walletAddress = trigger.getAttribute('data-wallet-address');
            
            // Set the user ID and wallet address into the modal form fields
            document.getElementById('wallet-user-id').value = userId;
            document.getElementById('updateWallet').value = walletAddress;
        });
    });

    // For opening the subscription modal (subscribe or pause subscription)
    const subscribeModalTriggers = document.querySelectorAll('.open-subscribe-model');
    subscribeModalTriggers.forEach(function (trigger) {
        trigger.addEventListener('click', function () {
            const userId = trigger.getAttribute('data-user-id');
            const currentPlan = trigger.getAttribute('data-current-plan');
            
            // Set the user ID and current subscription plan into the modal form fields
            document.getElementById('sub-user-id').value = userId;
            document.getElementById('currentPlan').value = currentPlan;
        });
    });

    // For opening the password update modal
    const passwordModalTriggers = document.querySelectorAll('.open-password-model');
    passwordModalTriggers.forEach(function (trigger) {
        trigger.addEventListener('click', function () {
            const userId = trigger.getAttribute('data-user-id');
            
            // Set the user ID into the modal form field
            document.getElementById('password-update-user-id').value = userId;
        });
    });

    // For opening the user delete modal
    const deleteModalTriggers = document.querySelectorAll('.open-delete-model');
    deleteModalTriggers.forEach(function (trigger) {
        trigger.addEventListener('click', function () {
            const userId = trigger.getAttribute('data-user-id');
            const username = trigger.getAttribute('data-username');
            
            // Set the user ID and username into the modal form fields
            document.getElementById('delete-user-id').value = userId;
            document.getElementById('delete-username').value = username;
        });
    });


        
    //You can add additional modal opening logic for more modals if needed.
});





</script>



<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    //function to lock a user with the user-id
    const lockUser = (user_id) => {
        $.ajax({
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php',
            type: 'POST',
            data: { user_id, action: 'lock' },
            dataType: 'json',
            success: function(response) {
                $('#key-' + user_id).text('locked');
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,  // Prevent closing on outside click
                    allowEscapeKey: false      // Prevent closing on escape key
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);  // Reload after confirmation
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    }

    //function to unlock a user with the user-id
    const unlockUser = (user_id) => {
        $.ajax({
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php',
            type: 'POST',
            data: { user_id, action: 'unlock' },
            dataType: 'json',
            success: function(response) {
                $('#key-' + user_id).text('active');
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
                console.log(response);
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    }

    //function to add days to the subscription of a user
    $('#submitSubscribeForm').on('click', function(e) {
        e.preventDefault();
        let formData = $('#subscribeForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=subscribe',
            data: formData,
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    });

    //function to add days to the subscription of a user
    $('#submitForm').on('click', function(e) {
        e.preventDefault();
        let formData = $('#addDaysForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=addDays',
            data: formData,
            success: function(response) {
                $('#walletInput').val('');
                Swal.fire({
                    title: 'Success!',
                    text: 'Operation successful',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    });

    //function to add days to the subscription of a user
    $('#submitWalletForm').on('click', function(e) {
        e.preventDefault();
        let formData = $('#walletUpdateForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=fund',
            data: formData,
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Operation successful',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    });

    //function to add days to the subscription of all users
    $('#submitFormToAll').on('click', function(e) {
        e.preventDefault();
        let formData = $('#addDaysToAllForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=addDaysToAll',
            data: formData,
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Operation successful',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    });

    //function to update the email of a user
    $('#emailUpdateForm').on('submit', function(e) {
        e.preventDefault();
        let formData = $(this).serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=updateEmail',
            data: formData,
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    });

    //function to cancel the subscription of a user
    const cancelAllSubsForUser = (user_id) => {
        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=cancelSubscription',
            data: { user_id },
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Subscription Cancelled. Please reload the page to see the effect',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    }

    //function to reset the subscription of a user
    const resetSubscription = (user_id) => {
        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=resetSubscription',
            data: { user_id },
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Subscription reset successful.',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    }

    //function to reset the password of a user and mail it to them
    const resetPassword = (user_id) => {
        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=resetPassword',
            data: { user_id },
            success: function(response) {
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred: ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false
                }).then(() => {
                    
                    window.history.replaceState({}, document.title, window.location.pathname);

                    window.location.reload(true);
                });
            }
        });
    }
</script>




</body>
</html>