<?php

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: ../logout.php");  // Redirects to Google
    exit;
}


require 'db.php'; // Your database connection script



function deleteUserData() {
    global $pdo;

    $id = $_POST['id'];

    // Prepare the SQL query to set status to inactive
    $sql = "UPDATE user_data SET status = 'inactive' WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id, PDO::PARAM_INT);

    // Execute the query
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'User status updated to inactive']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update user status']);
    }
}



function restoreUserData () {
    global $pdo;

    $id = $_POST['id'];

    // Prepare the SQL query to set status to inactive
    $sql = "UPDATE user_data SET status = 'active' WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id, PDO::PARAM_INT);

    // Execute the query
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'User status updated to inactive']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update user status']);
    }
}

if (isset($_GET['action']) && $_GET['action'] == 'delete') {
    deleteUserData();
} else if ( isset($_GET['action']) && $_GET['action'] == 'restore' ) {
    restoreUserData();
} else if ( isset($_GET['action']) && $_GET['action'] == 'restore' ) {
    
}