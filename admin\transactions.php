<?php
require '../assets/admin_header.php';
require '../php_files/db.php';

global $pdo; // Your PDO connection
$stmt = $pdo->prepare("SELECT * FROM wallet_transactions ORDER BY created_at ASC");
$stmt->execute();
$transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// var_dump( $transactions );




?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 overflow-y-scroll">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5"> Wallet Transactions </h1>
    </div>
    <div class="table-responsive">
    <table class="table table-striped table-sm h-full">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">Transaction Type</th>
                <th scope="col">Amount</th>
                <th scope="col">Transaction ID</th>
                <th scope="col">Type</th>
                <th scope="col">Status</th>
                <th scope="col"></th>
            </tr>
            </thead>
            <tbody>
            <?php
            if ( $transactions ) {
                foreach ($transactions as $transaction) {
                    echo '
                        <tr>
                            <td>' . $transaction['id'] . '</td>
                            <td>' . $transaction['crypto_type'] . '</td>
                            <td>' . $transaction['amount'] . '</td>
                            <td>' . $transaction['transaction_id'] . '</td>
                            <td>' . $transaction['type'] . '</td>
                            <td>' . $transaction['status'] . '</td>';
                            
                    // Check if the crypto type is 'Transfer' and the type is 'debit'
                    if ($transaction['crypto_type'] == 'Transfer' && $transaction['type'] == 'debit') {
                        echo '
                            <td>
                                <div class="btn-group">
                                    <button class="btn btn-secondary bg-transparent border-0 btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <button id="key-' . $transaction['id'] . '" onclick="reverseTransaction(\'' . $transaction['transaction_id'] . '\')" class="dropdown-item" type="button">Revert</button>
                                        </li>
                                    </ul>
                                </div>
                            </td>';
                    } else {
                        echo '<td></td>'; // If not, provide an empty cell
                    }
                
                    echo '</tr>'; // Close the table row
                }
                
            } else {
                echo ' No transaction has been made.';
            }
            ?>

            </tbody>
        </table>
    </div>
</main>
</div>
</div>

<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="../js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

        function reverseTransaction(transactionId) {
            if (confirm('Are you sure you want to reverse this transaction?')) {
                $.ajax({
                    url: '<?= BASE_URL?>/php_files/admin/reverse_transaction.php', // The PHP script that handles the request
                    type: 'POST',
                    data: { transaction_id: transactionId }, // Send the transaction ID
                    success: function(response) {
                        const data = JSON.parse(response); // Parse the JSON response
                        if (data.success) {
                            alert('Transaction reversed successfully!');
                            location.reload(); // Reload the page to reflect changes
                        } else {
                            alert('Error: ' + data.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('AJAX error: ' + error);
                    }
                });
            }
        }
</script>
</body>
</html>