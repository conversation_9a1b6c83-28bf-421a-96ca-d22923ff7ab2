<?php


// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    header("Location: ../logout.php");  // Redirects to Google
    exit;
}



require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');




global $pdo;






    $stmt = $pdo->query("SELECT * FROM theme_images WHERE isApproved = TRUE");
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    
 

    
?>

<?php
require('assets/header.php')
?>
<style>
    .gallery img {
        width: 100px;
        height: 100px;
        cursor: pointer;
        margin: 5px;
        object-fit: cover;
    }
    .selected {
        border: 2px solid red;
    }
</style>
<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <h2>Select Your Background Image</h2>
    <div class="gallery">
        <?php
        if ( $images ) {
            foreach ($images as $image) {
                echo '
                            <img src="' . ($image['imageUrl']) . '" class="card-img-top" alt="Image 1" data-image="' . ($image['imageUrl']) . '" alt="' .
                htmlspecialchars($image['altText']) . '">
                    ';
            }
        } else {
            echo "No theme image added yet";
        }
        ?>
    </div>
    <button class="btn btn-success" id="saveButton">Apply Background</button>

    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h4">Upload Cookies Page Background Image</h1>
    </div>
    <div>
        <form class="row g-3" id="themeForm" method="post" enctype="multipart/form-data">
            <div class="col-md-6">
                <label for="inputCity" class="form-label">Theme Picture</label>
                <input type="file" name="profile_picture" accept="image/*" class="form-control" id="inputPicture" required>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary"> Upload New image </button>
            </div>
        </form>
    </div>
</main>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script>
    let selectedImage = '';

    // Handle image selection
    $('.gallery img').on('click', function() {
        $('.gallery img').removeClass('selected');
        $(this).addClass('selected');
        selectedImage = $(this).data('image');
    });

    // Handle save button click for selecting an existing image
    $('#saveButton').on('click', function() {
        if (selectedImage) {
            $.ajax({
                url: '<?= BASE_URL ?>/php_files/save_image.php?action=add',
                type: 'POST',
                data: { image: selectedImage },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: response.error || 'An unexpected error occurred. Please try again.',
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Error: ' + textStatus + ' ' + errorThrown,
                    });
                }
            });
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'No image selected',
                text: 'Please select an image first.',
            });
        }
    });

    // Handle form submission for uploading a new image
    $(document).ready(function() {
        $('#themeForm').on('submit', function(e) {
            e.preventDefault(); // Prevent the default form submission

            var formData = new FormData(this); // Create a FormData object

            $.ajax({
                url: '<?= BASE_URL ?>/php_files/save_image.php?action=update',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === '200') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: response.error,
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Error: ' + textStatus + ' ' + errorThrown,
                    });
                }
            });
        });
    });
</script>


</body>
</html>