<?php
require('../assets/admin_authorize.php');



// Allow CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Auto-create the table if it doesn't exist, adding the user_id column
    $table_sql = "CREATE TABLE IF NOT EXISTS cloudflare_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,  
        main_email VARCHAR(255) NOT NULL,
        main_token VARCHAR(255) NOT NULL,
        addon_email VARCHAR(255) NOT NULL,
        addon_token VARCHAR(255) NOT NULL,
        redirect_email VARCHAR(255) NOT NULL,
        redirect_token VARCHAR(255) NOT NULL,
        attach_email VARCHAR(255) NOT NULL,
        attach_token VARCHAR(255) NOT NULL,
        username VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    $pdo->exec($table_sql);

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $user_id = "null";
        $main_email = $_POST['main_email'];
        $main_token = $_POST['main_token'];
        $addon_email = $_POST['addon_email'];
        $addon_token = $_POST['addon_token'];
        $redirect_email = $_POST['redirect_email'];
        $redirect_token = $_POST['redirect_token'];
        $attach_email = $_POST['attach_email'];
        $attach_token = $_POST['attach_token'];
        $username = $_POST['username'];

        // Check for existing data (emails, tokens, and username)
        $check_stmt = $pdo->prepare("SELECT main_email, main_token, addon_email, addon_token, redirect_email, redirect_token, attach_email, attach_token, username 
                                      FROM cloudflare_data 
                                      WHERE main_email = ? OR main_token = ? 
                                      OR addon_email = ? OR addon_token = ? 
                                      OR redirect_email = ? OR redirect_token = ? 
                                      OR attach_email = ? OR attach_token = ? 
                                      OR username = ?");
        $check_stmt->execute([$main_email, $main_token, $addon_email, $addon_token, $redirect_email, $redirect_token, $attach_email, $attach_token, $username]);
        $existing_data = $check_stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($existing_data) {
            $existing_fields = [];
            foreach ($existing_data as $row) {
                if ($row['main_email'] === $main_email) {
                    $existing_fields[] = "main_email: $main_email";
                }
                if ($row['main_token'] === $main_token) {
                    $existing_fields[] = "main_token: $main_token";
                }
                if ($row['addon_email'] === $addon_email) {
                    $existing_fields[] = "addon_email: $addon_email";
                }
                if ($row['addon_token'] === $addon_token) {
                    $existing_fields[] = "addon_token: $addon_token";
                }
                if ($row['redirect_email'] === $redirect_email) {
                    $existing_fields[] = "redirect_email: $redirect_email";
                }
                if ($row['redirect_token'] === $redirect_token) {
                    $existing_fields[] = "redirect_token: $redirect_token";
                }
                if ($row['attach_email'] === $attach_email) {
                    $existing_fields[] = "attach_email: $attach_email";
                }
                if ($row['attach_token'] === $attach_token) {
                    $existing_fields[] = "attach_token: $attach_token";
                }
                if ($row['username'] === $username) {
                    $existing_fields[] = "username: $username";
                }
            }
            echo json_encode(["status" => "error", "message" => "The following fields already exist in the database: " . implode(", ", $existing_fields)]);
        } else {
            // Insert data if no conflicts
            $stmt = $pdo->prepare("INSERT INTO cloudflare_data (user_id, main_email, main_token, addon_email, addon_token, redirect_email, redirect_token, attach_email, attach_token, username) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$user_id, $main_email, $main_token, $addon_email, $addon_token, $redirect_email, $redirect_token, $attach_email, $attach_token, $username]);
            echo json_encode(["status" => "success"]);
        }
    }
} catch (PDOException $e) {
    echo json_encode(["status" => "error", "message" => $e->getMessage()]);
}
?>
