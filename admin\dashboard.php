<?php







    require '../assets/admin_header.php';
    require '../php_files/db.php';
    require '../php_files/functions.php';

        try {

            // Query for total users
            $total_users_query = "SELECT COUNT(*) AS total_users FROM user_profiles WHERE user_type = 'regular'";
            $stmt = $pdo->prepare($total_users_query);
            $stmt->execute();
            $total_users = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];

            // Query for subscribed users
            $subscribed_users_query = "SELECT COUNT(*) AS subscribed_users FROM user_profiles WHERE subscribed > 0";
            $stmt = $pdo->prepare($subscribed_users_query);
            $stmt->execute();
            $subscribed_users = $stmt->fetch(PDO::FETCH_ASSOC)['subscribed_users'];

            // Query for unsubscribed users
            $unsubscribed_users_query = "SELECT COUNT(*) AS unsubscribed_users FROM user_profiles WHERE subscribed = 0 AND user_type = 'regular'";
            $stmt = $pdo->prepare($unsubscribed_users_query);
            $stmt->execute();
            $unsubscribed_users = $stmt->fetch(PDO::FETCH_ASSOC)['unsubscribed_users'];

            // Query to fetch all users
            $users_query = "SELECT * FROM user_profiles";
            $stmt = $pdo->prepare($users_query);
            $stmt->execute();
            $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $subscription_plans_query = "SELECT 
            subscription_plans.*,
            CASE 
                WHEN DATE_ADD(subscription_plans.created_at, INTERVAL subscription_plans.duration_days DAY) < NOW() THEN 'expired'
                ELSE 'active'
            END AS subscription_status
            FROM 
                subscription_plans
            ORDER BY 
            subscription_status ASC, 
            subscription_plans.created_at DESC";
            $stmt = $pdo->prepare($subscription_plans_query);
            $stmt->execute();
            $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            /*print "<pre>";
            print_r($subscriptions);
            print "</pre>";*/


        } catch (PDOException $e) {
            echo 'Connection failed: ' . $e->getMessage();
        }

        
?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h4">Dashboard</h1>
                    <button type="button" data-bs-toggle="modal" data-bs-target="#trackUrl" class="btn my-1 btn-primary btn-sm" >
                        Add Track URLs
                    </button>
                    <button type="button" class="btn my-1 btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#baseUrl">
                        Add Base URLs
                    </button>
                    <button type="button" class="btn my-1 btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#openRedirectUrl">
                        Add Open Redirect URLs
                    </button>
            </div>
            <div class="block row">
                    <div class="card col-6 col-sm-3 text-center">
                        <div class="card-header fs-6">
                            Total Users
                        </div>
                        <div class="card-body ">
                            <h5 class="card-title"><?php echo $total_users; ?></h5>
                        </div>
                    </div>
                    <div class="card col-6 col-sm-3 text-center">
                        <div class="card-header fs-6">
                            Subscribed Users
                        </div>
                        <div class="card-body ">
                            <h5 class="card-title"><?php echo $subscribed_users; ?></h5>
                        </div>
                    </div>
                    <div class="card col-6 col-sm-3 text-center">
                        <div class="card-header fs-6">
                            Unsubscribed Users
                        </div>
                        <div class="card-body ">
                            <h5 class="card-title"><?php echo $unsubscribed_users; ?></h5>
                        </div>
                    </div>
                </div>

           
            <h5 class="mt-2">All Subscriptions Plans</h5>
            <div class="table-responsive small">
                <table class="table table-striped table-sm">
                    <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Name</th>
                        <th scope="col">Duration Days</th>
                        <th scope="col">Description</th>
                        <th scope="col">Status</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    if ( $subscriptions ) {
                        foreach ( $subscriptions as $subscription ) {
                            echo '
                                <tr>
                                    <td> '. $subscription['id'] .' </td>
                                    <td> '. $subscription['plan_name'] .' </td>
                                    <td> '. $subscription['duration_days'] .' </td>
                                    <td> '. $subscription['description'] .' </td>
                                    <td> '. $subscription['subscription_status'] .' </td>
                                </tr>
                            ';
                        }
                    } else {
                        echo ' No user has been created. Click <a href="create_user.php">here</a> to create a user ';
                    }
                    ?>

                    </tbody>
                </table>
            </div>
        </main>
    </div>
</div>


        <!-- Modal for adding track urls -->
        <div class="modal fade" id="trackUrl" aria-hidden="true"
    aria-labelledby="exampleModalToggleLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <form id="trackUrlForm" class="w-100">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add Track URLs </h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="domain-inputs">
                        <!-- The first input will show the clicked domain -->
                        <div class="mb-3 input-group">
                            <input type="text" required class="form-control domain-input"
                                id="updateDomain" name="domains[]"
                                placeholder="Enter track url">
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary" id="trackBtn">Add More Track URLs</button>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" type="submit" id="submitFormToAll">Update</button>
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>



        <!-- Modal for adding base urls -->
        <div class="modal fade" id="baseUrl" aria-hidden="true"
    aria-labelledby="exampleModalToggleLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <form id="baseUrlForm" class="w-100">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add Base URLs </h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="domain">
                        <!-- The first input will show the clicked domain -->
                        <div class="mb-3 input-group">
                            <input type="text" required class="form-control domain"
                                id="updateDomain" name="domains[]"
                                placeholder="Enter base url">
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary" id="baseBtn">Add More Base URLs</button>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="submitFormToAll">Update</button>
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>


        <!-- Modal for adding open redirect urls -->
        <div class="modal fade" id="openRedirectUrl" aria-hidden="true"
    aria-labelledby="exampleModalToggleLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <form id="openUrlForm" class="w-100">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add Open Redirect URLs </h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="domain-open-urls">
                        <!-- The first input will show the clicked domain -->
                        <div class="mb-3 input-group">
                            <input type="text" required class="form-control domain-open-url"
                                id="updateDomain" name="domains[]"
                                placeholder="Enter base url">
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary" id="openBtn">Add More Open Redirect URLs</button>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="submitFormToAll">Update</button>
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>



<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>

     // Function to show domain in the modal when clicked
function showTrackModal() {
    console.log( "hi" )
    function showTrackModal() {
    $('#domain-inputs').html(`
        <div class="mb-3">
            <input type="text" required class="form-control domain-input" 
                   id="updateDomain" name="trackUrls[]" placeholder="Enter track url">
        </div>
    `); 
}
}

$('#trackBtn').on('click', function() {
    // Create a new input group with a remove button
    const inputGroup = $('<div class="mb-3 input-group"></div>');

    const inputField = $('<input type="text" class="form-control domain-input" name="trackUrls[]" placeholder="Enter domain" required>');

    // Add the remove button
    const removeButton = $('<button type="button" class="btn btn-danger ms-2">Remove</button>');

    // Append the input field and remove button to the input group
    inputGroup.append(inputField).append(removeButton);

    // Append the new input group to the container
    $('#domain-inputs').append(inputGroup);

    // Remove the input group when clicking the remove button
    removeButton.on('click', function() {
        inputGroup.remove();
    });
});


// Update your approveDomain function to handle multiple domains
$('#trackUrlForm').on('submit', function(e) {
    e.preventDefault();

    var domains = [];
    $('.domain-input').each(function() {
        domains.push($(this).val()); // Collect all domain inputs
    });

    console.log( domains )

    $.ajax({
        url: '../php_files/admin/urls.php?action=trackUrl',
        type: 'POST',
        data: {
            domains: domains,
        },
        success: function(result) {
            alert(result.message);
            if (result.message.includes("successfully")) {
                location.reload();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            alert('An error occurred while updating the domain.');
        }
    });
});




$('#baseBtn').on('click', function() {
    // Create a new input group with a remove button
    const inputGroup = $('<div class="mb-3 input-group"></div>');

    const inputField = $('<input type="text" class="form-control domain" name="baseUrls[]" placeholder="Enter domain" required>');

    // Add the remove button
    const removeButton = $('<button type="button" class="btn btn-danger ms-2">Remove</button>');

    // Append the input field and remove button to the input group
    inputGroup.append(inputField).append(removeButton);

    // Append the new input group to the container
    $('#domain').append(inputGroup);

    // Remove the input group when clicking the remove button
    removeButton.on('click', function() {
        inputGroup.remove();
    });
});


// Update your approveDomain function to handle multiple domains
$('#baseUrlForm').on('submit', function(e) {
    e.preventDefault();

    var domains = [];
    $('.domain').each(function() {
        domains.push($(this).val()); // Collect all domain inputs
    });

    console.log( domains )

    $.ajax({
        url: '../php_files/admin/urls.php?action=baseUrl',
        type: 'POST',
        data: {
            domains: domains,
        },
        success: function(result) {
            alert(result.message);
            if (result.message.includes("successfully")) {
                location.reload();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            alert('An error occurred while updating the domain.');
        }
    });
});




$('#openBtn').on('click', function() {
    // Create a new input group with a remove button
    const inputGroup = $('<div class="mb-3 input-group"></div>');

    const inputField = $('<input type="text" class="form-control domain-open-url" name="openUrls[]" placeholder="Enter domain" required>');

    // Add the remove button
    const removeButton = $('<button type="button" class="btn btn-danger ms-2">Remove</button>');

    // Append the input field and remove button to the input group
    inputGroup.append(inputField).append(removeButton);

    // Append the new input group to the container
    $('#domain-open-urls').append(inputGroup);

    // Remove the input group when clicking the remove button
    removeButton.on('click', function() {
        inputGroup.remove();
    });
});


// Update your approveDomain function to handle multiple domains
$('#openUrlForm').on('submit', function(e) {
    e.preventDefault();

    var domains = [];
    $('.domain-open-url').each(function() {
        domains.push($(this).val()); // Collect all domain inputs
    });

    console.log( domains )

    $.ajax({
        url: '../php_files/admin/urls.php?action=openUrl',
        type: 'POST',
        data: {
            domains: domains,
        },
        success: function(result) {
            alert(result.message);
            if (result.message.includes("successfully")) {
                location.reload();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            alert('An error occurred while updating the domain.');
        }
    });
});


</script>
</body>
</html>