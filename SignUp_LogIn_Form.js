const container = document.querySelector('.container');
const registerBtn = document.querySelector('.register-btn');
const loginBtn = document.querySelector('.login-btn');

registerBtn.addEventListener('click', () => {
    container.classList.add('active');
})

loginBtn.addEventListener('click', () => {
    container.classList.remove('active');
})


 function fetchQuote() {
            fetch('signinqoute.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('quote-box').innerHTML = data;
                    document.querySelector('.quote-text').style.opacity = 1;
                    document.querySelector('.quote-text').style.transform = 'translateY(0)';
                    document.querySelector('.quote-author').style.opacity = 1;
                    document.querySelector('.quote-author').style.transform = 'translateY(0)';
                });
        }

        
        fetchQuote();

       
        setInterval(fetchQuote, 10000);
        
        