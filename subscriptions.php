<?php
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');

$stmt = $pdo->prepare("
    SELECT 
        subscription_plans.id AS plan_id, 
        subscription_plans.plan_name, 
        subscription_plans.price, 
        subscription_plans.duration_days,
        subscription_plans.description,
        user_subscriptions.user_id,
        user_subscriptions.subscription_end_date
    FROM 
        subscription_plans
    LEFT JOIN 
        user_subscriptions
    ON 
        subscription_plans.id = user_subscriptions.plan_id 
        AND user_subscriptions.user_id = :user_id
        AND user_subscriptions.subscription_end_date > NOW()
    GROUP BY 
        subscription_plans.id, 
        subscription_plans.plan_name, 
        subscription_plans.price, 
        subscription_plans.duration_days, 
        subscription_plans.description, 
        user_subscriptions.user_id,
        user_subscriptions.subscription_end_date;
");

$stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
$stmt->execute();

// Fetch all plans with subscription status
$plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
// echo '<pre>';
// echo json_encode($plans, JSON_PRETTY_PRINT);
// echo '</pre>';

?>
<?php require('assets/header.php') ?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5">Subscription Plans</h1>
    </div>
    <div class="row row-cols-1 row-cols-md-3 mb-3 text-center">
        <?php foreach ($plans as $plan): 
        
        
        $price_per_day = $plan['price'] / $plan['duration_days'];
        
        
        ?>
            <div class="col">
                <div class="card mb-4 rounded-3 shadow-sm">
                    <div class="card-header py-3">
                        <h4 class="my-0 fw-normal"><?php echo htmlspecialchars($plan['plan_name']); ?> $<?php echo number_format($price_per_day, 2); ?> per day</h4>
                    </div>
                    <div class="card-body">
                        <h1 class="card-title pricing-card-title">$<?php echo htmlspecialchars($plan['price']); ?></h1>
                        <h1 class="card-title pricing-card-title">
                            <?php echo htmlspecialchars($plan['duration_days']); ?>
                            <small class="text-body-secondary fw-light">/days</small>
                        </h1>
                        <ul class="list-unstyled mt-3 mb-4">
                            <li><?php echo htmlspecialchars($plan['description']); ?></li>
                        </ul>

                        <!-- Check if the user is subscribed and the subscription is still active -->
                        <?php if ($plan['user_id'] && $plan['subscription_end_date'] > date('Y-m-d')): ?>
                            <button disabled type="button" class="w-100 btn btn-lg btn-success">Subscribed</button>
                        <?php else: ?>
                            <button type="button" class="w-100 btn btn-lg btn-primary"
                                    onclick="subscribeToPlan(<?php echo $plan['plan_id'] ?>)"
                            >Subscribe</button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</main>
</div>
</div>
<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    const subscribeToPlan = (id) => {
        $.ajax({
            url: '<?= BASE_URL?>/php_files/subscribe.php',
            type: 'POST',
            data: { plan_id: id },
            dataType: 'json',
            success: function(response) {
                // Use SweetAlert to display the message
                Swal.fire({
                    icon: response.status === 'success' ? 'success' : 'error',  // Icon based on response status
                    title: response.status === 'success' ? 'Success' : 'Subscription Conflict',  // Title based on status
                    text: response.message,  // Message from the server
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Reload the page after SweetAlert is closed
                    if (response.status === 'success') {
                        location.reload();
                    }
                });
            },
            error: function(xhr, status, error) {
                // Show SweetAlert on error
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'An error occurred: ' + error,
                    confirmButtonText: 'OK'
                });
                //console.error('AJAX Error:', status, error);
            }
        });
    }
</script>



<script>
    
// Check if any button has the "Subscribed" text (we don't use btn-primary now)
        const alreadySubscribedButton = document.querySelector('button:contains("Subscribed")');

        // If there is a "Subscribed" button, disable all buttons
        if (alreadySubscribedButton) {
            // Disable all buttons
            const allButtons = document.querySelectorAll('button[type="button"]');
            allButtons.forEach(btn => {
                btn.disabled = true; // Disable the button
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary'); // Optional: Change button style to indicate it's disabled
                btn.innerText = 'Already Subscribed'; // Optional: Update button text
            });

            // Show a SweetAlert message
            Swal.fire({
                title: 'You have already subscribed!',
                text: 'You cannot subscribe to another plan until your current subscription ends.',
                icon: 'info',
                confirmButtonText: 'OK'
            });
            return; // Prevent the AJAX request or further action
        }

</script>

</body>
</html>
