<?php
require '../db.php';
require '../functions.php';


function resetPassword ( $userId ) {
    global $pdo;
    if (empty($userId)) {
        echo json_encode(['status' => 'error', 'message' => 'User ID is required']);
        exit;
    }

    $newPassword = generateRandomPassword();
    $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);

    try {
        // Update password in database
        $stmt = $pdo->prepare("UPDATE user_profiles SET password = ? WHERE user_id = ?");
        $stmt->execute([$hashedPassword, $userId]);

        if ($stmt->rowCount() > 0) {
            // Fetch user email
            $stmt = $pdo->prepare("SELECT email FROM user_profiles WHERE user_id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                $to = $user['email'];
                $subject = 'Password Reset Request';
                $message =  "
    <head>
       
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
            .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }
            .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }
            .email-header img { width: 30px; }
            .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }
            h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }
            .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }
            .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }
            .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }
        </style>
    </head>
    <body>
        <div class='email-container'>
            <div class='email-header'>
                <img src='https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png' alt='Log in'>
            </div>
            <div class='email-content'>
                <h1>Auto-Generated Password Notification</h1>
                
                <div class='details'>
                <p>Your password has been automatically reset by your RaccoonO365 Panel system successfully. A new password has been automatically generated by your panel.</p>
                    <p><strong>New System-Generated Password: </strong><br>$newPassword</p>
                     
                     
                    <p> If this was you, no further action is needed.</p>
                </div>
            </div>
        </div>
    </body>
</html>
               ";
   

                // Send email using the function from functions.php
                if (sendMail($to, $subject , $message , 'system_mail')) {
                    return ['status' => 'success', 'message' => 'Password reset successfully. New password sent to user email.'];
                } else {
                    return ['status' => 'error', 'message' => 'Failed to send email'];
                }
            } else {
                return ['status' => 'error', 'message' => 'User not found'];
            }
        } else {
            return ['status' => 'error', 'message' => 'Failed to update password'];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
}


// Lock user account
function lockUser($userId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE user_profiles SET status = 'locked' WHERE user_id = :id");
        $stmt->execute(['id' => $userId]);

        if ($stmt->rowCount() > 0) {
            return ['status' => 'success', 'message' => 'User account locked successfully'];
        } else {
            return ['status' => 'failure', 'message' => 'Failed to lock user account. User may not exist.'];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Unlock user account
function unlockUser($userId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE user_profiles SET status = 'active' WHERE user_id = :id");
        $stmt->execute(['id' => $userId]);

        if ($stmt->rowCount() > 0) {
            return ['status' => 'success', 'message' => 'User account unlocked successfully'];
        } else {
            return ['status' => 'failure', 'message' => 'Failed to unlock user account. User may not exist.'];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
}


function updateEmail () {
    global $pdo;
    $userId = intval($_POST['user-id']);
    $newEmail = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

    if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
        return ['status' => 'error', 'message' => 'Invalid email format'];
    } else {
        // Update the email in the database
        $stmt = $pdo->prepare("UPDATE user_profiles SET email = ? WHERE user_id = ?");
        $stmt->execute([$newEmail, $userId]);

        if ($stmt->rowCount() > 0) {
           return ['status' => 'success', 'message' => 'Email updated successfully'];
        } else {
            return ['status' => 'error', 'message' => 'No changes made'];
        }
    }
}


function fundUserWallet() {
    global $pdo;
    $userId = $_POST['user-id'];
    $amount = $_POST['amount'];
    $transactionId = uniqid('txn_');

    $pdo->beginTransaction();

    try {
        // Check if the wallet exists for the user
        $stmt = $pdo->prepare("SELECT * FROM wallet WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $userId]);
        $wallet = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$wallet) {
            // Create wallet if it doesn't exist
            $stmt = $pdo->prepare("INSERT INTO wallet (user_id, balance, created_at, updated_at) 
                VALUES (:user_id, 0, NOW(), NOW())");
            $stmt->execute(['user_id' => $userId]);
        }

        // Update the user's wallet balance
        $sql = "UPDATE wallet SET balance = balance + :amount WHERE user_id = :user_id";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'amount' => $amount,
            'user_id' => $userId
        ]);

        // Check if the wallet balance was updated
        if ($stmt->rowCount() > 0) {
            // Insert a new record into the wallet_transactions table
            $sql = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
                VALUES (:user_id, :crypto_type, :amount, :transaction_id, 'credit', 'completed')";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                'user_id' => $userId,
                'crypto_type' => 'USDT',
                'amount' => $amount,
                'transaction_id' => $transactionId
            ]);

            // Commit the transaction
            $pdo->commit();

            // Send a notification
            $notify = saveNotification($userId, 'Wallet Funded', 'The admin has funded your wallet with $' . $amount);

            return [
                'status' => 'success',
                'message' => 'Wallet funded successfully.',
                'transaction_id' => $transactionId
            ];
        } else {
            // Rollback if wallet update failed
            $pdo->rollBack();
            return ['status' => 'error', 'message' => 'Failed to fund wallet.'];
        }
    } catch (Exception $e) {
        // Rollback transaction in case of error
        $pdo->rollBack();
        return ['status' => 'error', 'message' => $e->getMessage()];
    }
}


function updateDomain() {
    // var_dump($_POST);
    $new_domains = $_POST['domains']; // Expecting an array of domain names
    $user_id = $_POST['user_id'];

    global $pdo;

    try {
        // Start a transaction to ensure all updates are atomic
        $pdo->beginTransaction();

        // Update the last domain change date in user_profiles table
        $stmt = $pdo->prepare("UPDATE user_profiles SET last_domain_change = NOW() WHERE user_id = :uid");
        $stmt->bindParam(':uid', $user_id);
        $stmt->execute();

        // Delete any existing profile links for the user
        $stmt = $pdo->prepare("DELETE FROM user_profile_links WHERE user_id = :uid");
        $stmt->bindParam(':uid', $user_id);
        $stmt->execute();

        // Insert new domain links into the user_profile_links table
        $stmt = $pdo->prepare("INSERT INTO user_profile_links (user_id, profile_link) VALUES (:uid, :profile_link)");

        foreach ($new_domains as $domain) {
            $stmt->bindParam(':uid', $user_id);
            $stmt->bindParam(':profile_link', $domain);
            $stmt->execute();
        }

        // Commit the transaction
        $pdo->commit();

        // Fetch user information for notifications
        $stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE user_id = :uid");
        $stmt->bindParam(':uid', $user_id);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        $message = 'Hi ' . $user['username'] . ', Link no more showing google red screen in broswers.';

        // Save notification and send email
        saveNotification($user_id, "Red Screen issue fixed", "Your domain is no more showing red screen in broswers.");
        sendMail($user['email'], "Red screen issue fixed", $message, 'system_mail');

        // Delete the user's domain request
        $stmt = $pdo->prepare("DELETE FROM domain_requests WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return ['message' => 'The user has been notified that hes link is no more showing google red screen in broswers.'];
    } catch (PDOException $e) {
        // Roll back the transaction on error
        $pdo->rollBack();
        return ['message' => 'Error updating domains: ' . $e->getMessage()];
    } catch (Exception $e) {
        // Roll back the transaction on unexpected errors
        $pdo->rollBack();
        return ['message' => 'An unexpected error occurred: ' . $e->getMessage()];
    }
}


function updateUserAntiBotSetting() {
    global $pdo;
    $user_id = $_GET['user_id'];
    $type = $_GET['type'];
    $value = $_GET['value'];

    // Check if the setting exists for the given user and type
    $checkQuery = "
        SELECT uas.id 
        FROM user_antibot_settings uas
        JOIN antibottoggle_states ats ON uas.antibot_id = ats.id
        WHERE uas.user_id = :user_id AND ats.type = :type
    ";
    $stmt = $pdo->prepare($checkQuery);
    $stmt->execute(['user_id' => $user_id, 'type' => $type]);
    $setting = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($setting) {
        // Update existing record for this user and setting type
        $updateQuery = "
            UPDATE user_antibot_settings AS uas
            JOIN antibottoggle_states AS ats ON uas.antibot_id = ats.id
            SET uas.state = :state
            WHERE uas.user_id = :user_id AND ats.type = :type
        ";
        $stmt = $pdo->prepare($updateQuery);
        $stmt->execute(['state' => $value, 'user_id' => $user_id, 'type' => $type]);
    } else {
        // Insert a new record for this user and setting type
        $insertAntibotQuery = "
            SELECT id FROM antibottoggle_states WHERE type = :type
        ";
        $stmt = $pdo->prepare($insertAntibotQuery);
        $stmt->execute(['type' => $type]);
        $antibot = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($antibot) {
            $insertQuery = "
                INSERT INTO user_antibot_settings (user_id, antibot_id, state)
                VALUES (:user_id, :antibot_id, :state)
            ";
            $stmt = $pdo->prepare($insertQuery);
            $stmt->execute(['user_id' => $user_id, 'antibot_id' => $antibot['id'], 'state' => $value]);
        } else {
            return [ 'error' => 'error' , 'message' => 'Antibot setting type not found'];
        }
    }

    return [ 'status' => 'success' , 'user_id' => $user_id, 'type' => $type, 'value' => $value];
}





// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

// Handle lock/unlock requests
if (isset($_POST['action'])) {
    if ($_POST['action'] === 'lock') {
        if (isset($_POST['user_id'])) {
            $response = lockUser($_POST['user_id']);
        } else {
            $response = ['status' => 'error', 'message' => 'User ID is required'];
        }
    } elseif ($_POST['action'] === 'unlock') {
        if (isset($_POST['user_id'])) {
            $response = unlockUser($_POST['user_id']);
        } else {
            $response = ['status' => 'error', 'message' => 'User ID is required'];
        }
    }
}
// Handle password reset requests
if (isset($_GET['action']) && $_GET['action'] === 'resetPassword') {
        $response = resetPassword($_POST['user_id']);
    } elseif (isset($_GET['action']) && $_GET['action'] === 'updateEmail') {
        $response = updateEmail();
} elseif ( isset($_GET['action']) && $_GET['action'] == 'fund' ) {
        $response = fundUserWallet();
} else if ( isset($_GET['action']) && $_GET['action'] === 'updateDomain' ) {
    $response = updateDomain();
} else if ( isset($_GET['action']) && $_GET['action'] === 'updateAntiBotSetting' ) {
    $response = updateUserAntiBotSetting();
}

// Send a JSON response
header('Content-Type: application/json');
echo json_encode($response);
