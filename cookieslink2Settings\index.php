<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}



// Include the content of domaintracker again
include('domaintracker.php');  // or use require('domaintracker');


// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

$userId = $_SESSION['user_id']; // Get the logged-in user's ID

// List of subdomain prefixes
$subdomains = [
    'docs', 'view', 'files', 'portal', 'share', 'access', 'storage', 'upload',
    'download', 'manage', 'preview', 'resource', 'archive', 'docs-view', 'doc-center',
    'file-view', 'docs-storage', 'content', 'collaborate', 'paperless', 'cloud-docs',
    'doc-hub', 'secure-docs', 'docs-access', 'e-docs', 'web-docs', 'docs-portal',
    'docs-upload', 'online-docs', 'shared-docs', 'docs-center', 'manage-docs',
    'docs-library', 'docs-host', 'docs-site', 'docs-repository', 'docs-system',
    'docs-zone', 'docs-storage', 'doc-viewer', 'docs-track', 'doc-link', 'file-share',
    'docs-viewer', 'document-view', 'docs-dashboard', 'docs-archive', 'docs-folder',
    'docs-console', 'docs-app', 'doc-secure', 'docs-manager', 'docs-folder',
    'docs-systems', 'docs-organization', 'docs-tools', 'docs-center', 'docs-system',
    'docs-cloud', 'docs-explorer', 'docs-download', 'docs-storage', 'docs-file',
    'docs-retrieval', 'docs-directory', 'docs-library', 'docs-research', 'docs-catalog',
    'docs-net', 'docs-project', 'docs-internal', 'docs-sys', 'docs-zone', 'docs-upload',
    'docs-link', 'docs-system', 'docs-tracking', 'docs-console', 'docs-data',
    'docs-storage', 'docs-tool', 'docs-view', 'docs-center', 'docs-host', 'docs-portal',
    'docs-database', 'docs-organizer', 'docs-share', 'docs-repository', 'docs-site',
    'docs-files', 'docs-exchange', 'docs-filehub', 'docs-tracking', 'docs-folder',
    'docs-archive', 'docs-links', 'docs-management', 'docs-viewer', 'docs-system'
];

// Shuffle the subdomains array to randomize
shuffle($subdomains);

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if secondcookieslinkdnsdomain_requests table exists
    $checkTableQuery = "SHOW TABLES LIKE 'secondcookieslinkdnsdomain_requests'";
    $stmt = $pdo->query($checkTableQuery);
    $tableExists = $stmt->fetch(PDO::FETCH_ASSOC);

    // If the table doesn't exist, create it
    if (!$tableExists) {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS secondcookieslinkdnsdomain_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                domain_name VARCHAR(255) NOT NULL,
                status VARCHAR(255) DEFAULT 'Pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                has_updated TINYINT(1) DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
            ) ENGINE=InnoDB;
        ";
        $pdo->exec($createTableQuery);
    }

    // Check if 'created_at' column exists in 'secondcookieslinkdnsdomain_requests'
    if (!columnExists('secondcookieslinkdnsdomain_requests', 'created_at')) {
        $alterTableQuery = "ALTER TABLE secondcookieslinkdnsdomain_requests ADD created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
        $pdo->exec($alterTableQuery);
    }
    
    // Fetch secondcookieslinkdnsdomain_requests for the current user
    $stmt = $pdo->prepare("SELECT * FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    $userRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Check if user has non-empty secondcookieslinkdnsdomain_requests
    if (!empty($userRequests)) {
        include('nameservers.php'); 
    }

    // Check if the table exists for viewpaths
    $tableCheckStmt = $pdo->prepare("SHOW TABLES LIKE 'secondcookieslinkuser_viewpaths'");
    $tableCheckStmt->execute();
    if ($tableCheckStmt->rowCount() == 0) {
        $createTableStmt = $pdo->prepare("
            CREATE TABLE secondcookieslinkuser_viewpaths (
                user_id INT NOT NULL PRIMARY KEY,
                viewpath VARCHAR(255) DEFAULT NULL
            )
        ");
        $createTableStmt->execute();
    }

    // Check if the user's viewpath exists
    $stmt = $pdo->prepare("SELECT viewpath FROM secondcookieslinkuser_viewpaths WHERE user_id = ?");
    $stmt->execute([$userId]);
    $viewpath = $stmt->fetchColumn();

    if (!$viewpath) {
        $random_viewpath = $subdomains[array_rand($subdomains)];
        $insert_stmt = $pdo->prepare("INSERT INTO secondcookieslinkuser_viewpaths (user_id, viewpath) VALUES (?, ?) ON DUPLICATE KEY UPDATE viewpath = VALUES(viewpath)");
        $insert_stmt->execute([$userId, $random_viewpath]);
        $viewpath = $random_viewpath;
    }

} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
    exit();
}

// Function to check if a column exists
function columnExists($table, $column) {
    global $pdo;
    $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE :column");
    $stmt->execute(['column' => $column]);
    return $stmt->rowCount() > 0;
}

if (isset($_POST['connect'])) {
    $domain = htmlspecialchars(trim($_POST['domain']));

    // **Reset `has_updated` for all records of the current user.**
    $stmt = $pdo->prepare("UPDATE secondcookieslinkdnsdomain_requests SET has_updated = 0 WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);

    // Check if the user already has an existing domain request
    $stmt = $pdo->prepare("SELECT * FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    $existingRequest = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existingRequest) {
        // Update the existing domain request
        $updateStmt = $pdo->prepare("UPDATE secondcookieslinkdnsdomain_requests SET domain_name = :domain, has_updated = 0, created_at = CURRENT_TIMESTAMP WHERE user_id = :user_id");
        if ($updateStmt->execute(['domain' => $domain, 'user_id' => $userId])) {
            echo "Domain request updated successfully.";
        } else {
            echo "Failed to update domain request.";
        }
    } else {
        // Insert a new domain request with created_at timestamp
        $insertStmt = $pdo->prepare("INSERT INTO secondcookieslinkdnsdomain_requests (domain_name, user_id, has_updated, created_at) VALUES (:domain, :user_id, 0, CURRENT_TIMESTAMP)");
        if ($insertStmt->execute(['domain' => $domain, 'user_id' => $userId])) {
            echo "Request sent successfully.";
        } else {
            echo "Failed to submit request.";
        }
    }
}

// Fetch domain requests for the signed-in user
$stmt = $pdo->prepare("SELECT * FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id");
$stmt->execute(['user_id' => $userId]);
$domainRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>


<!DOCTYPE html>
<html>
<head>
    <title>Submit Domain Request</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(to right, #3a7bd5, #00d2ff);
            color: #fff;
            min-height: 100vh;
        }
        h1, h2, h3 {
            margin: 0;
            padding: 0;
            font-weight: bold;
        }
        h1 {
            color: #fff;
           
            padding: 20px;
            font-size: 36px;
        }
        h2, h3 {
            color: #f7f7f7;
            margin-top: 20px;
           
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0 20px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f4f4f4;
            color: #333;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #ff6a00, #ff3a00);
            border: none;
            color: white;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: linear-gradient(45deg, #ff3a00, #ff6a00);
            transform: translateY(-2px);
        }

        .status {
            padding: 20px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .status p {
            font-size: 16px;
            margin: 10px 0;
        }

        .success {
            color: white;
            font-weight: bold;
        }

        .redirect {
            color: white;
            font-weight: bold;
        }

        .client-error {
            color: white;
            font-weight: bold;
        }

        .server-error {
            color: white;
            font-weight: bold;
        }

        .offline {
            color: white;
            font-weight: bold;
        }

        .dns-error {
            color:white;
            font-weight: bold;
        }

    </style>
    
</head>
<body>
    <div class="container">
   
        
        <!-- Show domain requests submitted by the logged-in user -->
        <?php if ($domainRequests): ?>
          
            <ul>
                <?php foreach ($domainRequests as $request): ?>
                    <li><?php echo htmlspecialchars($request['domain_name']); ?> (Status: <?php echo $request['status']; ?>)</li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

 <h3>Welcome to our QR Code and Attachment Settings Page! Here, you can easily connect your personal QR Code and Attachment domain. Follow the steps below to ensure everything is set up correctly.</h3>
        
        <!-- Form to submit a new domain request -->
        <h3>Connect Your Newly Bought Domain to Panel</h3>
        <form id="domainRequestForm">
            <label for="domain">Domain Name:</label>
            <input type="text" id="domain" name="domain" required>
            <button type="submit" id="submitBtn">Connect</button>
        </form>
        
  
    <?php 
    $domainExists = false;
    $statusExists = false;

    foreach ($domainRequests as $request) {
        if (!empty($request['domain_name']) && !empty($request['status'])) {
            $domainExists = true;
            $statusExists = true;
            break;
        }
    }
    ?>

<br>
<br>
    <?php if ($domainExists && $statusExists): ?>
    <div class="alert alert-info">
        Please visit the QR CODE and Attachment <strong>NameServers Settings</strong> by clicking the button below to set up the domain properly with the RaccoonO365 Panel if you have not yet done so. 
        <a href="userconnect.php" class="btn btn-primary">
            Click here to visit NameServers Settings to set NameServers for this Domain
        </a>
    </div>
<?php endif; ?>

        
       
    </div>
    
  
    <script>
        $(document).ready(function() {
            $("#domainRequestForm").on("submit", function(event) {
                event.preventDefault(); // Prevent default form submission

                var domain = $("#domain").val(); // Get domain value

                $.ajax({
                    url: '', // Send request to self
                    method: 'POST',
                    data: {
                        connect: true,
                        domain: domain
                    },
                    success: function() {
                        // Show success message using SweetAlert
                        Swal.fire({
                            title: 'Success!',
                            text: 'Domain request updated successfully',  // Display static success message
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Refresh the page after success
                                location.reload();
                            }
                        });
                    },
                    error: function() {
                        // Show error message using SweetAlert
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>

