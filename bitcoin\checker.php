<?php

session_start();
$config = include 'sextractedconfig.php';

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Set header to return JSON response
header('Content-Type: application/json');

// Auto-create `blockchaintransactions` table if it does not exist
$tableSQL = "CREATE TABLE IF NOT EXISTS blockchaintransactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_hash VARCHAR(255) NOT NULL UNIQUE,
    walletAddress VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    amount_received DECIMAL(20, 8) NOT NULL,
    transactionDate DATE NOT NULL,
    Amountbtc DECIMAL(20, 8) NOT NULL,
    Equivalentusd DECIMAL(20, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$pdo->exec($tableSQL);

// Function to get the current BTC to USD exchange rate
function getBTCtoUSD() {
    $url = "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd";
    $response = file_get_contents($url);
    if (!$response) return 0;
    $data = json_decode($response, true);
    return $data['bitcoin']['usd'] ?? 0;
}

// Function to get recent transactions for a Bitcoin address
function getRecentTransactions($walletAddress) {
    $url = "https://blockchain.info/rawaddr/$walletAddress";
    $response = file_get_contents($url);
    if (!$response) return [];
    $data = json_decode($response, true);
    return $data['txs'] ?? [];
}

$userId = $_SESSION['user_id'];

// Get bitcoin_wallet for the signed-in user
$sql = "SELECT bitcoin_wallet FROM user_profiles WHERE user_id = ?";
$stmt = $pdo->prepare($sql);
$stmt->execute([$userId]);
$bitcoin_wallet = $stmt->fetchColumn();

if (!$bitcoin_wallet) {
    die(json_encode(["error" => "Bitcoin wallet not found for user."]));
}

$walletAddress = $bitcoin_wallet;

// Get BTC to USD exchange rate
$btcToUsd = getBTCtoUSD();

// Get latest transactions
$transactions = getRecentTransactions($walletAddress);

// Get user ID from session
if (!isset($_SESSION['user_id'])) {
    die("User not logged in");
}
$user_id = $_SESSION['user_id'];

if (empty($transactions)) {
    // No transactions found
} else {
    foreach ($transactions as $tx) {
        $btcAmount = 0;
        foreach ($tx['out'] as $output) {
            if ($output['addr'] === $walletAddress) {
                $btcAmount += $output['value'] / 100000000; // Convert Satoshis to BTC
            }
        }

        if ($btcAmount > 0) {
            $usdAmount = $btcAmount * $btcToUsd;
           $fee = $usdAmount * 0.0321; // Calculate 3.21% fee

            $remainingUsdAmount = $usdAmount - $fee; // Subtract fee from USD amount

            $transactionHash = $tx['hash'];
            $transactionDate = date('Y-m-d', $tx['time']); // Only show the date

            // Store transaction in the database
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO blockchaintransactions (transaction_hash, walletAddress, user_id, amount_received, transactionDate, Amountbtc, Equivalentusd)
                    VALUES (:transaction_hash, :walletAddress, :user_id, :amount_received, :transactionDate, :Amountbtc, :Equivalentusd)
                    ON DUPLICATE KEY UPDATE amount_received = :amount_received, transactionDate = :transactionDate, Amountbtc = :Amountbtc, Equivalentusd = :Equivalentusd
                ");

                $stmt->execute([
                    ':transaction_hash' => $transactionHash,
                    ':walletAddress' => $walletAddress,
                    ':user_id' => $user_id,
                    ':amount_received' => $remainingUsdAmount,
                    ':transactionDate' => $transactionDate,
                    ':Amountbtc' => $btcAmount,
                    ':Equivalentusd' => $usdAmount
                ]);
            } catch (PDOException $e) {
                // Error inserting transaction
            }
        }
    }
}

// Function to update confirmed transactions
function updateConfirmedTransactions($pdo) {
    $query = "SELECT transaction_hash FROM bitcointransaction WHERE status = 'confirmed'";
    $stmt = $pdo->query($query);
    $confirmedTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($confirmedTransactions as $transaction) {
        $transactionHash = $transaction['transaction_hash'];

        $updateQuery = "UPDATE blockchaintransactions 
                        SET status = 'confirmed' 
                        WHERE transaction_hash = :transaction_hash";

        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->execute([':transaction_hash' => $transactionHash]);
    }
}

// Run the function to update transactions
updateConfirmedTransactions($pdo);
?>
