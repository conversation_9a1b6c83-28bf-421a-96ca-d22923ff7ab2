<?php
// download.php

// Include the database connection or other necessary files
require_once '../db.php'; // Adjust the path as needed

// Get the file ID or filename from the URL parameter
if (isset($_GET['file'])) {
    $file = basename($_GET['file']); // Get the file name securely
    $filePath = 'uploads/' . $file; // Define the path to the file

    // Check if the file exists
    if (file_exists($filePath)) {
        // Set the appropriate headers for file download
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($filePath) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filePath));

        // Clear the output buffer and read the file
        flush();
        readfile($filePath);
        exit;
    } else {
        // File not found
        echo 'File not found.';
    }
} else {
    // No file specified
    echo 'No file specified.';
}
