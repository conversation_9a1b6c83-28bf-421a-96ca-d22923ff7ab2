<?php
session_start(); // Start the session to access session variables

// Destroy all session variables
$_SESSION = array(); 

// If you want to destroy the session completely (removes the session cookie)
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000, 
        $params["path"], 
        $params["domain"], 
        $params["secure"], 
        $params["httponly"]
    );
}

// Destroy the session completely
session_destroy(); 

// Clear all cookies by iterating over the $_COOKIE array
foreach ($_COOKIE as $cookie_name => $cookie_value) {
    setcookie($cookie_name, '', time() - 3600, '/'); // Set each cookie's expiration time to the past
}

// Redirect to the login page or homepage after logout
header("Location: signin.htm"); 
exit();
?>
