<?php
// Include database connection
require_once '../db.php';

function saveSmtpSettings() {
    global $pdo;

    // Prepare the SQL statement
    $stmt = $pdo->prepare("INSERT INTO smtp_settings (smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption, is_active) VALUES (?, ?, ?, ?, ?, ?)");
    
    // Execute the statement with the posted data
    $success = $stmt->execute([
        $_POST['host'],
        $_POST['port'],
        $_POST['username'],
        $_POST['password'],
        $_POST['encryption'],
        isset($_POST['is_active']) ? 1 : 0 // Set active based on checkbox input
    ]);

    if ($success) {
        return ['status' => 'success', 'message' => 'SMTP settings saved successfully.'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to save SMTP settings.'];
    }
}


function updateTransferFee() {
    global $pdo;
    $t_fee = $_POST['transfer_fee'];

    try {
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        
        // Execute with the fee and key
        $stmt->execute(['transfer_fee', $t_fee, $t_fee]);

        return ['status' => 'success', 'message' => 'Transfer fee settings updated successfully.'];
    } catch (\Throwable $th) {
        return ['status' => 'error', 'message' => 'Something went wrong: ' . $th->getMessage()];
    }
}


function updateApiKey() {
    global $pdo;
    $api_key = $_POST['apiKey'];

    try {
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        
        // Execute with the fee and key
        $stmt->execute(['api_key', $api_key, $api_key]);

        return ['status' => 'success', 'message' => 'Plisio API settings updated successfully.'];
    } catch (\Throwable $th) {
        return ['status' => 'error', 'message' => 'Something went wrong: ' . $th->getMessage()];
    }
}


function activate($id) {
    global $pdo;

    // Deactivate all other SMTP settings
    $stmt = $pdo->prepare("UPDATE smtp_settings SET is_active = 0");
    $stmt->execute();

    // Activate the selected SMTP setting
    $stmt = $pdo->prepare("UPDATE smtp_settings SET is_active = 1 WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    return ['status' => 'success', 'message' => 'SMTP activated successfully.'];
}


function deactivate($id) {
    global $pdo;

    // Deactivate the selected SMTP setting
    $stmt = $pdo->prepare("UPDATE smtp_settings SET is_active = 0 WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    return ['status' => 'success', 'message' => 'SMTP deactivated successfully.'];
}

function updateEmailUnlockFee() {
    global $pdo;
    $e_fee = $_POST['email_fee'];

    try {
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");

        // Execute with the fee and key
        $stmt->execute(['email_unlock_fee', $e_fee, $e_fee]);

        return ['status' => 'success', 'message' => 'Email Unlock settings updated successfully.'];
    } catch (\Throwable $th) {
        return ['status' => 'error', 'message' => 'Something went wrong: ' . $th->getMessage()];
    }
}


// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

if (isset($_GET['action']) && $_GET['action'] === 'updateSmtp') {
        $response = saveSmtpSettings();
} else if ( isset($_GET['action']) && $_GET['action'] === 'updateApi' ) {
    $response = updateApiKey();
}  else if ( isset($_GET['action']) && $_GET['action'] === 'updateEUnlockFee' ) {
} else if ( isset($_GET['action']) && $_GET['action'] === 'updateTransferFee' ) {
    $response = updateTransferFee();
}  else if ( isset($_GET['action']) && $_GET['action'] === 'updateEUnlockFee' ) {
    $response = updateEmailUnlockFee();
} else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($_POST['action'] === 'activate') {
        $response = activate($_POST['id']);
    } elseif ($_POST['action'] === 'deactivate') {
        $response = deactivate($_POST['id']);
    }
}


// Send a JSON response
header('Content-Type: application/json');
echo json_encode($response);