<?php
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a connection to the database
$mysqli = new mysqli($host, $username, $password, $dbname);

// Check for database connection errors
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

// Function to get botToken, allowedChatId, and user_id from transferagent table
function getBotDetails($mysqli, $chatId) {
    $query = "SELECT bot_token, allowed_chat_id, user_id FROM transferagent WHERE allowed_chat_id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("i", $chatId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $row = $result->fetch_assoc()) {
        return [
            'botToken' => $row['bot_token'],
            'allowedChatId' => $row['allowed_chat_id'],
            'userId' => $row['user_id']
        ];
    }
    return null; // Return null if no data found
}

// Get the incoming message
$content = file_get_contents("php://input");
$update = json_decode($content, true);

// Check if message is from a valid chat ID
if (isset($update['message'])) {
    $chatId = $update['message']['chat']['id'];
    $text = $update['message']['text'];
    
    $text = str_replace('$', '', $text);

    // Get bot details from transferagent table for the chat_id
    $botDetails = getBotDetails($mysqli, $chatId);
    
    if (!$botDetails) {
        sendMessage($chatId, "You are not authorized to use this bot.");
        exit;
    }

    $botToken = $botDetails['botToken'];
    $allowedChatId = $botDetails['allowedChatId'];
    $apiUrl = "https://api.telegram.org/bot$botToken/";

    // Check if the message is from the allowed chat ID
    if ($chatId == $allowedChatId) {
        // Parse the message for "send <amount> to <username>" or "send $<amount> to <username>"
         $pattern = "/^send\s*(\d+(\.\d{1,2})?)\s*to\s*([a-zA-Z0-9_]+)$/i";

        if (preg_match($pattern, $text, $matches)) {
            // Extracted amount (with or without $)
            $amount = $matches[1];  // This will capture the amount (with or without $)

            // Remove the dollar sign if present
            $amount = str_replace('$', '', $amount);

            // Check if the amount is a valid number after removing $
            if (is_numeric($amount)) {
                // Extract the recipient username
                $recipientUsername = $matches[3];

                // Get the sender's user_id from the transferagent table based on the chat_id
                $senderUserId = $botDetails['userId'];

                if ($senderUserId) {
                    // Get the sender's wallet balance
                    $senderBalance = getWalletBalance($senderUserId, $mysqli);
                    if ($senderBalance >= $amount) {
                        // Get recipient's user_id from username
                        $recipientUserId = getUserIdFromUsername($recipientUsername, $mysqli);

                        if ($recipientUserId) {
                            // Check if the sender is trying to send money to themselves
                            if ($senderUserId == $recipientUserId) {
                                sendMessage($chatId, "My Comrade! Code Red, Find a worthy recipient! \n\n You cannot send money to yourself, that’s not how we conduct business here. This is not a one-man army operation.\n\n Get back in formation and find an actual recipient RaccoonO365 panel username! This is a mission failure, you need a partner in crime RaccoonO365 panel username. ");
                            } else {
                                // Perform the transfer and update balances
                                $success = performTransfer($senderUserId, $recipientUserId, $amount, $mysqli, $chatId, $recipientUsername);
                                if ($success) {
                                    // This message is handled inside performTransfer function
                                } else {
                                    sendMessage($chatId, "Failed to transfer to $recipientUsername. Please try again.");
                                }
                            }
                        } else {
                            sendMessage($chatId, "Greetings Comrade! \n\nThe Recipient panel username does not exist in RaccoonO365 Engine.");
                        }
                    } else {
                        sendMessage($chatId, "Greetings Comrade!\n\n You do not have insufficient funds to complete this transfer.");
                    }
                } else {
                    //sendMessage($chatId, "Sender user ID not found.");
                }
            } else {
                //sendMessage($chatId, "Invalid amount format.");
            }
        } 
        // Check for balance command (case-insensitive)
        elseif (strtolower($text) == "balance") {
            $senderUserId = $botDetails['userId'];

            if ($senderUserId) {
                // Get the sender's wallet balance
                $senderBalance = getWalletBalance($senderUserId, $mysqli);
                // Respond with the balance
                sendMessage($chatId, "Greetings, Comrade!\n\n 📡 Intel confirms that your current RaccoonO365 Panel wallet balance is: $$senderBalance. Stay vigilant, Stay sharp! 💼 \n\n You’re almost there, Commander! To make a transfer to another RaccoonO365 user panel wallet, please respond with the message format: 'send <amount> to <panelusername>' Let’s make that transfer happen!");
            } else {
                //sendMessage($chatId, "Sender user ID not found.");
            }
        } elseif (preg_match("/^(reset password)$/i", trim($text))) {
            resetUserPassword($botDetails['userId'], $chatId, $mysqli);
        }
        else {
            sendMessage($chatId, "Whoops, Comrade! 😬 Invalid command, Comrade. Respond with the format: 'send <amount> to <username>' to make transfer to another RaccoonO365 user panel wallet\n\n OR Respond with 'balance' to check your wallet balance.\n\n To reset your RaccoonO365 Panel Password respond with 'Reset Password' Get back in formation and try again! Stay sharp! ");
        }
    } else {
       // sendMessage($chatId, "You are not authorized to use this bot. Please link your bot token and chat ID.");
    }
}

// Function to send a message back to the user
function sendMessage($chatId, $message) {
    global $apiUrl;
    file_get_contents($apiUrl . "sendMessage?chat_id=$chatId&text=" . urlencode($message));
}

// Function to get wallet balance for the sender
function getWalletBalance($userId, $mysqli) {
    $query = "SELECT balance FROM wallet WHERE user_id = ?";  
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $wallet = $result->fetch_assoc();
    return $wallet ? $wallet['balance'] : 0;
}

// Function to get user_id from the recipient's username
function getUserIdFromUsername($username, $mysqli) {
    $query = "SELECT user_id FROM user_profiles WHERE username = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    return $user ? $user['user_id'] : null;
}

// Function to perform the transfer and update balances
function performTransfer($senderUserId, $recipientUserId, $amount, $mysqli, $chatId, $recipientUsername) {
    // Begin a transaction
    $mysqli->begin_transaction();
    
    try {
        // Deduct amount from sender's balance
        $updateSenderBalance = "UPDATE wallet SET balance = balance - ? WHERE user_id = ?";  
        $stmt = $mysqli->prepare($updateSenderBalance);
        $stmt->bind_param("di", $amount, $senderUserId);
        $stmt->execute();

        // Add amount to recipient's balance
        $updateRecipientBalance = "UPDATE wallet SET balance = balance + ? WHERE user_id = ?";  
        $stmt = $mysqli->prepare($updateRecipientBalance);
        $stmt->bind_param("di", $amount, $recipientUserId);
        $stmt->execute();

        // Store the transaction in the wallet_transactions table
        $insertSenderTransaction = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status, created_at) VALUES (?, 'USDT', ?, ?, 'debit', 'completed', NOW())";
        $stmt = $mysqli->prepare($insertSenderTransaction);
        $transactionId = generateTransactionId();
        $stmt->bind_param("ids", $senderUserId, $amount, $transactionId);
        $stmt->execute();

        $insertRecipientTransaction = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status, created_at) VALUES (?, 'USDT', ?, ?, 'credit', 'completed', NOW())";
        $stmt = $mysqli->prepare($insertRecipientTransaction);
        $stmt->bind_param("ids", $recipientUserId, $amount, $transactionId);
        $stmt->execute();

        // Commit the transaction
        $mysqli->commit();

        // Fetch the updated balances for both sender and recipient
        $senderNewBalance = getWalletBalance($senderUserId, $mysqli);
        $recipientNewBalance = getWalletBalance($recipientUserId, $mysqli);

        // Send the success message with updated balances
        $message = "Greetings Comrade!\n Transfer of $$amount to $recipientUsername was successful!\n" .
                   "Remaining amount in your wallet: $$senderNewBalance\n" .
                   "Current $recipientUsername's wallet balance is: $$recipientNewBalance";
        sendMessage($chatId, $message);

        return true;
    } catch (Exception $e) {
        // If an error occurs, roll back the transaction
        $mysqli->rollback();
       // sendMessage($chatId, "An error occurred. Please try again later.");
        return false;
    }
}



function resetUserPassword($userId, $chatId, $mysqli) {
    // Auto-detect BASE_URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . "://" . $host;

    // Prepare and execute the query to get the user's email
    $stmt = $mysqli->prepare("SELECT email FROM user_profiles WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        $email = $user['email'];
        $resetUrl = $baseUrl . "/php_files/admin/user_actions.php?action=resetPassword"; // Use auto-detected base URL
        $postData = http_build_query(['user_id' => $userId]);
        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => $postData,
            ]
        ];
        file_get_contents($resetUrl, false, stream_context_create($options)); // Send the reset request
        sendMessage($chatId, "Greetings Comrade!\n\n Password reset email sent to $email. Please be aware that there may be a slight delay in receiving the password reset email. This can happen due to email server processing times. If you don't receive the email right away, kindly wait a few minutes and check your inbox again. Don't forget to check your spam or junk folder in case the email is filtered there. Thank you for your patience!");
    } else {
        sendMessage($chatId, "Email not found.");
    }
}



// Function to generate a unique transaction ID
function generateTransactionId() {
    return 'txn_' . bin2hex(random_bytes(16)); // Generates a unique 32-character transaction ID
}
?>
