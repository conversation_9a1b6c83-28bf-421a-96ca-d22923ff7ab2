[15-Feb-2025 06:02:43 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 173
[15-Feb-2025 06:09:42 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 173
[15-Feb-2025 06:10:07 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 173
[15-Feb-2025 06:10:12 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 173
[15-Feb-2025 06:10:25 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 173
[15-Feb-2025 06:13:37 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 180
[15-Feb-2025 06:14:06 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 180
[15-Feb-2025 06:14:38 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 180
[15-Feb-2025 06:14:58 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 180
[15-Feb-2025 06:21:26 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 180
[15-Feb-2025 06:25:43 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 180
[15-Feb-2025 06:27:07 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
[15-Feb-2025 06:27:22 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
[15-Feb-2025 06:33:00 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
[15-Feb-2025 06:33:04 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
[15-Feb-2025 06:33:10 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
[15-Feb-2025 06:39:27 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
[17-Feb-2025 13:02:17 UTC] Executing query: CREATE TABLE landing_page (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        url VARCHAR(255) NOT NULL
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM antibottoggle_states
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT state FROM antibottoggle_states WHERE type = 'isVisitLimit'
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT url FROM landing_page WHERE id = 1
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT is_enabled FROM blockedlandingpageredirect WHERE id = 1
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Microsoft')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Google')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Bing')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Yahoo')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Godaddy')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Digital Ocean')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Unified Layer')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Cloudflare')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Cisco Umbrella')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Barracuda Networks')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Proofpoint')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Mimecast')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Amazon')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Fortinet')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Webroot')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Kaspersky Lab')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Trend Micro')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('McAfee')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Scaleway')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Forcepoint')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Zscaler')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('FireEye')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Trellix')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('Sophos')
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT COUNT(*) as count FROM blockedispslist WHERE LOWER(isp_name) = LOWER('F-Secure')
[17-Feb-2025 13:02:17 UTC] Executing query: CREATE TABLE IF NOT EXISTS landingpageurl (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        url VARCHAR(255) NOT NULL DEFAULT 'https://google.com/de'
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci
[17-Feb-2025 13:02:17 UTC] Executing query: SELECT url FROM landingpageurl WHERE id = 1
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT COUNT(*) as count FROM antibottoggle_states
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT state FROM antibottoggle_states WHERE type = 'isVisitLimit'
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT url FROM landing_page WHERE id = 1
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT is_enabled FROM blockedlandingpageredirect WHERE id = 1
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT COUNT(*) as count FROM antibottoggle_states
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT state FROM antibottoggle_states WHERE type = 'isVisitLimit'
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT url FROM landing_page WHERE id = 1
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT is_enabled FROM blockedlandingpageredirect WHERE id = 1
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT COUNT(*) as count FROM antibottoggle_states
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT state FROM antibottoggle_states WHERE type = 'isVisitLimit'
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT url FROM landing_page WHERE id = 1
[17-Feb-2025 13:02:18 UTC] Executing query: SELECT is_enabled FROM blockedlandingpageredirect WHERE id = 1
[18-Feb-2025 15:43:15 UTC] PHP Warning:  Undefined array key "signinpageurl" in /home/<USER>/public_html/admin/walletaddressview.php on line 155
[18-Feb-2025 15:43:15 UTC] PHP Warning:  Undefined array key "expired" in /home/<USER>/public_html/admin/walletaddressview.php on line 156
[18-Feb-2025 15:43:15 UTC] PHP Warning:  Undefined array key "signinpageurl" in /home/<USER>/public_html/admin/walletaddressview.php on line 155
[18-Feb-2025 15:43:15 UTC] PHP Warning:  Undefined array key "expired" in /home/<USER>/public_html/admin/walletaddressview.php on line 156
[18-Feb-2025 22:45:31 UTC] PHP Parse error:  syntax error, unexpected end of file in /home/<USER>/public_html/admin/subscriptions.php on line 285
[19-Feb-2025 23:47:39 UTC] PHP Warning:  Undefined array key "api_key" in /home/<USER>/public_html/admin/settings.php on line 176
