<?php
require('db.php');
require('functions.php');

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['username'])) {
    // Return error if not authenticated
    echo json_encode(['status' => 'error', 'message' => 'User not authenticated']);
    exit;
}

$encryptionKey = 'RaccoonO365'; // Encryption key for decryption

// Decryption function
function decryptMessage($encryptedMessage, $key) {
    // Ensure message is not empty or null
    if (empty($encryptedMessage)) {
        return false;
    }

    // Split the encrypted message into data and IV parts
    list($encryptedData, $iv) = explode('::', $encryptedMessage);

    // Check if the IV length is valid (32 hex chars = 16 bytes)
    if (strlen($iv) != 32) {
        return false;
    }

    // Decrypt the message using AES-256-CBC
    return openssl_decrypt($encryptedData, 'aes-256-cbc', $key, 0, hex2bin($iv));
}

$username = $_SESSION['username'];

try {
    // Fetch all messages related to the logged-in user using username
    $stmt = $pdo->prepare("SELECT id, username, last_message, support_agentmessage, status, created_at, is_read FROM chats WHERE username = ? ORDER BY created_at ASC");
    $stmt->execute([$username]);
    
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Decrypt each message's 'last_message' and 'support_agentmessage' fields
    foreach ($messages as &$message) {
        // Decrypt the last_message and support_agentmessage
        $decryptedLastMessage = decryptMessage($message['last_message'], $encryptionKey);
        $decryptedAgentMessage = decryptMessage($message['support_agentmessage'], $encryptionKey);
        
        if ($decryptedLastMessage === false) {
            $message['last_message'] = '';
        } else {
            $message['last_message'] = $decryptedLastMessage;
        }

        if ($decryptedAgentMessage === false) {
            $message['support_agentmessage'] = '';
        } else {
            $message['support_agentmessage'] = $decryptedAgentMessage;
        }
    }
    
    // Return messages in JSON format
    echo json_encode(['status' => 'success', 'messages' => $messages]);
} catch (PDOException $e) {
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
