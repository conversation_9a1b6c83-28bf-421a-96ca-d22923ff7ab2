<?php
/*// Include database connection and any required functions
require_once '../db.php';

// Define the time interval to check for expiring subscriptions (e.g., within the next 24 hours)
$expirationInterval = '1 DAY'; // Modify this to the appropriate interval

try {
    // Get all subscriptions that will expire in the next 24 hours
    $stmt = $pdo->prepare("
        SELECT us.*, sp.price, sp.duration_days 
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.subscription_end_date <= NOW() + INTERVAL $expirationInterval
    ");
    $stmt->execute();
    $expiringSubscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($expiringSubscriptions as $subscription) {
        $userId = $subscription['user_id'];
        $subscriptionPrice = floatval($subscription['price']);

        // Fetch user's wallet balance
        $stmt = $pdo->prepare("SELECT balance FROM user_wallets WHERE user_id = ?");
        $stmt->execute([$userId]);
        $wallet = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($wallet && floatval($wallet['balance']) >= $subscriptionPrice) {
            // Deduct the subscription price from the wallet
            $newBalance = floatval($wallet['balance']) - $subscriptionPrice;
            $stmt = $pdo->prepare("UPDATE user_wallets SET balance = ? WHERE user_id = ?");
            $stmt->execute([$newBalance, $userId]);

            // Extend the subscription by the plan's duration
            $newSubscriptionEndDate = date('Y-m-d', strtotime("+{$subscription['duration_days']} days"));

            $stmt = $pdo->prepare("UPDATE user_subscriptions SET subscription_end_date = ?, status = 'active' WHERE id = ?");
            $stmt->execute([$newSubscriptionEndDate, $subscription['id']]);

            // Optionally, notify the user (email or in-app notification)
            // sendNotification($userId, 'Your subscription has been renewed.');
            echo "Subscription renewed for user ID: {$userId}\n";
        } else {
            // Insufficient wallet balance, send a notification
            // sendNotification($userId, 'Your subscription could not be renewed due to insufficient wallet balance.');
            echo "Insufficient balance for user ID: {$userId}\n";
        }
    }
} catch (PDOException $e) {
    // Handle any database errors
    echo "Error: " . $e->getMessage();
}
*/?>
