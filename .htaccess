RewriteEngine On

# Block direct access from external sites (hotlink protection), EXCEPT for the listed file types
RewriteCond %{HTTP_REFERER} !^$ 
RewriteCond %{HTTP_REFERER} !^https?://%{HTTP_HOST}/?.*$ [NC]
RewriteCond %{HTTP_REFERER} !^https?://www\.%{HTTP_HOST}/?.*$ [NC]
RewriteCond %{REQUEST_URI} !\.[a-zA-Z0-9]+$ [NC]  # Exclude all files with extensions (e.g., .php, .html, .css, etc.)
RewriteRule .*\.(.*)$ - [F,NC]

<FilesMatch "\.(css|gif|jpg|png|js|ico|woff2|ttf|svg|eot|woff)$">
    Require all granted
</FilesMatch>

<IfModule mod_headers.c>
    # Allow all origins
    Header set Access-Control-Allow-Origin "*"

    # Allow specific HTTP methods (adjust as needed)
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"

    # Allow specific headers (adjust as needed)
    Header set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

    # Allow credentials (optional, if needed)
    # Header set Access-Control-Allow-Credentials "true"
</IfModule>

# Force HTTPS for all requests (no www)
RewriteCond %{HTTPS} !=on
RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [R=301,L]

# Set the custom 404 error page
ErrorDocument 404 /error/

# Optionally, redirect all non-existing pages to the custom 404 page
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /error/ [L]

# Prevent direct access to the .htaccess file for security
<Files .htaccess>
  Order Allow,Deny
  Deny from all
</Files>
