
<?php
require('../assets/admin_authorize.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare Data Form</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Include SweetAlert -->

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 10px;
            text-align: center;
        }

        h3 {
            color: #555;
            font-size: 1.1rem;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
        }
      button {
            background-color: #007BFF;
            color: white;
            border: none;
            cursor: pointer;
        }
        .form-group input {
           
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group button:focus {
            border-color: #4caf50;
            outline: none;
        }

        .form-group label {
            font-weight: bold;
            font-size: 0.9rem;
            color: #555;
            margin-bottom: 6px;
            display: block;
        }

        .form-group input {
            margin-bottom: 15px;
        }

        button {
            background-color: #007BFF;
            color: white;
            border: none;
            cursor: pointer;
            text-align: center;
            font-size: 1rem;
            transition: background-color 0.3s ease, transform 0.2s ease;
             border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 1rem;
            padding: 13px;
        }

        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        button:active {
            background-color: #004085;
            transform: translateY(0);
        }

        .form-header {
            margin-bottom: 30px;
        }

        .form-footer {
            text-align: center;
            margin-top: 30px;
        }

        .form-footer p {
            color: #777;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="form-header">
        <h1>RaccoonO365 VPS Loader</h1>
        <h3>Please enter the required details below</h3>
    </div>

    <form id="cloudflareForm">
        <div class="form-group">
            <div>
                <label for="main_email">Main Cookies Link</label>
                <input type="email" id="main_email" name="main_email" placeholder="Cloudflare Email" required>
                <input type="text" id="main_token" name="main_token" placeholder="API Token" required><br>
                <span>There are the required links:</span><br><span>Name: view</span>,<br><span>Name: connect</span>,<br><span>Name: office</span>,<br><span>Name: hotmail</span>,<br><span>Name: datacenter</span>
            </div>
            <div>
                <label for="addon_email">Addon Cookies Link</label>
                <input type="email" id="addon_email" name="addon_email" placeholder="Cloudflare Email" required>
                <input type="text" id="addon_token" name="addon_token" placeholder="API Token" required><br>
                <span>There are the required links:</span><br><span>Name: view</span>,<br><span>Name: connect</span>,<br><span>Name: office</span>,<br><span>Name: hotmail</span>,<br><span>Name: datacenter</span>
            </div>
        </div>

        <div class="form-group">
            <div>
                <label for="redirect_email">Redirect Link</label>
                <input type="email" id="redirect_email" name="redirect_email" placeholder="Cloudflare Email" required>
                <input type="text" id="redirect_token" name="redirect_token" placeholder="API Token" required>
                <br>
                <span>There are the required links:</span><br><span>Name: datacenter</span>,<br><span>Name: redirect</span>
            </div>
            <div>
                <label for="attach_email">Attach & QR Code Link</label>
                <input type="email" id="attach_email" name="attach_email" placeholder="Cloudflare Email" required>
                <input type="text" id="attach_token" name="attach_token" placeholder="API Token" required>
                <br> <span>There are the required links:</span><br><span>Name: qrcode</span>,<br><span>Name: attachment</span>,<br><span>Name: datacenter</span>
            </div>
        </div>

        <div class="form-group">
            <div>
                <label for="username">Panel Username</label>
                <input type="text" id="username" name="username" placeholder="Username" value="" required>
            </div>
            <div>
                <button type="button" id="generateUsername">Generate Username</button>
            </div>
        </div>

        <button type="submit">Submit</button>
    </form>

    <div class="form-footer">
        <p>Powered by RaccoonO365</p>
    </div>
</div>

<script>
    // JavaScript to generate new username
    $('#generateUsername').click(function() {
        $.ajax({
            url: 'generate_username.php', // Make a separate PHP file for username generation
            type: 'GET',
            success: function(response) {
                $('#username').val(response.username); // Update the username input with the new value
            }
        });
    });

    $("#cloudflareForm").on("submit", function(event) {
        event.preventDefault();
        $.ajax({
            url: 'submit.php',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                let result = JSON.parse(response);
                if (result.status === "success") {
                    // SweetAlert for success
                    Swal.fire({
                        title: 'Success!',
                        text: 'Data saved successfully!',
                        icon: 'success',
                        confirmButtonText: 'Okay'
                    });
                } else {
                    // SweetAlert for error
                    Swal.fire({
                        title: 'Error!',
                        text: result.message,
                        icon: 'error',
                        confirmButtonText: 'Try Again'
                    });
                }
            }
        });
    });
</script>

</body>
</html>
