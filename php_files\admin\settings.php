<?php
// Include database connection
require_once '../db.php';

function saveSmtpSettings() {
    global $pdo;
    
    
    
    // Check if the 'expire_date' column exists
    $stmt = $pdo->query("DESCRIBE smtp_settings");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // If 'expire_date' column does not exist, add it
    if (!in_array('expire_date', $columns)) {
        $alterStmt = $pdo->query("ALTER TABLE smtp_settings ADD COLUMN expire_date DATE");
        if (!$alterStmt) {
            //return ['status' => 'error', 'message' => 'Failed to add expire_date column.'];
        }
    }
    

    
     // Prepare the SQL statement
    $stmt = $pdo->prepare("INSERT INTO smtp_settings (smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption, tag, is_active, expire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

    // Get expiration date from POST request
    $expireDate = isset($_POST['expire_date']) ? $_POST['expire_date'] : null;

    // Validate the expiration date format (YYYY-MM-DD)
    if (!$expireDate || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $expireDate)) {
        return ['status' => 'error', 'message' => 'Invalid expiration date format. Use YYYY-MM-DD.'];
    }


    // Execute the statement with the posted data
    $success = $stmt->execute([
        $_POST['host'],
        $_POST['port'],
        $_POST['username'],
        $_POST['password'],
        $_POST['encryption'],
        $_POST['purpose'],
         isset($_POST['is_active']) ? 1 : 0, // Set active based on checkbox input
        $expireDate // Expiry date from POST request
    ]);

    if ($success) {
        return ['status' => 'success', 'message' => 'SMTP settings saved successfully.'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to save SMTP settings.'];
    }
}


function updateTransferFee() {
    global $pdo;
    $t_fee = $_POST['transfer_fee'];

    try {
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        
        // Execute with the fee and key
        $stmt->execute(['transfer_fee', $t_fee, $t_fee]);

        return ['status' => 'success', 'message' => 'Transfer fee settings updated successfully.'];
    } catch (\Throwable $th) {
        return ['status' => 'error', 'message' => 'Something went wrong: ' . $th->getMessage()];
    }
}


function updateApiKey() {
    global $pdo;
    $api_key = $_POST['apiKey'];

    try {
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        
        // Execute with the fee and key
        $stmt->execute(['api_key', $api_key, $api_key]);

        return ['status' => 'success', 'message' => 'Plisio API settings updated successfully.'];
    } catch (\Throwable $th) {
        return ['status' => 'error', 'message' => 'Something went wrong: ' . $th->getMessage()];
    }
}


function activate($id) {
    global $pdo;

    // Get the SMTP setting by ID
    $stmt = $pdo->prepare("SELECT tag FROM smtp_settings WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $smtpSetting = $stmt->fetch(PDO::FETCH_ASSOC);

    if (empty($smtpSetting)) {
        return ['status' => 'error', 'message' => 'SMTP setting not found.'];
    }

    // Activate the selected SMTP setting without deactivating others
    $stmt = $pdo->prepare("UPDATE smtp_settings SET is_active = 1 WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    return ['status' => 'success', 'message' => 'SMTP activated successfully.'];
}




function deactivate($id) {
    global $pdo;

    // Get the tag of the SMTP setting to be deactivated
    $stmt = $pdo->prepare("SELECT tag FROM smtp_settings WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $smtpSetting = $stmt->fetch(PDO::FETCH_ASSOC);

    if (empty($smtpSetting)) {
        return ['status' => 'error', 'message' => 'SMTP setting not found.'];
    }

    $tag = $smtpSetting['tag'];

    // Count the number of active SMTP settings with the same tag
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM smtp_settings WHERE is_active = 1 AND tag = :tag");
    $stmt->bindParam(':tag', $tag);
    $stmt->execute();
    $activeCount = $stmt->fetchColumn();

    // If only one active SMTP setting exists, do not allow deactivation
    if ($activeCount <= 1) {
        return ['status' => 'error', 'message' => 'Cannot deactivate the only active SMTP setting for this tag.'];
    }

    // Deactivate the selected SMTP setting
    $stmt = $pdo->prepare("UPDATE smtp_settings SET is_active = 0 WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    return ['status' => 'success', 'message' => 'SMTP deactivated successfully.'];
}


function updateEmailUnlockFee() {
    global $pdo;
    $e_fee = $_POST['email_fee'];

    try {
        // Use INSERT ... ON DUPLICATE KEY UPDATE
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");

        // Execute with the fee and key
        $stmt->execute(['email_unlock_fee', $e_fee, $e_fee]);

        return ['status' => 'success', 'message' => 'Email Unlock settings updated successfully.'];
    } catch (\Throwable $th) {
        return ['status' => 'error', 'message' => 'Something went wrong: ' . $th->getMessage()];
    }
}


// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

if (isset($_GET['action']) && $_GET['action'] === 'updateSmtp') {
        $response = saveSmtpSettings();
} else if ( isset($_GET['action']) && $_GET['action'] === 'updateApi' ) {
    $response = updateApiKey();
} else if ( isset($_GET['action']) && $_GET['action'] === 'updateTransferFee' ) {
    $response = updateTransferFee();
}  else if ( isset($_GET['action']) && $_GET['action'] === 'updateEUnlockFee' ) {
    $response = updateEmailUnlockFee();
} else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($_POST['action'] === 'activate') {
        $response = activate($_POST['id']);
    } elseif ($_POST['action'] === 'deactivate') {
        $response = deactivate($_POST['id']);
    }
}


// Send a JSON response
header('Content-Type: application/json');
echo json_encode($response);