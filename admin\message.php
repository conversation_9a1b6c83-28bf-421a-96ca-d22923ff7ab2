<?php
require('../php_files/db.php');
session_start();

error_reporting(0);

    if ( !isset( $_GET['chat_id'] ) ) {
        header('Location: support.php');
    }

    $chat_id = $_GET['chat_id'];
    $user_id = $_SESSION['user_id'];

    // Update the status of the chat to 'read'
    $sql = "UPDATE chats SET status = 'read' WHERE id = :chat_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':chat_id', $chat_id, PDO::PARAM_INT);
    $stmt->execute();



?>
<?php require('../assets/admin_header.php') ?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h4">Chat</h1>
    </div>
    <div class="card-body h-full">

        <div class="overflow-y-scroll"  id="chat-window" style="height: 250px;">

        </div>

        <div data-mdb-input-init class="form-outline">
                    <textarea class="form-control bg-body-tertiary" id="textAreaExample"
                              rows="3"></textarea>
            <button class="btn btn-primary" onclick="send_messages()">Send</button>
        </div>

    </div>
    </div>

    </div>

</main>
</div>
</div>
<script src="<?= BASE_URL ?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    const send_messages = () => {
        let message = $('#textAreaExample').val()
        $.ajax({
            url: '<?= BASE_URL ?>/php_files/send_message.php',
            type: 'POST',
            data: {
                chat_id : <?php echo $chat_id  ?> ,
                message : message
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#textAreaExample').val('');
                    fetchMessages();
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    }


    function fetchMessages() {
        $.ajax({
            url: '<?= BASE_URL ?>/php_files/send_message.php?action=get_messages',
            method: 'GET',
            data: { chat_id : <?php echo $chat_id  ?> } ,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    let messagesHtml = '';

                    // Loop through each message and append it to the chat window
                    $.each(response.messages, function(index, message) {
                        if (message.user_id === <?php echo $user_id; ?>) {
                            // Message sent by current user
                            messagesHtml += `
                                    <div class="d-flex flex-row justify-content-end mb-4">
                                        <div class="p-3 me-3 border bg-body-tertiary" style="border-radius: 15px;">
                                            <p class="small mb-0">${message.message}</p>
                                        </div>
                                    </div>
                                `;
                        } else {

                            let messageBackground;
                            if (message.type == 'emergency') {
                                messageBackground = 'bg-danger';  // Emergency message styling
                            } 
                            // Message sent by other user
                            messagesHtml += `
                                    <div class="d-flex flex-row justify-content-start mb-4">
                                        <div class="p-3 ms-3 ${messageBackground}" style="border-radius: 15px; background-color: rgba(57, 192, 237, .2);">
                                            <p class="small mb-0">${message.message}</p>
                                        </div>
                                    </div>
                                `;
                        }
                    });

                    // Update the chat window with the new messages
                    $('#chat-window').html(messagesHtml);

                    // Scroll the chat window to the bottom
                    $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);
                } else {
                    console.error(response.message);
                }
            },
            error: function(error) {
                console.error('Error fetching messages:', error);
            }
        });
    }

    // Fetch messages every 30 seconds
    setInterval(fetchMessages, 5000); // 30000 milliseconds = 30 seconds

    // Fetch messages immediately when the page loads
    fetchMessages();
</script>
</body>
</html>