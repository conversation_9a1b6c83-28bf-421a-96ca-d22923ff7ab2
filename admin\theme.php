<?php
require('../php_files/db.php');
global $pdo;

// Query for admin images: approved images from users and images from the admin
$stmt = $pdo->query("
    SELECT * FROM theme_images 
    WHERE (isApproved = TRUE AND user_id IS NOT NULL) 
    OR (user_id IS NULL)
");
$images = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Query for user images: images from users that haven't been approved yet
$user_stmt = $pdo->query("
    SELECT * FROM theme_images 
    WHERE isApproved = FALSE 
    AND user_id IS NOT NULL
");
$user_images = $user_stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<?php require '../assets/admin_header.php'; ?>
<style>
    .delete-btn:hover {
        cursor: pointer;
    }
    .delete-btn {
        height: 45px;
        width: 45px;
        top: 0;
        right: 15px;
    }
    .image-div {
        width: 200px;
        overflow: hidden;
        height: 150px;
    }
    .image-top {
        height: 100%;
        width: 100%;
        object-fit: contain;
    }
</style>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h4"> Add A Theme Image </h1>
    </div>
    <div>
        <form class="row g-3" id="themeForm" method="post" enctype="multipart/form-data">
            <div class="col-md-6">
                <label for="inputCity" class="form-label">Profile Picture</label>
                <input type="file" name="profile_picture" accept="image/*" class="form-control" id="inputPicture" required>
            </div>
            <div class="col-md-6">
                <label for="alt" class="form-label">Alt text</label>
                <input type="text" class="form-control" name="alt" id="alt">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary"> Add theme image </button>
            </div>
        </form>
    </div>
    <div class="row py-3">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h4"> Admin Theme Images </h1>
        </div>
        <?php
            if ( $images ) {
                foreach ($images as $image) {
                    echo '
                        <div class="col-md-3 col-6 mb-3 position-relative image-div">
                            <span onClick="deleteTheme('. $image['id'] .')" class="position-absolute delete-btn top-0 z-3 text-danger fs-5"> 
                                <i class="bi bi-trash3-fill p-0 m-0"></i> 
                            </span>
                            <div class="card">
                                <img src="' . ($image['imageUrl']) . '" class="card-img-top image-top" alt="' .
                        htmlspecialchars($image['altText']) . '">
                            </div>
                        </div>
                    ';
                }
            } else {
                echo "No theme image added yet";
            }
        ?>
    </div>
    <div class="row py-3">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h4"> User Theme Images  </h1>
        </div>
        <?php
            if ( $user_images ) {
                foreach ($user_images as $image) {
                    echo '
                        <div class="col-md-3 col-6 mb-3 position-relative">
                            <button onClick="approveTheme('. $image['id'] .')" class="position-absolute top-0 z-3 btn btn-success btn-sm"> Approve </button>
                            <span onClick="deleteTheme('. $image['id'] .')" class="position-absolute delete-btn top-0 right-0 z-3 text-danger fs-5"> 
                                <i class="bi bi-trash3-fill p-0 m-0"></i> 
                            </span>
                            <div class="card">
                                <img src="' . ($image['imageUrl']) . '" class="card-img-top" alt="' .
                        htmlspecialchars($image['altText']) . '">
                            </div>
                        </div>
                    ';
                }
            } else {
                echo "User theme is empty";
            }
        ?>
    </div>
</main>

<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Include SweetAlert2 Library -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Include SweetAlert2 Library -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(document).ready(function() {
        $('#themeForm').on('submit', function(e) {
            e.preventDefault(); // Prevent the default form submission

            var formData = new FormData(this); // Create a FormData object

            $.ajax({
                url: '<?= BASE_URL ?>/php_files/admin/theme_action.php', // PHP file to handle the request
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    // SweetAlert success message
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(function() {
                        window.location.reload(); // Reload the page after clicking OK
                    });
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    // SweetAlert error message
                    Swal.fire({
                        title: 'Error!',
                        text: 'Error: ' + textStatus + ' ' + errorThrown,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    }).then(function() {
                        window.location.reload(); // Reload the page after clicking OK
                    });
                }
            });
        });
    });

    function deleteTheme(id) {
        $.ajax({
            url: '<?= BASE_URL ?>/php_files/admin/theme_action.php?action=delete', // PHP file to handle the request
            type: 'POST',
            data: {
                id: id
            },
            dataType: 'json',
            success: function(response) {
                // SweetAlert success message
                Swal.fire({
                    title: 'Deleted!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(function() {
                    window.location.reload(); // Reload the page after deletion
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                // SweetAlert error message
                Swal.fire({
                    title: 'Error!',
                    text: 'Error: ' + textStatus + ' ' + errorThrown,
                    icon: 'error',
                    confirmButtonText: 'OK'
                }).then(function() {
                    window.location.reload(); // Reload the page after clicking OK
                });
            }
        });
    }

    function approveTheme(id) {
        $.ajax({
            url: '<?= BASE_URL ?>/php_files/admin/theme_action.php?action=approve', // PHP file to handle the request
            type: 'POST',
            data: {
                id: id
            },
            dataType: 'json',
            success: function(response) {
                // SweetAlert success message
                Swal.fire({
                    title: 'Approved!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(function() {
                    window.location.reload(); // Reload the page after approval
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                // SweetAlert error message
                Swal.fire({
                    title: 'Error!',
                    text: 'Error: ' + textStatus + ' ' + errorThrown,
                    icon: 'error',
                    confirmButtonText: 'OK'
                }).then(function() {
                    window.location.reload(); // Reload the page after clicking OK
                });
            }
        });
    }
</script>


</body>
</html>
