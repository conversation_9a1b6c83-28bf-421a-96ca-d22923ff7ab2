<?php
session_start();
require 'db_connection.php';

$user_id = $_SESSION['user_id'] ?? null;
$path = $_POST['path'] ?? null;
$generated_url = $_POST['generated_url'] ?? null;

if (!$user_id || !$path || !$generated_url) {
    echo json_encode(['error' => 'Invalid data']);
    exit;
}

// Delete all existing records for the given user_id
$delete_query = "DELETE FROM secondcookieslinkusergeneratedpath WHERE user_id = ?";
$delete_stmt = $conn->prepare($delete_query);
$delete_stmt->bind_param('i', $user_id);
if ($delete_stmt->execute()) {
    // Proceed with the check to see if we need to update or insert
    $query = "SELECT id FROM secondcookieslinkusergeneratedpath WHERE user_id = ? AND path = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('is', $user_id, $path);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Record exists, update the existing entry
        $update_query = "UPDATE secondcookieslinkusergeneratedpath SET generated_url = ? WHERE user_id = ? AND path = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param('sis', $generated_url, $user_id, $path);
        if ($update_stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Record updated']);
        } else {
            echo json_encode(['error' => 'Update failed']);
        }
    } else {
        // No existing record, insert a new entry
        $insert_query = "INSERT INTO secondcookieslinkusergeneratedpath (user_id, path, generated_url) VALUES (?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param('iss', $user_id, $path, $generated_url);
        if ($insert_stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Record inserted']);
        } else {
            echo json_encode(['error' => 'Insertion failed']);
        }
    }
} else {
    echo json_encode(['error' => 'Deletion failed']);
}

?>
