<?php


// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 



// Database configuration
$host =  $config['host']; // Database host
$db = $config['dbname']; // Database name
$user = $config['username']; // Database username
$pass = $config['password']; // Database password

// Check if any database setting is empty
if (empty($host) || empty($db) || empty($user) || (!isset($pass) && $pass !== '')) {
    echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Configuration</title>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css'>
    <script src='https://code.jquery.com/jquery-3.6.0.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js'></script>
    <style>
    .sweet-alert button {
  
  display: none !important;
}
.confirm { display: none !important; } /* Hide the confirm button */
        .swal-button--confirm { display: none !important; } /* Hide the confirm button */
    </style>
</head>
<body>
    <script>
        $(document).ready(function() {
            swal({
                title: 'Database Configuration Incomplete',
                text: 'Please check the database settings. Once the config has been added, please visit index.php for auto installation. The application will be auto installed upon visiting.',
                icon: 'warning',
                buttons: false, 
                closeOnClickOutside: false,  
                closeOnEsc: false
            });
        });
    </script>
</body>
</html>";
    exit; // Stop further script execution
}

?>