<?php
// Database connection settings
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

try {
    // SQL query to delete all history
    $query = "DELETE FROM newsletter_history";
    $pdo->exec($query);

    // Return success message
    echo json_encode(['message' => 'All email history has been cleared.']);
} catch (PDOException $e) {
    // Return error message if something goes wrong
   // echo json_encode(['message' => 'Failed to clear history: ' . $e->getMessage()]);
}
?>
