<?php

$email = '4christopher2<PERSON><PERSON><EMAIL>';  // Cloudflare email
$apiKey = '00c343d7e53c0c64183b9199b8ce6d277056a';  // Global API Key
$domain = 'officecloudfiles.com';

/**
 * Get the Zone ID
 */
function getZoneId($domain, $apiKey, $email) {
    $apiUrl = "https://api.cloudflare.com/client/v4/zones?name=" . urlencode($domain);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Auth-Email: $email",
        "X-Auth-Key: $apiKey",
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo "<span class='error'>cURL Error: " . curl_error($ch) . "</span><br>";
    }
    $data = json_decode($response, true);
    curl_close($ch);

    return $data['success'] ? $data['result'][0]['id'] : null;
}



/**
 * Generate Secure CSR with Wildcard for Subdomains
 */
function generateSecureCSR($domain) {
    // Generate the private key
    $privateKey = openssl_pkey_new([
        "private_key_bits" => 2048, 
        "private_key_type" => OPENSSL_KEYTYPE_RSA
    ]);
    
    // Extract the private key to save for later use
    openssl_pkey_export($privateKey, $privateKeyStr);

    // Set CSR details with wildcard for subdomains
    $csrDetails = [
        "countryName" => "US",
        "organizationName" => "Microsoft",
        "organizationalUnitName" => "Microsoft Cloud",
        "commonName" => "*.$domain",  // Wildcard for all subdomains
    ];

    // Generate the CSR
    $csr = openssl_csr_new($csrDetails, $privateKey);

    // Check if CSR generation was successful
    if (!$csr) {
        echo "<span class='error'>Failed to generate CSR.</span><br>";
        return null;
    }

    openssl_csr_export($csr, $csrStr);

    // Return the CSR and private key for safe storage
    return [
        'csr' => $csrStr,
        'private_key' => $privateKeyStr
    ];
}

/**
 * Create Client Certificate for Subdomains
 */
function createClientCertificate($zoneId, $apiKey, $email, $domain) {
    $apiUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/client_certificates";

    // Generate a secure CSR dynamically with wildcard for subdomains
    $csrData = generateSecureCSR($domain);
    if ($csrData === null) {
        return null;
    }

    $csr = $csrData['csr'];
    $privateKey = $csrData['private_key'];

    $validityDays = 5475; // Valid for 15 years

    $data = json_encode([
        "csr" => $csr,
        "validity_days" => $validityDays
    ]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Auth-Email: $email",
        "X-Auth-Key: $apiKey",
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo "<span class='error'>Client Certificate Error: " . curl_error($ch) . "</span><br>";
    }
    curl_close($ch);

    return $response;
}


// 1. Get Zone ID
$zoneId = getZoneId($domain, $apiKey, $email);

if ($zoneId) {
   // echo "<span class='success'>Zone ID: $zoneId retrieved successfully.</span><br>";

    // 2. Always create and install a new Client Certificate
    $clientCertResponse = createClientCertificate($zoneId, $apiKey, $email, $domain);
    if ($clientCertResponse) {
       // echo "<span class='success'>Client Certificate Created and Installed: $clientCertResponse</span><br>";
        
        
        echo "Client Certificate Created and Installed"; 
        
        
    } else {
      //  echo "<span class='error'>Failed to create Client Certificate.</span><br>";
      
      echo "Failed to install Client Certificate"; 
    }

} else {
  //  echo "<span class='error'>Failed to retrieve Zone ID for $domain.</span><br>";
}

?>
