<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google reCAPTCHA Setup</title>

    <!-- SweetAlert2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Stylesheet for a mature, polished look -->
    <style>
        /* Global styles */
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            margin: 0;
            padding: 0;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
        }

        h1 {
            color: #2a3d66;
            font-size: 2.5rem;
            margin-bottom: 30px;
        }

        h2 {
            font-size: 1.6rem;
            color: #444;
            margin-bottom: 15px;
        }

        /* Form container */
        .form-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 100%;
            max-width: 600px;
        }

        /* Input fields */
        label {
            display: block;
            font-size: 1rem;
            color: #555;
            margin-bottom: 5px;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 1rem;
            color: #333;
            box-sizing: border-box;
        }

        input[type="text"]:focus {
            border-color: #5e9ad6;
            outline: none;
        }

        /* Button */
        button {
            background-color: #5e9ad6;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #4e8bc6;
        }

        button:active {
            background-color: #3f7da4;
        }

        /* Instructions styling */
        .instructions {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            width: 100%;
            max-width: 800px;
            margin-bottom: 30px;
        }

        .instructions ol {
            padding-left: 20px;
            line-height: 1.8;
        }

        .instructions a {
            color: #5e9ad6;
            text-decoration: none;
        }

        .instructions a:hover {
            text-decoration: underline;
        }

        /* Responsive layout for smaller screens */
        @media screen and (max-width: 600px) {
            h1 {
                font-size: 2rem;
            }

            .form-container {
                width: 90%;
            }

            .instructions {
                width: 90%;
            }
        }
    </style>

    <script>
    $(document).ready(function() {
        // Fetch the user's domain dynamically on page load
        $.ajax({
            url: "process.php?action=fetch_domain",
            method: "GET",
            success: function(data) {
                const response = JSON.parse(data);
                if (response.success) {
                    const domain = response.domain;
                    // Set domain value in the input field and the instructions section
                    $("#google_domain").val(domain);
                    $("#domain").text(domain);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Error fetching domain: ' + response.message,
                    });
                }
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Unexpected Error',
                    text: 'An unexpected error occurred: ' + error,
                });
            }
        });

        // Fetch the user's Google Site Key and auto-fill the input field
        $.ajax({
            url: "process.php?action=fetch_google_key",
            method: "GET",
            success: function(data) {
                const response = JSON.parse(data);
                if (response.success) {
                    $("#google_site_key").val(response.google_key);
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error'
                    });
                }
            }
        });

        // Validate the form before submission and submit using AJAX
        $("form").submit(function(event) {
            event.preventDefault();  // Prevent the form from submitting the traditional way

            const googleKey = $("#google_site_key").val().trim();
            if (googleKey === "") {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Google Site Key cannot be empty!',
                });
            } else {
                const formData = $(this).serialize();  // Serialize form data for AJAX submission

                // AJAX request to submit the form data
                $.ajax({
                    url: "process.php?action=update_google_key",
                    method: "POST",
                    data: formData,
                    success: function(response) {
                        if (response.indexOf('Google Site Key updated successfully') !== -1) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: 'Google Site Key has been updated!',
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'There was an error updating the Site Key: ' + response.message,
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Unexpected Error',
                            text: 'An unexpected error occurred: ' + error,
                        });
                    }
                });
            }
        });

        // Add click event to input field to copy value to clipboard
        $("#google_domain").click(function() {
            const domainValue = $(this).val(); // Get the value of the input field
            navigator.clipboard.writeText(domainValue)  // Copy to clipboard
                .then(function() {
                    Swal.fire({
                        icon: 'success',
                        title: 'Copied!',
                        text: 'The domain has been copied to your clipboard.',
                    });
                })
                .catch(function(err) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to copy the domain: ' + err,
                    });
                });
        });
    });
</script>

</head>
<body>
    <h1>Set Up Google reCAPTCHA</h1>

    <!-- Instructions Section -->
    <div class="instructions">
        <h2>Instructions to Register Google reCAPTCHA</h2>
        <ol>
            <li>Visit the <a href="https://www.google.com/recaptcha/admin/create" target="_blank">Google reCAPTCHA Admin Console</a>.</li>
            <li>Create a new reCAPTCHA project:
                <ul>
                    <li>Enter a project name, e.g., <code id=domain></code>.</li>
                    <li>Select <strong>"reCAPTCHA v2"</strong> and choose the <strong>"I'm not a robot" Checkbox</strong> option.</li>
                </ul>
            </li>
            <li>Add the domain(s) where reCAPTCHA will be used:
                <ul>
                    <li>Do not include <code>http://</code> or <code>https://</code>. Enter just the domain name, e.g., your actual domain name like <code id=domain></code>.</li>
                </ul>
            </li>
            <li>Click <strong>Register</strong> to generate your Site Key and Secret Key.</li>
            <li>Copy the <strong>Site Key</strong> and paste it into the form below.</li>
        </ol>
        
        
        <!-- Form Section -->
    <div class="form-container">
        <form action="process.php?action=update_google_key" method="POST">
            <label for="google_site_key">Google Site Key:</label>
            <input type="text" id="google_site_key" name="google_site_key" placeholder="Enter your Site Key here" required>

            <label for="google_domain">Domain (from DNS records):</label>
            <input type="text" id="google_domain" name="google_domain" readonly>

            <button type="submit">Update Site Key</button>
        </form>
    </div>
    
    </div>
    
    
</body>
</html>
