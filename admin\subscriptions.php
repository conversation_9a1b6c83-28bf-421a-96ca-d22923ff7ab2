<?php
require('../assets/admin_authorize.php');
require('../php_files/db.php');
require('../php_files/functions.php');
error_reporting(0);

// Prepare and execute SQL query
$stmt = $pdo->prepare("SELECT * FROM subscription_plans");
$stmt->execute();
$plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<?php require '../assets/admin_header.php' ?>


 
        
<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5"> Subscriptions </h1>
        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal"
                data-bs-target="#addSubscriptionModal">
            Add subscription
        </button>
    </div>
    <div class="block row">
        <?php foreach ($plans as $plan) : 
        
        
        
        $price_per_day = $plan['price'] / $plan['duration_days'];
        
        ?>
        
        
        
        
        
            <div class="card plan-<?php echo $plan['id']; ?> col-lg-3 col-md-4 col-sm-5 col-6 text-center p-1">
                <div class="card-header fs-6 p-1">
                    <?php echo $plan['plan_name']; ?> $<?php echo number_format($price_per_day, 2); ?> per day
                </div>
                <div class="card-body d-inline-flex flex-row justify-content-between">
                    <h5 class="card-title d-inline-block">
                        $<?php echo $plan['price']; ?> | <?php echo $plan['duration_days']; ?> days
                    </h5>
                    <div class="dropdown d-inline-block">
                        <button class="btn btn-secondary bg-transparent text-white border-0 dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <!-- Edit Option -->
                            <li>
                                <a class="dropdown-item" href="#"
                                   onclick='openEditModal(<?php echo json_encode($plan['id']); ?>, <?php echo json_encode($plan['plan_name']); ?>, <?php echo json_encode($plan['price']); ?>, <?php echo json_encode($plan['duration_days']); ?>, <?php echo json_encode($plan['description']); ?>)'>
                                    <i class="bi bi-pencil-square"></i> Edit Plan
                                </a>
                            </li>
                            <!-- Delete Option -->
                            <li onclick="deleteSubscriptionPlan(<?php echo $plan['id']; ?>)">
                                <a class="dropdown-item" href="#">
                                    <i class="bi bi-trash3-fill"></i> Delete Plan
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Add Subscription Modal -->
    <div class="modal fade" id="addSubscriptionModal" tabindex="-1" aria-labelledby="addSubscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-6" id="addSubscriptionModalLabel">New Subscription</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="POST" action="" id="addSubscription">
                        <div class="mb-2">
                            <label for="add-plan-name" class="col-form-label">Name:</label>
                            <input type="text" class="form-control" required name="plan_name" id="add-plan-name">
                        </div>
                        <div class="mb-2">
                            <label for="add-price" class="col-form-label">Price($):</label>
                            <input type="number" class="form-control" required name="price" id="add-price">
                        </div>
                        <div class="mb-2">
                            <label for="add-duration-days" class="col-form-label">Duration (Business Days):</label>
                            <input type="number" class="form-control" required name="duration_days" id="add-duration-days">
                        </div>
                        <div class="mb-2">
                            <label for="add-description" class="col-form-label">Description:</label>
                            <textarea class="form-control" name="description" id="add-description"></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary add_sub fs-6">Add</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Subscription Modal -->
    <div class="modal fade" id="editSubscriptionModal" tabindex="-1" aria-labelledby="editSubscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-6" id="editSubscriptionModalLabel">Edit Subscription</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Edit form; note the hidden input for plan_id -->
                    <form id="editSubscriptionForm">
                        <input type="hidden" name="plan_id" id="edit-plan-id">
                        <div class="mb-2">
                            <label for="edit-plan-name" class="col-form-label">Name:</label>
                            <input type="text" class="form-control" required name="plan_name" id="edit-plan-name">
                        </div>
                        <div class="mb-2">
                            <label for="edit-price" class="col-form-label">Price($):</label>
                            <input type="number" class="form-control" required name="price" id="edit-price">
                        </div>
                        <div class="mb-2">
                            <label for="edit-duration-days" class="col-form-label">Duration (Business Days):</label>
                            <input type="number" class="form-control" required name="duration_days" id="edit-duration-days">
                        </div>
                        <div class="mb-2">
                            <label for="edit-description" class="col-form-label">Description:</label>
                            <textarea class="form-control" name="description" id="edit-description"></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary edit_sub fs-6">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>
</div>
</div>

<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Include SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    // Function to delete a subscription plan via AJAX using SweetAlert2 for confirmation and alerts
    const deleteSubscriptionPlan = (plan_id) => {
        Swal.fire({
            title: 'Are you sure?',
            text: "This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= BASE_URL?>/php_files/admin/manage_subscription_plan.php',
                    type: 'POST',
                    data: {
                        action: 'delete',
                        plan_id: plan_id
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            $('.plan-' + plan_id).remove();
                            Swal.fire({
                                title: 'Deleted!',
                                text: 'Plan has been deleted.',
                                icon: 'success'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Error deleting plan',
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        Swal.fire({
                            title: 'AJAX Error!',
                            text: 'Status: ' + status + ', Error: ' + error,
                            icon: 'error'
                        });
                        console.error('AJAX Error: ' + status + ' ' + error);
                    }
                });
            }
        });
    };

    // Function to open the edit modal and prefill the form with the plan data
    const openEditModal = (plan_id, plan_name, price, duration_days, description) => {
        $('#edit-plan-id').val(plan_id);
        $('#edit-plan-name').val(plan_name);
        $('#edit-price').val(price);
        $('#edit-duration-days').val(duration_days);
        $('#edit-description').val(description);
        new bootstrap.Modal(document.getElementById('editSubscriptionModal')).show();
    };

    // AJAX for adding a subscription (using the addSubscription form)
    $('#addSubscription').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission
        var formData = $(this).serialize();
        $.ajax({
            url: '<?= BASE_URL?>/php_files/admin/add_subscription.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success'){
                    $('#addSubscriptionModal').modal('toggle');
                    Swal.fire({
                        title: 'Success!',
                        text: 'Subscription added successfully!',
                        icon: 'success'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Error adding subscription',
                        icon: 'error'
                    });
                }
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'AJAX Error!',
                    text: 'Status: ' + status + ', Error: ' + error,
                    icon: 'error'
                });
                console.error('AJAX Error: ' + status, error);
            }
        });
    });

    // AJAX for editing a subscription plan
    $('#editSubscriptionForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission
        var formData = $(this).serialize() + '&action=edit';
        $.ajax({
            url: '<?= BASE_URL?>/php_files/admin/manage_subscription_plan.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success'){
                    $('#editSubscriptionModal').modal('toggle');
                    Swal.fire({
                        title: 'Success!',
                        text: 'Plan updated successfully!',
                        icon: 'success'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Error editing plan',
                        icon: 'error'
                    });
                }
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'AJAX Error!',
                    text: 'Status: ' + status + ', Error: ' + error,
                    icon: 'error'
                });
                console.error('AJAX Error: ' + status + ' ' + error);
            }
        });
    });
</script>

</body>
</html>
