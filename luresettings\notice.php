<?php
session_start(); // Start the session to access user_id from session

// Check if the request content type is JSON
if ($_SERVER['CONTENT_TYPE'] !== 'application/json') {
    die(json_encode(['success' => false, 'message' => 'Invalid content type. Expected JSON.']));
}

// Database connection and initialization
$config = include 'extractedconfig.php'; // Assuming the extractedconfig.php contains DB connection parameters

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

$conn = new mysqli($host, $username, $password, $dbname);

if ($conn->connect_error) {
    die(json_encode(['success' => false, 'message' => 'Connection failed: ' . $conn->connect_error]));
}

// Check if the notice_history table exists
$tableCheckQuery = "SHOW TABLES LIKE 'notice_history'";
$result = $conn->query($tableCheckQuery);

if ($result->num_rows == 0) {
    // Create the table if it does not exist
    $createTableQuery = "
        CREATE TABLE notice_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            message TEXT NOT NULL
        );
    ";

    if ($conn->query($createTableQuery) !== TRUE) {
        die(json_encode(['success' => false, 'message' => 'Error creating table: ' . $conn->error]));
    }
}

// Check for user authentication
if (!isset($_SESSION['user_id'])) {
    die(json_encode(['success' => false, 'message' => 'User not authenticated']));
}

// Get JSON input and decode
$data = json_decode(file_get_contents('php://input'), true);

// Validate the notice input
if (!isset($data['notice']) || empty(trim($data['notice']))) {
    die(json_encode(['success' => false, 'message' => 'Notice message cannot be empty. Please provide a valid notice.']));
}

$noticeMessage = trim($data['notice']); // Remove unnecessary whitespace
$user_id = $_SESSION['user_id'];

// Use prepared statements to prevent SQL injection
$checkNoticeQuery = "SELECT id FROM notice_history WHERE user_id = ?";
$stmtCheck = $conn->prepare($checkNoticeQuery);
$stmtCheck->bind_param("i", $user_id);
$stmtCheck->execute();
$stmtCheck->store_result();

if ($stmtCheck->num_rows > 0) {
    // If the notice already exists for this user, update it
    $updateNoticeQuery = "UPDATE notice_history SET message = ? WHERE user_id = ?";
    $stmtUpdate = $conn->prepare($updateNoticeQuery);
    $stmtUpdate->bind_param("si", $noticeMessage, $user_id);

    if ($stmtUpdate->execute()) {
        echo json_encode(['success' => true, 'message' => 'Notice updated successfully.']);
    } else {
        error_log("Error updating notice: " . $stmtUpdate->error);
        echo json_encode(['success' => false, 'message' => 'An error occurred while updating the notice. Please try again later.']);
    }

    $stmtUpdate->close();
} else {
    // If the notice does not exist for this user, insert it
    $insertNoticeQuery = "INSERT INTO notice_history (user_id, message) VALUES (?, ?)";
    $stmtInsert = $conn->prepare($insertNoticeQuery);
    $stmtInsert->bind_param("is", $user_id, $noticeMessage);

    if ($stmtInsert->execute()) {
        echo json_encode(['success' => true, 'message' => 'Notice added successfully.']);
    } else {
        error_log("Error inserting notice: " . $stmtInsert->error);
        echo json_encode(['success' => false, 'message' => 'An error occurred while adding the notice. Please try again later.']);
    }

    $stmtInsert->close();
}

$stmtCheck->close();
$conn->close();
?>
