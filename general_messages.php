<?php
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');

// Fetch messages from the database
$sql = "SELECT * FROM general_messages ORDER BY created_at DESC";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

$encryptionKey = 'RaccoonO365'; // Encryption key for decryption

function decryptMessage($encryptedMessage, $key) {
    list($encryptedData, $iv) = explode('::', $encryptedMessage);
    return openssl_decrypt($encryptedData, 'aes-256-cbc', $key, 0, hex2bin($iv));
}

?>

<?php require('assets/header.php') ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Chat Interface</title>
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 900px;
            margin: 30px auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: #1d3557;
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 1.8em;
            font-weight: 600;
        }

        #chat-window {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: linear-gradient(180deg, #ffffff 0%, #f7f9fc 100%);
        }

        #chat-window::-webkit-scrollbar {
            width: 10px;
        }

        #chat-window::-webkit-scrollbar-thumb {
            background-color: #1d3557;
            border-radius: 10px;
        }

        .message {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-end;
        }

        .message img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
        }

        .message.start img {
            order: 1;
        }

        .bubble {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            font-size: 1em;
            line-height: 1.5;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .bubble::after {
            content: "";
            position: absolute;
            width: 12px;
            height: 12px;
            background: inherit;
            transform: rotate(45deg);
        }

        .bubble.start {
            background-color: #e9f5fc;
            color: #1d3557;
            align-self: flex-start;
        }

        .bubble.start::after {
            bottom: 10px;
            left: -6px;
        }

        .bubble.end {
            background-color: #1d3557;
            color: #ffffff;
            align-self: flex-end;
        }

        .bubble.end::after {
            bottom: 10px;
            right: -6px;
        }

        .bubble small {
            display: block;
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 5px;
        }

        .footer {
            background-color: #f7f9fc;
            padding: 15px;
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>General Messages</h1>
        </div>
        <div id="chat-window">
            <?php foreach ($messages as &$message): ?>
                <div class="message <?php echo ($message['is_user']) ? 'end' : 'start'; ?>">
                    <?php if (!$message['is_user']): ?>
                        <img src="path/to/avatar.png" alt="User Avatar">
                    <?php endif; ?>
                    <div class="bubble <?php echo ($message['is_user']) ? 'end' : 'start'; ?>">
                        <?php echo htmlspecialchars(decryptMessage($message['message'], $encryptionKey)); ?>
                        <small><?php echo $message['created_at']; ?></small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="footer">
            This is a read-only channel.
        </div>
    </div>
</body>
</html>
