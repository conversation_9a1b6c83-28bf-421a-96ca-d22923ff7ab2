<?php
session_start(); // Start the session to access user_id from session

// Database connection and initialization
$config = include 'extractedconfig.php'; // Include the extracted config

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    echo json_encode(['success' => false, 'message' => 'Connection failed: ' . $conn->connect_error]);
    exit;
}

// Check if the notice_history table exists
$tableCheckQuery = "SHOW TABLES LIKE 'notice_history'";
$result = $conn->query($tableCheckQuery);

if ($result->num_rows == 0) {
    echo json_encode(['success' => false, 'message' => 'Table does not exist']);
    exit;
}

// Fetch notices for the current user
$user_id = $_SESSION['user_id'];
$query = "SELECT message FROM notice_history WHERE user_id = ?"; 

// Prepare statement
$stmt = $conn->prepare($query);
if (!$stmt) {
    echo json_encode(['success' => false, 'message' => 'Statement preparation failed: ' . $conn->error]);
    exit;
}

// Bind parameter
$stmt->bind_param("i", $user_id);

// Execute statement
$stmt->execute();

// Fetch results
$result = $stmt->get_result();
$notices = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $inotices = $row['message']; // Collect all messages
    }
    echo json_encode(['success' => true, 'message' => $inotices]);
} else {
    echo json_encode(['success' => true, 'message' => null]); // No notices found
}

// Close statement and connection
$stmt->close();
$conn->close();
?>
