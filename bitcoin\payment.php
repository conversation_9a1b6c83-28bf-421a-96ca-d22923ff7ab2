<?php
session_start();
$config = include 'sextractedconfig.php';

// Get the user ID from the session
$userId = $_SESSION['user_id'] ?? null;
if (empty($userId)) {
    header("Location: ../logout");
    exit();
}



function getRequestHeaders() {
    $headers = [];
    foreach ($_SERVER as $name => $value) {
        if (strpos($name, 'HTTP_') === 0) {
            $header = str_replace('HTTP_', '', $name);
            $header = str_replace('_', '-', strtolower($header));
            $headers[$header] = $value;
        }
    }
    return $headers;
}

$headers = getRequestHeaders();



$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];
$cryptoType = "bitcoin";

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Could not connect to the database $dbname :" . $e->getMessage());
}

function getBTCtoUSD() {
    $url = "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($response, true);
    return $data['bitcoin']['usd'] ?? 0;
}

function checkBitcoinTransaction($txid, $walletAddress) {
    $url = "https://blockchain.info/rawtx/$txid";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    $transaction = json_decode($response, true);
    if (!isset($transaction['hash'])) {
        return "Transaction not found or invalid txid.";
    }
    $filteredOutputs = array_filter($transaction['out'], function ($output) use ($walletAddress) {
        return isset($output['addr']) && $output['addr'] === $walletAddress;
    });
    return [
        'hash' => $transaction['hash'],
        'block_height' => $transaction['block_height'],
        'time' => $transaction['time'],
        'outputs' => $filteredOutputs,
    ];
}

function handleTransaction($txid, $userId, $remainingAmount, $btcAmount, $transactionTime, $cryptoType) {
    global $db;
    $stmt = $db->prepare("SELECT COUNT(*) FROM bitcointransaction WHERE transaction_hash = :transactionHash");
    $stmt->execute(['transactionHash' => $txid]);
    $transactionExists = $stmt->fetchColumn();
    if ($transactionExists > 0) {
        return json_encode(["error" => "The transaction hash provided has already been used to fund your RaccoonO365 wallet. Check your wallet balance."]);
    }

    // Convert transaction time (Unix timestamp) to date format without time
    $formattedDate = date('Y-m-d', $transactionTime);

    if ($remainingAmount >= 0.01) {
        autoFundWallet($userId, $remainingAmount, $cryptoType);
        $stmt = $db->prepare(
            "INSERT INTO bitcointransaction (transaction_hash, user_id, total_received_crypto, total_received_usd, transaction_date, crypto_type, status) 
            VALUES (?, ?, ?, ?, ?, ?, 'confirmed')"
        );
        $stmt->execute([$txid, $userId, $btcAmount, $remainingAmount, $formattedDate, $cryptoType]);
        return json_encode([
            'success' => "Payment confirmed and wallet funded with the sum of $$remainingAmount using $txid.",
            'data' => [
                'transaction_hash' => $txid,
                'total_received_crypto' => $btcAmount,
                'total_received_usd' => $remainingAmount,
                'transaction_date' => $formattedDate, // Updated format without time
                'crypto_type' => ucfirst($cryptoType),
            ]
        ]);
    }
}

function autoFundWallet($userId, $remainingAmount, $cryptoType = 'USD') {
    global $db;
    try {
        $db->beginTransaction();
        $query = $db->prepare("SELECT balance FROM wallet WHERE user_id = :userId");
        $query->execute(['userId' => $userId]);
        $wallet = $query->fetch();
        if ($wallet) {
            $newBalance = $wallet['balance'] + $remainingAmount;
            $updateQuery = $db->prepare("UPDATE wallet SET balance = :newBalance WHERE user_id = :userId");
            $updateQuery->execute(['newBalance' => $newBalance, 'userId' => $userId]);
        } else {
            $insertQuery = $db->prepare("INSERT INTO wallet (user_id, balance) VALUES (:userId, :amount)");
            $insertQuery->execute(['userId' => $userId, 'amount' => $remainingAmount]);
        }
        $transactionQuery = $db->prepare(
            "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
            VALUES (:userId, :cryptoType, :amount, :transactionId, 'credit', 'completed')"
        );
        $transactionQuery->execute([
            'userId' => $userId,
            'cryptoType' => $cryptoType,
            'amount' => $remainingAmount,
            'transactionId' => uniqid('txn_')
        ]);
        $db->commit();
        return json_encode([
            'success' => true,
            'message' => "Wallet funded successfully with the sum of $$remainingAmount in $cryptoType."
        ]);
    } catch (Exception $e) {
        $db->rollBack();
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

$processingFeePercentage = 3.21;
// $txid = "dc4dce07d7ac94b7e30f58571b8b59abd82aa85f311574da4b6dcf6f12b0a16c";

$txid = $headers['transaction-hash'] ?? '';

// Get bitcoin_wallet for the signed-in user
$sql = "SELECT bitcoin_wallet FROM user_profiles WHERE user_id = ?";
$stmt = $db->prepare($sql);
$stmt->execute([$userId]);
$bitcoin_wallet = $stmt->fetchColumn();

if (!$bitcoin_wallet) {
    die(json_encode(["error" => "Bitcoin wallet not found for user."]));
}



$walletAddress = $GLOBALS['bitcoin_wallet'];


$btcToUsd = getBTCtoUSD();
$transactionDetails = checkBitcoinTransaction($txid, $walletAddress);

if (is_array($transactionDetails)) {
    foreach ($transactionDetails['outputs'] as $output) {
        $btcAmount = $output['value'] / 100000000;
        $usdAmount = $btcAmount * $btcToUsd;
        $feeAmount = ($processingFeePercentage / 100) * $usdAmount;
        $remainingAmount = $usdAmount - $feeAmount;
        $response = handleTransaction($txid, $userId, $remainingAmount, $btcAmount, $transactionDetails['time'], $cryptoType);
        header('Content-Type: application/json');
        echo $response;
    }
} else {
    echo "Error: " . $transactionDetails;
}



?>
