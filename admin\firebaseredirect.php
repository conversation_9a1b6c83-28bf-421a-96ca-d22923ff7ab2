<?php
// Include the database connection file
require_once __DIR__ . '/../php_files/config.php';

// Function to create the table if it doesn't exist
function setupDatabase($pdo) {
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS firebase_urls (
            id INT AUTO_INCREMENT PRIMARY KEY,
            url VARCHAR(255) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB;
    ";
    $pdo->exec($createTableQuery);
}

// Call the setup function
setupDatabase($pdo);

// Function to prevent duplicate entries
function checkDuplicate($pdo, $url) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM firebase_urls WHERE url = :url");
    $stmt->execute(['url' => $url]);
    return $stmt->fetchColumn() > 0;
}

// Handle form actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'add') {
            $url = trim($_POST['url']);
            if (!empty($url)) {
                if (checkDuplicate($pdo, $url)) {
                    echo json_encode(['status' => 'error', 'message' => 'URL already exists']);
                } else {
                    $stmt = $pdo->prepare("INSERT INTO firebase_urls (url) VALUES (:url)");
                    $stmt->execute(['url' => $url]);
                    echo json_encode(['status' => 'success', 'message' => 'URL added successfully']);
                }
            } else {
                echo json_encode(['status' => 'error', 'message' => 'URL cannot be empty']);
            }
        } elseif ($_POST['action'] === 'edit') {
            $id = (int)$_POST['id'];
            $url = trim($_POST['url']);
            if (!empty($url)) {
                if (checkDuplicate($pdo, $url)) {
                    echo json_encode(['status' => 'error', 'message' => 'URL already exists']);
                } else {
                    $stmt = $pdo->prepare("UPDATE firebase_urls SET url = :url WHERE id = :id");
                    $stmt->execute(['url' => $url, 'id' => $id]);
                    echo json_encode(['status' => 'success', 'message' => 'URL updated successfully']);
                }
            } else {
                echo json_encode(['status' => 'error', 'message' => 'URL cannot be empty']);
            }
        } elseif ($_POST['action'] === 'delete') {
            $id = (int)$_POST['id'];
            $stmt = $pdo->prepare("DELETE FROM firebase_urls WHERE id = :id");
            $stmt->execute(['id' => $id]);
            echo json_encode(['status' => 'success', 'message' => 'URL deleted successfully']);
        }
    }
    exit;
}

// Fetch all URLs for display
$urls = $pdo->query("SELECT * FROM firebase_urls ORDER BY id DESC")->fetchAll(PDO::FETCH_ASSOC);
?>

<?php require '../assets/admin_header.php'; ?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Glitch URLs</title>
     <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
       
        .container {
            max-width: 600px;
            margin: auto;
           
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            font-weight: bold;
        }
        input[type="text"] {
           width: 86%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        .actions button {
            margin-right: 5px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Manage Firebase URLs</h1>
    <div class="form-group">
        <label for="url">Add New URL:</label>
        <input type="text" id="url" placeholder="Enter Firebase URL">
        <button id="add-url">Add URL</button>
    </div>
    <table>
        <thead>
        <tr>
            <th>ID</th>
            <th>URL</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody id="url-list">
        <?php foreach ($urls as $url): ?>
            <tr data-id="<?= $url['id'] ?>">
                <td><?= $url['id'] ?></td>
                <td class="url-text"><?= $url['url'] ?></td>
                <td class="actions">
                    <button class="edit">Edit</button>
                    <button class="delete">Delete</button>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function () {
        // Add URL
        $('#add-url').on('click', function () {
            const url = $('#url').val();
            if (!url) {
                Swal.fire('Error', 'URL cannot be empty', 'error');
                return;
            }
            $.post('', {action: 'add', url: url}, function (response) {
                response = JSON.parse(response);
                Swal.fire(response.status === 'success' ? 'Success' : 'Error', response.message, response.status);
                if (response.status === 'success') location.reload();
            });
        });

        // Edit URL
        $('.edit').on('click', function () {
            const row = $(this).closest('tr');
            const id = row.data('id');
            const currentUrl = row.find('.url-text').text();

            Swal.fire({
                title: 'Edit URL',
                input: 'text',
                inputValue: currentUrl,
                showCancelButton: true,
                confirmButtonText: 'Save',
            }).then((result) => {
                if (result.isConfirmed) {
                    const newUrl = result.value;
                    $.post('', {action: 'edit', id: id, url: newUrl}, function (response) {
                        response = JSON.parse(response);
                        Swal.fire(response.status === 'success' ? 'Success' : 'Error', response.message, response.status);
                        if (response.status === 'success') location.reload();
                    });
                }
            });
        });

        // Delete URL
        $('.delete').on('click', function () {
            const row = $(this).closest('tr');
            const id = row.data('id');
            Swal.fire({
                title: 'Are you sure?',
                text: 'You won\'t be able to revert this!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, delete it!',
            }).then((result) => {
                if (result.isConfirmed) {
                    $.post('', {action: 'delete', id: id}, function (response) {
                        response = JSON.parse(response);
                        Swal.fire(response.status === 'success' ? 'Deleted!' : 'Error', response.message, response.status);
                        if (response.status === 'success') location.reload();
                    });
                }
            });
        });
    });
</script>
</body>
</html>