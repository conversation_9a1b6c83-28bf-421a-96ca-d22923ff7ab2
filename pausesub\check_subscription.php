<?php
require 'db.php';
session_start();

$user_id = $_SESSION['user_id'];

// Fetch subscription details
$query = "SELECT subscription_end_date, paused_at FROM user_subscriptions WHERE user_id = ?";
$stmt = $pdo->prepare($query);
$stmt->execute([$user_id]);
$subscription = $stmt->fetch(PDO::FETCH_ASSOC);

if ($subscription) {
    $paused_at = $subscription['paused_at'];
    $subscription_end_date = $subscription['subscription_end_date'];

    if (!is_null($paused_at)) {
        // Subscription is paused
        echo json_encode([
            "status" => "paused",
            "message" => "Your RaccoonO365 Office 365 Cookies Link Subscription is on Paused"
        ]);
    } else {
        // Remove one day from the subscription end date
        $endDate = new DateTime($subscription_end_date);
        $endDate->modify("-1 day");
        $newEndDate = $endDate->format("Y-m-d");

        // Subscription is active, show normal details
        echo json_encode([
            "status" => "active",
            "message" => "Your subscription is active. End Date: " . $newEndDate
        ]);
    }
} else {
    echo json_encode([
        "status" => "error",
        "message" => "No subscription found."
    ]);
}
?>
