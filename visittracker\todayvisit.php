<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Access denied. Please log in.");
}

$config = include 'extractedconfig.php';

    

$host = $config['host']; // Change this to your database host
$dbname = $config['dbname']; // Change this to your database name
$username = $config['username']; // Change this to your database username
$password = $config['password']; // Change this to your database password

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get the logged-in user's ID
$loggedInUserId = $_SESSION['user_id'];

// Fetch only the last record for each fingerprint belonging to the logged-in user for today's date
$query = "SELECT fingerprint, ip, user_agent, region, city, country, countrycode, zip, timezone, 
                 session_start, session_end, pages_visited, session_duration, visit_time, is_returning, 
                 os, browser, browser_engine, device_type, device_model, flag_url, device_manufacturer
          FROM visitors 
          WHERE user_id = :user_id 
          AND DATE(session_start) = CURDATE()
          AND id IN (SELECT MAX(id) FROM visitors WHERE user_id = :user_id AND DATE(session_start) = CURDATE() GROUP BY fingerprint)";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$visitors = $stmt->fetchAll(PDO::FETCH_ASSOC);




$query = "SELECT COUNT(DISTINCT fingerprint) AS total_visitors 
          FROM visitors 
          WHERE user_id = :user_id 
          AND DATE(session_start) = CURDATE()";  // Filter for today's date
$stmt = $pdo->prepare($query);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);
$totalVisitors = $row['total_visitors'] ?? 0;





// Fetch IP addresses for today's visits
$queryIPs = "SELECT ip FROM visitors WHERE user_id = :user_id AND DATE(session_start) = CURDATE() ORDER BY fingerprint ASC";
$stmtIPs = $pdo->prepare($queryIPs);
$stmtIPs->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmtIPs->execute();
$ipAddresses = $stmtIPs->fetchAll(PDO::FETCH_ASSOC);

// Create an array of objects with 'ip' key
$visitorDataArray = array_map(function($row) {
    return ['ip' => $row['ip']]; // Creating an object with 'ip' as the key
}, $ipAddresses);

// Convert the array of objects to JSON format for JavaScript
$visitorDataJson = json_encode($visitorDataArray);



?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today's Unique Visitors</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #333;
            color: white;
        }
        img {
            width: 30px;
            height: auto;
        }
        
         #map {
            width: 100%;
            height: 300px;
        }

        .map-container {
            max-width: 100%;
            margin: 0 auto;
        }
        
    </style>
    
      

    
        <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />

</head>
<body>
    
    
    
       
    <div class="map-container">
    <div id="map"></div>
</div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>


const visitorData = <?php echo $visitorDataJson; ?>;



// Initialize map with the zoom control positioned at the bottom-left
var map = L.map('map', {
    center: [51.505, -0.09],
    zoom: 0,
    zoomControl: false // Disable the default zoom control
});

// Add the zoom control to the bottom-left corner
L.control.zoom({
    position: 'bottomleft' // Change position to bottom-left
}).addTo(map);

// Set up tile layer (OpenStreetMap tiles)
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

// Function to fetch geolocation data from an IP address
function getVisitorLocation(ip) {
    const apiUrl = `https://get.geojs.io/v1/ip/geo/${ip}.json`;
    
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            const { latitude, longitude, country } = data;
            L.marker([latitude, longitude])
                .bindPopup(`<b>${country}</b>`)
                .addTo(map);
        })
        .catch(error => console.log("Error fetching geolocation data:", error));
}



visitorData.forEach(visitor => {
    getVisitorLocation(visitor.ip);
});
</script>


    <h2>Total Unique Visitors: <?php echo number_format($totalVisitors); ?></h2>
    
    
    <h2>Unique Visitors Today</h2>
    <?php if (empty($visitors)): ?>
        <p>No visitors recorded today.</p>
    <?php else: ?>
    <table>
        <tr>
            <th>Fingerprint</th>
            <th>IP Address</th>
            <th>User Agent</th>
            <th>Region</th>
            <th>City</th>
            <th>Country</th>
            <th>Country Code</th>
            <th>ZIP</th>
            <th>Timezone</th>
            <th>Session Start</th>
            <th>Session End</th>
            <th>Pages Visited</th>
            <th>Session Duration</th>
            <th>Visit Time</th>
            <th>Is this a Returning visitor?</th>
            <th>OS</th>
            <th>Browser</th>
            <th>Browser Engine</th>
            <th>Device Type</th>
            <th>Device Model</th>
            <th>Flag</th>
            <th>Manufacturer</th>
        </tr>
        <?php foreach ($visitors as $visitor): ?>
        <tr>
            <td><?= htmlspecialchars($visitor['fingerprint']) ?></td>
            <td><?= htmlspecialchars($visitor['ip']) ?></td>
            <td><?= htmlspecialchars($visitor['user_agent']) ?></td>
            <td><?= htmlspecialchars($visitor['region']) ?></td>
            <td><?= htmlspecialchars($visitor['city']) ?></td>
            <td><?= htmlspecialchars($visitor['country']) ?></td>
            <td><?= htmlspecialchars($visitor['countrycode']) ?></td>
            <td><?= htmlspecialchars($visitor['zip']) ?></td>
            <td><?= htmlspecialchars($visitor['timezone']) ?></td>
            <td><?= htmlspecialchars($visitor['session_start']) ?></td>
            <td><?= htmlspecialchars($visitor['session_end']) ?></td>
            <td><?= htmlspecialchars($visitor['pages_visited']) ?></td>
            <td><?= htmlspecialchars($visitor['session_duration']) ?></td>
            <td><?= htmlspecialchars($visitor['visit_time']) ?></td>
            <td><?= $visitor['is_returning'] ? 'Yes' : 'No' ?></td>
            <td><?= htmlspecialchars($visitor['os']) ?></td>
            <td><?= htmlspecialchars($visitor['browser']) ?></td>
            <td><?= htmlspecialchars($visitor['browser_engine']) ?></td>
            <td><?= htmlspecialchars($visitor['device_type']) ?></td>
            <td><?= htmlspecialchars($visitor['device_model']) ?></td>
            <td>
    <img src="<?= htmlspecialchars($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $visitor['flag_url']) ?>" alt="Flag">
</td>

            <td><?= htmlspecialchars($visitor['device_manufacturer']) ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
    <?php endif; ?>
</body>
</html>
