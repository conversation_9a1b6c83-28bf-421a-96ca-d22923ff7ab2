<?php
header('Access-Control-Allow-Origin: *'); // Allow requests from any origin
header('Content-Type: application/json');

session_start(); // Ensure session_start() is called at the beginning
require 'db_connection.php'; // Ensure your database connection is established


 $userId = $_SESSION['user_id'];
 
 // Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}



// // Log the POST data
// function logPostData($data) {
//     $logFile = 'post_log.txt';
//     $timestamp = date('Y-m-d H:i:s');
//     $logEntry = "[$timestamp] Received POST Data: " . json_encode($data) . "\n";
//     file_put_contents($logFile, $logEntry, FILE_APPEND);
// }

// Business days calculation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true); // Decode incoming JSON
    // logPostData($input); // Log the received data

    if (isset($input['businessDays'])) {
        $businessDays = intval($input['businessDays']);
        $costPerDay = 28; // Cost per business day
        $minDays = 2;      // Minimum business days
        $maxDays = 7;     // Maximum business days

        if ($businessDays < $minDays) {
            echo json_encode(['success' => false, 'message' => "The minimum purchase is $minDays business days."]);
        } elseif ($businessDays > $maxDays) {
            echo json_encode(['success' => false, 'message' => "The maximum purchase is $maxDays business days."]);
        } else {
            $totalCost = $businessDays * $costPerDay;

            // Calculate the target date
            $currentDate = new DateTime();
            $daysAdded = 0;

            while ($daysAdded < $businessDays) {
                $currentDate->modify('+1 day');
                $dayOfWeek = $currentDate->format('N'); // 1 = Monday, 7 = Sunday
                if ($dayOfWeek >= 1 && $dayOfWeek <= 5) {
                    $daysAdded++;
                }
            }

            // Add 1 extra business day if needed
            $currentDate->modify('+1 day');
            while (!in_array($currentDate->format('N'), [1, 2, 3, 4, 5])) {
                $currentDate->modify('+1 day');
            }



            $subscriptionExpire = $currentDate->format('Y-m-d'); // Format expire date for storage

//   $result = [
//                 'total' => true,
//                 'message' => "Your total fee is $" . $totalCost
//             ];

            // Call the updateSubscription function
           
            $updateResult = updateSubscription($conn, $userId, $businessDays, $subscriptionExpire, $totalCost);


            // Append update result to the final response
            $result['updateSubscription'] = $updateResult;

            echo json_encode($result); // Final JSON response
            
            
        }
    } else {
        // echo json_encode(['total' => false, 'message' => 'Missing "businessDays" parameter.']);
    }
} else {
   
}

// Function to update subscription
function updateSubscription($conn, $userId, $businessDays, $subscriptionExpire, $totalCost) {
    // Define min and max business days
    $minBusinessDays = 2;
    $maxBusinessDays = 7;

    if ($businessDays < $minBusinessDays || $businessDays > $maxBusinessDays) {
        return ['success' => false, 'message' => 'The number of business days must be between ' . $minBusinessDays . ' and ' . $maxBusinessDays . '.'];
    }

    // Check if the user has ever subscribed
    $query = $conn->prepare("SELECT * FROM user_subscriptions WHERE user_id = ?");
    $query->bind_param("i", $userId);
    $query->execute();
    $rlt = $query->get_result();
    $subscription = $rlt->fetch_assoc();

    if (!$subscription) {
        return ['success' => false, 'message' => 'Please purchase our regular subscription plan in the menu before using this service. For users who might be struggling financially, our recharge option can be a lifesaver.'];
    }

    $startDate = new DateTime($subscription['subscription_start_date']);
    $endDate = new DateTime($subscription['subscription_end_date']);
    $currentDate = new DateTime();

    if ($endDate < $currentDate) {
        // Subscription expired, apply new end date
        $interval = new DateInterval('P' . $businessDays . 'D');
        $newEndDate = $startDate->add($interval)->format('Y-m-d');
    } else {
        // Extend current subscription
        $interval = new DateInterval('P' . $businessDays . 'D');
        $endDate->add($interval);
        $newEndDate = $endDate->format('Y-m-d');
    }

    // Fetch and update the wallet balance directly
    $balanceQuery = $conn->prepare("SELECT balance FROM wallet WHERE user_id = ?");
    $balanceQuery->bind_param("i", $userId);
    $balanceQuery->execute();
    $balanceResult = $balanceQuery->get_result();
    $balance = $balanceResult->fetch_assoc()['balance'];

    if ($balance <= 0) {
        return ['success' => false, 'message' => "Insufficient balance. Please fund your wallet. Total cost is $$totalCost."];
    } elseif ($balance < $totalCost) {
        return ['success' => false, 'message' => "Not enough balance. Please add more funds. Total cost is $$totalCost."];
    }

    // Deduct the total cost from the wallet
    $newBalance = $balance - $totalCost;
    $updateBalanceSQL = "UPDATE wallet SET balance = ? WHERE user_id = ?";
    $updateBalanceStmt = $conn->prepare($updateBalanceSQL);
    $updateBalanceStmt->bind_param("di", $newBalance, $userId);
    if (!$updateBalanceStmt->execute()) {
        return ['success' => false, 'message' => 'Failed to update wallet balance. Please try again.'];
    }

    // Update subscription only if balance is sufficient
    $updateSQL = "UPDATE user_subscriptions SET subscription_end_date = ? WHERE user_id = ?";
    $stmt = $conn->prepare($updateSQL);
    $stmt->bind_param("si", $newEndDate, $userId);
    if (!$stmt->execute()) {
        return ['success' => false, 'message' => 'Failed to update subscription. Please try again.'];
    }

    // Record wallet transaction
    $transactionSQL = "
    INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status, created_at)
    VALUES (?, 'Recharge', ?, ?, 'debit', 'completed', NOW())";
    $stmt = $conn->prepare($transactionSQL);
    $transactionId = 'txn_' . uniqid();
    $stmt->bind_param("isd", $userId, $totalCost, $transactionId);
    if (!$stmt->execute()) {
        return ['success' => false, 'message' => "Transaction failed. Please check your wallet balance or try again. Total cost is $$totalCost."];
    }

    return [
        'success' => true,
        'message' => "Congratulations! Your subscription has been successfully activated. You have successfully subscribed to keep your RaccoonO365 Suite online. Total cost is $$totalCost. Thank you!"
    ];
}



?>
