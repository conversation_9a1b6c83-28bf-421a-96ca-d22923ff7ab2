<?php


include 'php_files/db.php'; // Include your database connection file

header('Content-Type: application/json'); // Set content type to JSON

// Get user ID from the request parameters and sanitize
$user_id = filter_input(INPUT_GET, 'user_id', FILTER_SANITIZE_STRING);

if ($user_id === null || $user_id === false) {
    // Return a JSON response for an invalid user ID
    echo json_encode(['status' => 'error', 'message' => 'Invalid user ID']);
    exit;
}

try {
    // Prepare and execute query
    $sql = "SELECT background_image FROM user_profiles WHERE username = :user_id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['user_id' => $user_id]);

    // Fetch the result
    $background_image = $stmt->fetchColumn();

    // Close the statement (optional since PDO closes automatically at the end of the script)
    $stmt = null;

    // Prepare the response
    if ($background_image !== false) {
        // Return a JSON response with the background image
        echo json_encode(['status' => 'success', 'background_image' => $background_image]);
    } else {
        // Return a JSON response indicating no background image was found
        echo json_encode(['status' => 'error', 'message' => 'No background image found for the specified user.']);
    }

} catch (PDOException $e) {
    // Handle any errors
    echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
}
