<?php
require('db.php');
require('functions.php');

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    header("Location: ../logout.php");  // Redirects to Google
    exit;
}




// Define your encryption key
$encryptionKey = 'RaccoonO365'; // Replace this with a strong, secure key





// Encryption function
function encryptMessage($message, $key) {
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));
    return openssl_encrypt($message, 'aes-256-cbc', $key, 0, $iv) . '::' . bin2hex($iv);
}



// Function to create the required tables (only chats table in this case)
function createChatsTable($db) {
    try {
        // Create chats table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS chats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                username VARCHAR(255) NOT NULL,
                support_agent_id INT DEFAULT NULL,
                status ENUM('open', 'closed', 'pending') DEFAULT 'open',
                last_message TEXT NOT NULL,
                support_agentmessage TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,  -- Track if the message is read
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                closed_at TIMESTAMP DEFAULT NULL
            )
        ");
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}

$username = $_SESSION['username'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    global $pdo;
    $user_id = $_SESSION['user_id'];
    try {
        // Ensure message is sanitized (example using htmlspecialchars)
        $message = htmlspecialchars($_POST['message'], ENT_QUOTES, 'UTF-8');
        
        

 // Encrypt the message
    $message = encryptMessage($message, $encryptionKey);


        // Check if a support agent is assigned
        $support_agent_id = null; // Default value if no agent is assigned

        // Insert the message into the chats table
        $stmt = $pdo->prepare("INSERT INTO chats (user_id, username, support_agent_id, last_message, status, is_read) VALUES (?, ?, ?, ?, ?, ?)");
        
        // If no support agent is assigned, set status to 'pending'
        $status = $support_agent_id === null ? 'pending' : 'open';
        
        // Set is_read to false for new messages
        $is_read = false;

        $stmt->execute([$user_id, $username, $support_agent_id, $message, $status, $is_read]);

        echo json_encode(['status' => 'success']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
