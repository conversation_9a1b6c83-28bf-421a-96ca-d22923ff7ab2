<?php
header('Content-Type: application/json');

// Check if username is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['error' => 'Username parameter is required']);
    exit;
}

$username = $_GET['id'];

// Include configuration file for DB connection
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$usernameDB = $config['username'];
$passwordDB = $config['password'];

$conn = new mysqli($host, $usernameDB, $passwordDB, $dbname);

// Check connection
if ($conn->connect_error) {
    die(json_encode(['error' => 'Database connection failed: ' . $conn->connect_error]));
}

// Fetch user_id from user_profiles
$stmt = $conn->prepare("SELECT user_id FROM user_profiles WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $user_id = $row['user_id'];

    // Fetch lure_pause details
    $stmt2 = $conn->prepare("SELECT date_paused, duration, unpause_time FROM lure_pauses WHERE user_id = ?");
    $stmt2->bind_param("i", $user_id);
    $stmt2->execute();
    $result2 = $stmt2->get_result();

    if ($result2->num_rows > 0) {
        $lure_pause_details = $result2->fetch_assoc();

        // Fetch notice_history message
        $stmt3 = $conn->prepare("SELECT message FROM notice_history WHERE user_id = ?");
        $stmt3->bind_param("i", $user_id);
        $stmt3->execute();
        $result3 = $stmt3->get_result();

        if ($result3->num_rows > 0) {
            $notice_history = $result3->fetch_assoc();
        } else {
           
        }

        echo json_encode([
           
            'lure_pause' => $lure_pause_details,
            'notice_message' => $notice_history['message']
        ]);
    } else {
        
    }

    $stmt2->close();
} else {
    echo json_encode(['error' => 'User not found']);
}

$stmt->close();
$conn->close();
?>
