<?php
// Start a session
session_start();


// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database credentials
$host = $config['host'];        
$username = $config['username'];         
$password = $config['password'];             
$dbname = $config['dbname'];  
$charset = 'utf8mb4'; // Ensure this is explicitly set

$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
];

try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}


// Check if user is signed in
if (!isset($_SESSION['user_id'])) {
    die("Unauthorized access. Please log in.");
}

// Get signed-in user's ID
$user_id = $_SESSION['user_id'];

// Fetch Cloudflare API key and email for the signed-in user
$stmt = $pdo->prepare("SELECT cloudflareredirectemail, cloudflareredirectapi FROM user_profiles WHERE user_id = :user_id");
$stmt->execute(['user_id' => $user_id]);
$user = $stmt->fetch();

if (!$user || empty($user['cloudflareredirectemail']) || empty($user['cloudflareredirectapi'])) {
    die("Cloudflare API credentials not found for the signed-in user.");
}

$cf_email = $user['cloudflareredirectemail'];
$cf_apikey = $user['cloudflareredirectapi'];

// Security Level Enums
$SL_UNDER_ATTACK = 'under_attack';
$SL_ESSENTIALLY_OFF = 'essentially_off';

// Function to get all zone IDs
function get_zone_ids($cf_email, $cf_apikey) {
    $url = "https://api.cloudflare.com/client/v4/zones";
    $headers = [
        "X-Auth-Email: $cf_email",
        "X-Auth-Key: $cf_apikey",
        "Content-Type: application/json"
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);

    $zones = json_decode($response, true);
    $zone_ids = [];
    if (isset($zones['result'])) {
        foreach ($zones['result'] as $zone) {
            $zone_ids[] = $zone['id'];
        }
    }
    return $zone_ids;
}

// Function to toggle UAM
function toggle_uam($cf_email, $cf_apikey, $cf_zoneid, $security_level) {
    $url = "https://api.cloudflare.com/client/v4/zones/$cf_zoneid/settings/security_level";
    $headers = [
        "X-Auth-Email: $cf_email",
        "X-Auth-Key: $cf_apikey",
        "Content-Type: application/json"
    ];
    $data = json_encode(["value" => $security_level]);

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

    $response = curl_exec($ch);
    curl_close($ch);

    $response_data = json_decode($response, true);
    return isset($response_data['result']['value']) && $response_data['result']['value'] === $security_level;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['uam_toggle']) && $_POST['uam_toggle'] == 'on') {
        $security_level = $SL_UNDER_ATTACK;
        $toggle_message = "ScanShield Mode has been ENABLED.";
    } else {
        $security_level = $SL_ESSENTIALLY_OFF;
        $toggle_message = "ScanShield Mode has been DISABLED.";
    }

    $zone_ids = get_zone_ids($cf_email, $cf_apikey);
    foreach ($zone_ids as $zone_id) {
        toggle_uam($cf_email, $cf_apikey, $zone_id, $security_level);
    }

    // Unset sensitive credentials after use
    unset($cf_email, $cf_apikey);

    $_SESSION['toggle_message'] = $toggle_message;
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare UAM Toggle</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Include custom CSS -->
    <style>
        /* General Reset and Box Sizing */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fb;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
        }

        .container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 600px;
        }

        /* Button Styles */
        button {
            display: inline-block;
            padding: 15px 30px;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            background-color: #2980b9;
            color: #fff;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease-in-out;
        }

        button:hover {
            background-color: #3498db;
            transform: translateY(-3px);
        }

        button:active {
            background-color: #2980b9;
            transform: translateY(2px);
        }

        button:focus {
            outline: none;
        }

        /* Form Styling */
        form {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        /* Response Messages Styling */
        .response-container {
            margin-top: 30px;
            padding: 15px;
            background-color: #eaf2f8;
            border-radius: 8px;
            border: 1px solid #dfe6e9;
            font-size: 1rem;
            color: #2980b9;
        }

        /* SweetAlert2 Custom Styling */
        .swal2-popup {
            font-family: 'Arial', sans-serif !important;
            padding: 20px;
            border-radius: 8px;
            background-color: #2980b9;
        }

        .swal2-title {
            color: #fff !important;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mitigate Automated Scanners - BotBlocker</h1>
        <form method="post">
            <button type="submit" name="uam_toggle" value="on">Enable Cloudflare Shield</button>
            <button type="submit" name="uam_toggle" value="off">Disable Cloudflare Shield</button>
        </form>

        <?php
        if (isset($_SESSION['toggle_message'])) {
            echo "<script>
                Swal.fire({
                    icon: 'success',
                    title: '{$_SESSION['toggle_message']}',
                    showConfirmButton: false,
                    timer: 1500
                });
            </script>";
            unset($_SESSION['toggle_message']);
        }
        ?>
    </div>
</body>
</html>
