<?php
session_start();


// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./php_files/logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}


// Assuming the user is logged in and their user ID is stored in session
$user_id = $_SESSION['user_id']; // Replace with your actual method of retrieving the signed-in user's ID

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database credentials
$host = $config['host'];        
$username = $config['username'];         
$password = $config['password'];             
$dbname = $config['dbname']; 

// Create connection
$mysqli = new mysqli($host, $username, $password, $dbname);

// Check the connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}


// Default values for checkboxes
$aiBotsProtection = false;
$enableJs = false;
$fightMode = false;




// Get the current bot management settings from Cloudflare (this happens only once)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get Cloudflare email and API key from the user's profile
    $query = "SELECT cloudflareemail, cloudflareapikey FROM user_profiles WHERE user_id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->bind_result($cfEmail, $cfApiKey);
    $stmt->fetch();
    $stmt->close();

    // Fetch current settings from Cloudflare
    $result = getCloudflareBotManagementSettings($cfEmail, $cfApiKey);
    // Unset sensitive API credentials after use
    unset($cfEmail, $cfApiKey);

    // Assign fetched values to variables
    $aiBotsProtection = $result['ai_bots_protection'];
    $enableJs = $result['enable_js'];
    $fightMode = $result['fight_mode'];
}

// Process the form submission and update Cloudflare settings if it's a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get Cloudflare email and API key from the user's profile
    $query = "SELECT cloudflareemail, cloudflareapikey FROM user_profiles WHERE user_id = ?";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->bind_result($cfEmail, $cfApiKey);
    $stmt->fetch();
    $stmt->close();

    // Get the toggled settings
    $aiBotsProtection = isset($_POST['ai_bots_protection']) ? "block" : "disabled";
    $enableJs = isset($_POST['enable_js']) ? true : false;
    $fightMode = isset($_POST['fight_mode']) ? true : false;

    // If Fight Mode is enabled, enable JS automatically
    if ($fightMode) {
        $enableJs = true;
    }

    // Process the API call to update settings
    $result = updateBotManagementConfigForAllZones($cfEmail, $cfApiKey, $aiBotsProtection, $enableJs, $fightMode);

    // Return the result as JSON (feedback for SweetAlert)
    echo json_encode($result);
    exit();
}

function getCloudflareBotManagementSettings($cfEmail, $cfApiKey) {
    $url = "https://api.cloudflare.com/client/v4/zones";
    
    $headers = [
        "X-Auth-Email: {$cfEmail}",
        "X-Auth-Key: {$cfApiKey}",
        "Content-Type: application/json"
    ];

    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    if ($httpCode >= 200 && $httpCode < 300) {
        $zones = json_decode($response, true)['result'];
        // Assuming we fetch settings from the first zone, you can modify to suit your needs
        if (isset($zones[0])) {
            $zoneId = $zones[0]['id'];
            return fetchBotManagementSettingsForZone($zoneId, $cfEmail, $cfApiKey);
        }
    }
    return [
        'ai_bots_protection' => false,
        'enable_js' => false,
        'fight_mode' => false
    ];
}

function fetchBotManagementSettingsForZone($zoneId, $cfEmail, $cfApiKey) {
    $url = "https://api.cloudflare.com/client/v4/zones/{$zoneId}/bot_management";
    $headers = [
        "X-Auth-Email: {$cfEmail}",
        "X-Auth-Key: {$cfApiKey}",
        "Content-Type: application/json"
    ];

    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    if ($httpCode >= 200 && $httpCode < 300) {
        $settings = json_decode($response, true)['result'];
        return [
            'ai_bots_protection' => $settings['ai_bots_protection'] == 'block',
            'enable_js' => $settings['enable_js'] == true,
            'fight_mode' => $settings['fight_mode'] == true
        ];
    }

    return [
        'ai_bots_protection' => false,
        'enable_js' => false,
        'fight_mode' => false
    ];
}

function updateBotManagementConfigForAllZones($cfEmail, $cfApiKey, $aiBotsProtection, $enableJs, $fightMode) {
    $url = "https://api.cloudflare.com/client/v4/zones";
    
    $headers = [
        "X-Auth-Email: {$cfEmail}",
        "X-Auth-Key: {$cfApiKey}",
        "Content-Type: application/json"
    ];

    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    if ($httpCode >= 200 && $httpCode < 300) {
        $zones = json_decode($response, true)['result'];
        foreach ($zones as $zone) {
            $zoneId = $zone['id'];
            $result = updateBotManagementConfig($zoneId, $cfEmail, $cfApiKey, $aiBotsProtection, $enableJs, $fightMode);
            if ($result['success'] === false) {
                return $result; // Return first failure
            }
        }
        return ['success' => true, 'message' => 'Successfully updated all zones.'];
    }
    return ['success' => false, 'message' => 'Failed to fetch zones. HTTP code: ' . $httpCode];
}

function updateBotManagementConfig($zoneId, $cfEmail, $cfApiKey, $aiBotsProtection, $enableJs, $fightMode) {
    $url = "https://api.cloudflare.com/client/v4/zones/{$zoneId}/bot_management";
    $data = [
        "ai_bots_protection" => $aiBotsProtection,
        "enable_js" => $enableJs,
        "fight_mode" => $fightMode
    ];
    $headers = [
        "X-Auth-Email: {$cfEmail}",
        "X-Auth-Key: {$cfApiKey}",
        "Content-Type: application/json"
    ];

    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => "PUT",
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => $headers,
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    if ($httpCode >= 200 && $httpCode < 300) {
        return ['success' => true, 'message' => "Successfully updated bot management config"];
    }
    return ['success' => false, 'message' => "Failed to update bot management config"];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toggle Bot Management</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(to right, #00b4db, #0083b0);
            color: #fff;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column; /* Ensures header and form stack vertically */
            transition: background 0.3s ease;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-align: center;
            color: #fff;
            font-weight: 700;
        }

        form {
            background: rgba(0, 0, 0, 0.5);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
        }

        label {
            font-size: 1.2rem;
            margin-bottom: 10px;
            display: block;
            color: #ddd;
            transition: color 0.3s ease;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            transform: scale(1.5);
        }

        button {
            background-color: #00b4db;
            color: #fff;
            font-size: 1rem;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin-top: 20px;
        }

        button:hover {
            background-color: #0083b0;
            transform: scale(1.05);
        }

        button:active {
            transform: scale(0.98);
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Bot Defender Settings</h1>
        <form id="botSettingsForm" method="POST">
            <label for="ai_bots_protection">Enable AI Bot Protection:</label>
            <input type="checkbox" name="ai_bots_protection" id="ai_bots_protection" <?= $aiBotsProtection ? 'checked' : '' ?>>
            <br><br>

            <div id="jsContainer" style="display:none important!">
                <label for="enable_js">Enable Bot JavaScript Security:</label>
                <input type="checkbox" name="enable_js" id="enable_js" <?= $enableJs ? 'checked' : '' ?>>
            </div>
            
            <label for="fight_mode">Enable Bot Fight Mode:</label>
            <input type="checkbox" name="fight_mode" id="fight_mode" <?= $fightMode ? 'checked' : '' ?>>
            <br><br>
            <button type="submit" id="saveSettings">Save Settings</button>
        </form>
    </div>

   <script>
        // This JavaScript function automatically enables the "Enable JavaScript Challenge" when "Fight Mode" is checked
        function toggleFightMode() {
            const fightModeCheckbox = document.getElementById('fight_mode');
            const enableJsCheckbox = document.getElementById('enable_js');
            
            // If Fight Mode is checked, Enable JavaScript should also be checked
            if (fightModeCheckbox.checked) {
                enableJsCheckbox.checked = true;
                enableJsCheckbox.disabled = true; // Disable it so the user cannot uncheck it
            } else {
                enableJsCheckbox.disabled = false; // Re-enable it if Fight Mode is unchecked
            }
        }

        // Run the toggle function on page load to ensure correct initial state
        window.onload = function() {
            toggleFightMode();
        };
    
    
        document.getElementById('botSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            var formData = new FormData(this);
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    var result = JSON.parse(xhr.responseText);
                    Swal.fire({
                        title: result.success ? 'Success' : 'Error',
                        text: result.message,
                        icon: result.success ? 'success' : 'error'
                    });
                }
            };

            var queryString = new URLSearchParams(formData).toString();
            xhr.send(queryString);
        });

        document.getElementById('fight_mode').addEventListener('change', function() {
            var jsContainer = document.getElementById('jsContainer');
            if (this.checked) {
                document.getElementById('enable_js').checked = true;
                jsContainer.style.display = 'block';
            } else {
                jsContainer.style.display = 'none';
            }
        });
    </script>
</body>
</html>
