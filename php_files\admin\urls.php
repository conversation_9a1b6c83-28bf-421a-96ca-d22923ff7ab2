<?php
 require "../db.php";
 require "../functions.php";

 function addTrackUrls() {
    try {
        // Check if 'domains' is set and is an array
        if (isset($_POST['domains']) && is_array($_POST['domains'])) {
            global $pdo;

            // Prepare the insert statement
            $insertStmt = $pdo->prepare("
                INSERT INTO track_urls (url) VALUES (:url)
            ");

            // Loop through each domain in the POST data and insert it
            foreach ($_POST['domains'] as $domain) {
                // Bind the domain to the statement
                $insertStmt->bindParam(':url', $domain, PDO::PARAM_STR);
                $insertStmt->execute();
            }
            return ['status' => 'success', 'message' => 'Track Urls added successfully.'];
        } else {
            return ['status' => 'error', 'message' => 'No valid domains found to insert.'];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'An error occurred'];
    }
}

 function addBaseUrl() {
    try {
        // Check if 'domains' is set and is an array
        if (isset($_POST['domains']) && is_array($_POST['domains'])) {
            global $pdo;

            // Prepare the insert statement
            $insertStmt = $pdo->prepare("
                INSERT INTO base_urls (url) VALUES (:url)
            ");

            // Loop through each domain in the POST data and insert it
            foreach ($_POST['domains'] as $domain) {
                // Bind the domain to the statement
                $insertStmt->bindParam(':url', $domain, PDO::PARAM_STR);
                $insertStmt->execute();
            }
            return ['status' => 'success', 'message' => 'Base Urls added successfully.'];
        } else {
            return ['status' => 'error', 'message' => 'No valid domains found to insert.'];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'An error occurred'];
    }
}


 function addOpenUrl() {
    try {
        // Check if 'domains' is set and is an array
        if (isset($_POST['domains']) && is_array($_POST['domains'])) {
            global $pdo;

            // Prepare the insert statement
            $insertStmt = $pdo->prepare("
                INSERT INTO open_redirect_urls (url) VALUES (:url)
            ");

            // Loop through each domain in the POST data and insert it
            foreach ($_POST['domains'] as $domain) {
                // Bind the domain to the statement
                $insertStmt->bindParam(':url', $domain, PDO::PARAM_STR);
                $insertStmt->execute();
            }
            return ['status' => 'success', 'message' => 'Base Urls added successfully.'];
        } else {
            return ['status' => 'error', 'message' => 'No valid domains found to insert.'];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'An error occurred'];
    }
}

 
$response = ['status' => 'error', 'message' => 'Invalid action'];

if (isset($_GET['action']) && $_GET['action'] === 'trackUrl') {
    $response = addTrackUrls();
} else if ( isset($_GET['action']) && $_GET['action'] === 'baseUrl' ) {
    $response = addBaseUrl();
} else if ( isset($_GET['action']) && $_GET['action'] === 'openUrl' ) {
    $response = addOpenUrl();
}

header('Content-Type: application/json');
echo json_encode($response);