<?php


// Start session to manage user authentication
session_start();
// Set session cookie parameters first
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict'   // Mitigate CSRF attacks
]);


// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    header("Location: ../logout.php");  // Redirects to Google login or similar page
    exit;
}


// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];


try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // Auto-add `paused_at` column if missing
    $checkColumnQuery = "SHOW COLUMNS FROM subscribeopenredirect LIKE 'paused_at'";
    $stmt = $pdo->query($checkColumnQuery);

    if ($stmt->rowCount() == 0) {
        $alterTableQuery = "ALTER TABLE subscribeopenredirect ADD COLUMN paused_at DATE NULL";
        $pdo->exec($alterTableQuery);
    }
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>
