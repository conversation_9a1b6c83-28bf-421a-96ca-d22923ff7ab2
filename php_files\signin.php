<?php

require_once('functions.php');
require 'db.php';

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);

error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set the default timezone
    date_default_timezone_set('Africa/Lagos'); // Change to your timezone

    $usernameOrEmail = sanitize($_POST['username']);
    $pass = sanitize($_POST['password']);

    try {
        // Prepare the SQL query using PDO
        $get_email = "SELECT * FROM user_profiles WHERE email = :email OR username = :username";
        $stmt = $pdo->prepare($get_email);

        // Bind parameters
        $stmt->bindParam(':email', $usernameOrEmail);
        $stmt->bindParam(':username', $usernameOrEmail);

        // Execute the query
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        // Check if user data is found and verify the password
        if ($row) {
            if (password_verify($pass, $row['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $row['user_id'];
                $_SESSION['username'] = $row['username'];
                $_SESSION['email'] = $row['email'];
                $_SESSION['user_type'] = $row['user_type'];
                $_SESSION['email_log_unlocked'] = $row['email_log_unlocked'];

                if ($row['user_type'] !== 'superadmin' && $row['user_type'] !== 'admin') {
                    // Get the current date and time
                    $currentDateTime = date('l, jS \of F Y, g:ia');
                    saveNotification($row['user_id'], 'Login Alert', 'You logged in on ' . $currentDateTime);

                    $userAgent = $_SERVER['HTTP_USER_AGENT'];
                    $ipAddress = $_SERVER['REMOTE_ADDR'];

                    // Create the DateTime object for the formatted email date
                    $date = new DateTime("now", new DateTimeZone("Africa/Lagos"));
                    $formattedDate = $date->format('Y-n-j H:i (T)');
                    $to = $row['email'];
                    $subject = 'RaccoonO365 Account Sign-In Detected!';
                    
                    
                    
                    
                    
                    
                    
                    // Unicode for FontAwesome icons (Using emojis here)
$browserIcon = "🌐";  // Globe icon for browser
$osIcon = "💻";  // Laptop icon for OS
$ipIcon = "📱";  // Address card icon for IP
$dateIcon = "📅";  // Calendar icon for date

// Function to detect browser and OS (assuming you already have it)
function getBrowserDetails($userAgent) {
    $browser = "Unknown Browser";
    $os = "Unknown OS";

    // Detect browser
    if (strpos($userAgent, "Firefox") !== false) {
        $browser = "Mozilla Firefox";
    } elseif (strpos($userAgent, "Chrome") !== false && strpos($userAgent, "Safari") !== false) {
        $browser = "Google Chrome"; // Chrome includes Safari in the user agent
    } elseif (strpos($userAgent, "Safari") !== false && strpos($userAgent, "Chrome") === false) {
        $browser = "Apple Safari";
    } elseif (strpos($userAgent, "Edge") !== false) {
        $browser = "Microsoft Edge";
    } elseif (strpos($userAgent, "MSIE") !== false || strpos($userAgent, "Trident") !== false) {
        $browser = "Microsoft Internet Explorer";
    } elseif (strpos($userAgent, "Opera") !== false || strpos($userAgent, "OPR") !== false) {
        $browser = "Opera";
    } elseif (strpos($userAgent, "Vivaldi") !== false) {
        $browser = "Vivaldi";
    } elseif (strpos($userAgent, "Yandex") !== false) {
        $browser = "Yandex Browser";
    } elseif (strpos($userAgent, "Brave") !== false) {
        $browser = "Brave";
    } elseif (strpos($userAgent, "UC Browser") !== false) {
        $browser = "UC Browser";
    } elseif (strpos($userAgent, "Samsung Browser") !== false) {
        $browser = "Samsung Internet";
    }

    // Detect operating system
    if (strpos($userAgent, "Windows NT") !== false) {
        $os = "Windows";
    } elseif (strpos($userAgent, "Mac OS X") !== false) {
        $os = "MacOS";
    } elseif (strpos($userAgent, "Linux") !== false) {
        $os = "Linux";
    } elseif (strpos($userAgent, "Android") !== false) {
        $os = "Android";
    } elseif (strpos($userAgent, "iPhone") !== false || strpos($userAgent, "iPad") !== false) {
        $os = "iOS";
    } elseif (strpos($userAgent, "Windows Phone") !== false) {
        $os = "Windows Phone";
    } elseif (strpos($userAgent, "BlackBerry") !== false) {
        $os = "BlackBerry";
    } elseif (strpos($userAgent, "Ubuntu") !== false) {
        $os = "Ubuntu Linux";
    } elseif (strpos($userAgent, "Debian") !== false) {
        $os = "Debian Linux";
    } elseif (strpos($userAgent, "FreeBSD") !== false) {
        $os = "FreeBSD";
    } elseif (strpos($userAgent, "OpenBSD") !== false) {
        $os = "OpenBSD";
    } elseif (strpos($userAgent, "Solaris") !== false) {
        $os = "Solaris";
    }

    // Handle specific device detection
    if (strpos($userAgent, "Mobile") !== false) {
        $os .= " (Mobile)";
    } elseif (strpos($userAgent, "Tablet") !== false) {
        $os .= " (Tablet)";
    }

    return array($browser, $os);
}

// Function to get the IP address of the user
function getUserIP() {
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // IP is passed from proxy or load balancer
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        // Direct connection
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

// Extract browser and OS
list($browser, $os) = getBrowserDetails($userAgent);

// Get the user's IP address
$userIP = getUserIP();

// Assuming $subject and $formattedDate are already defined

// Fix for the email content
$message = "
<html>
    <head>
        <title>$subject</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
            .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }
            .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }
            .email-header img { width: 30px; }
            .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }
            h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }
            .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }
            .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }
            .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }
        </style>
    </head>
    <body>
        <div class='email-container'>
            <div class='email-header'>
                <img src='https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png' alt='Log in'>
            </div>
            <div class='email-content'>
                <h1>Sign-in Alert</h1>
                
                <div class='details'>
                <p>Hi $username, </p>
                
                <p>A sign-in was detected on your RaccoonO365 2FA/MFA account.</p>
                    <p><strong>$browserIcon User Agent:</strong><br>$userAgent</p>
                    <p><strong>$browserIcon Browser:</strong><br>$browser</p>
                    <p><strong>$osIcon Operating System:</strong><br>$os</p>
                    <p><strong>$ipIcon IP Address:</strong><br>$userIP</p>
                    <p><strong>$dateIcon Date:</strong><br>$formattedDate</p>
                    <p> If this was you, no further action is needed. If you don’t recognize this activity, please secure your account immediately.</p>
                </div>
            </div>
        </div>
    </body>
</html>
";

                    sendMail($to, $subject, $message, 'system_mail');
                }

                // Respond with success
                echo json_encode([
                    'status' => 'success',
                    'role' => $row['user_type'],
                    'status_code' => 200,
                    'message' => 'Login successful.'
                ]);
            } else {
                // Incorrect password
                echo json_encode([
                    'status' => 'error',
                    'field' => 'username',
                    'message' => 'Invalid password.'
                ]);
            }
        } else {
            // User not found
            echo json_encode([
                'status' => 'error',
                'message' => 'Email does not exist. Please signup instead.'
            ]);
        }
    } catch (PDOException $error) {
        // Handle any errors
        echo json_encode([
            'status' => 'error',
            'message' => 'Database error: ' . $error->getMessage()
        ]);
    }
}
