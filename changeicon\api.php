<?php

// Set the content type to JSON
header('Content-Type: application/json');

// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create database connection using MySQLi
$conn = new mysqli($host, $username, $password, $dbname);


// Check connection
if ($conn->connect_error) {
    die(json_encode(['success' => false, 'message' => 'Database connection failed: ' . $conn->connect_error]));
}

// Ensure username is passed in the parameter
if (!isset($_GET['id']) || empty($_GET['id'])) {
    die(json_encode(['success' => false, 'message' => 'id parameter is required.']));
}

// Sanitize input by using a prepared statement
$username_param = $_GET['id'];
$stmt = $conn->prepare("SELECT selected_icon FROM user_profiles WHERE username = ?");
$stmt->bind_param("s", $username_param);

// Execute the query
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Fetch the selected_icon value
    $row = $result->fetch_assoc();
    echo json_encode(['success' => true, 'selected_icon' => $row['selected_icon']]);
} else {
    // User not found
    echo json_encode(['success' => false, 'message' => 'User not found.']);
}

// Close the prepared statement and the database connection
$stmt->close();
$conn->close();

?>
