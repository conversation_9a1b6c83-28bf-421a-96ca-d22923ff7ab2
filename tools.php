<?php
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');
// Prepare and execute SQL query
$sql = "SELECT * FROM attachments";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$attachments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    //var_dump($attachments);
?>
<?php require('assets/header.php') ?>


<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <h3 class="h5"> Attachments </h3>
    <div class="table-responsive small">
        <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">File Name</th>
                <th scope="col">Description</th>
                <th scope="col"></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($attachments as $attachment): ?>
                <tr>
                    <td><?= htmlspecialchars($attachment['id']) ?></td>
                    <td><?= htmlspecialchars($attachment['file_name']) ?></td>
                    <td><?= htmlspecialchars($attachment['description']) ?></td>
                    <td>
                        <a href="php_files/admin/download.php?file=<?= urlencode($attachment['file_name']) ?>" class="btn btn-sm btn-primary">
                            Download
                        </a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</main>
</div>
</div>
<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

</script>
</body>
</html>