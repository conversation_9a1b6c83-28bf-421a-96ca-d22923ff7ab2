<?php

// Set the content type to JSON
header('Content-Type: application/json');

// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php';

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create database connection using MySQLi
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die(json_encode(['success' => false, 'message' => 'Database connection failed: ' . $conn->connect_error]));
}

// Ensure username parameter is passed
if (!isset($_GET['id']) || empty($_GET['id'])) {
    die(json_encode(['success' => false, 'message' => 'id parameter is required.']));
}

// Sanitize the username input
$username_param = $_GET['id'];
$stmt = $conn->prepare("SELECT user_id FROM user_profiles WHERE username = ?");
$stmt->bind_param("s", $username_param);

// Execute the query
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Fetch the user_id
    $row = $result->fetch_assoc();
    $user_id = $row['user_id'];
    
    // Fetch the configuration text for the user_id
    $stmt = $conn->prepare("SELECT text FROM pspagetext WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo json_encode(['success' => true, 'text' => $row['text']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'User configuration not found.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Username not found.']);
}

// Close the prepared statement and the database connection
$stmt->close();
$conn->close();

?>
