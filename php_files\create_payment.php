<?php
require('db.php');
// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: /login.php");  // Redirects to Google
    exit;
}



require_once '../vendor/autoload.php'; // Fixed path

global $pdo;

try {
    // Query to fetch the active payment method
    $payment_method_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('active_payment_method')");
    $payment_method = $payment_method_stmt->fetch(PDO::FETCH_ASSOC); // Retrieve the payment method from settings

    // Check if the payment method is valid
    if (!$payment_method || !in_array($payment_method['setting_value'], ['plisio', 'manual'])) {
        throw new Exception('Invalid or missing payment method in settings');
    }

    // Check if the payment method is 'plisio'
    if ($payment_method['setting_value'] === 'plisio') {
        // Proceed with the Plisio payment logic (your existing code)
        
        // Fetch API key from settings
        $api_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('api_key')");
        $api_key = $api_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $plisio = new \Plisio\ClientAPI($api_key['api_key']);
        if (!$api_key || !$api_key['api_key']) {
            $response = ['error' => true, 'message' => 'Invalid or missing API key'];
        }

        $user_email = $_SESSION['email'];
        $amount = $_POST['amount'];
        $response = '';

        try {
            $response = $plisio->createTransaction([
                'amount' => $amount,
                'currency' => 'USDT_TRX',
                'order_name' => 'Wallet Top-Up',
                'order_number' => uniqid(),
                'email' => $user_email,
                'callback_url' => DOMAIN . "/php_files/payment_callback.php",
                'success_url' => DOMAIN . "/wallet.php",
                'cancel_url' => DOMAIN . "/payment_error.php",
                'source_currency' => 'USD',
            ]);

            if ($response && $response['status'] === 'success') {
                $response = [
                    'success' => true,
                    'invoice_url' => $response['data']['invoice_url'],
                    'message' => "You will be redirected shortly",
                ];
            } else {
                $response = ['success' => false, 'message' => $response['data']['message']];
            }
        } catch (Exception $e) {
            $response = ['success' => false, 'message' => $e->getMessage()];
        }

        

    } elseif ($payment_method['setting_value'] === 'manual') {
        // If the payment method is 'manual', redirect to the manual payment page with the amount in the URL
        $amount = $_POST['amount'];
        $response = [ 'type' => 'manual' ];
    }
} catch (PDOException $e) {
    echo 'Connection failed: ' . $e->getMessage();
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage();
}

header('Content-Type: application/json');
        echo json_encode($response);