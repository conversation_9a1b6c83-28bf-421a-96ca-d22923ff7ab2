<?php
require '../assets/admin_header.php';
?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5"> Send a broadcast message </h1>
    </div>
    <div>
        <form class="row g-3" id="signupForm" action="" method="post"
              enctype="multipart/form-data">
            <div class="col-md-6">
                <label for="title" class="form-label">Title</label>
                <input type="text" class="form-control" name="title" id="title">
            </div>
            <div class="col-md-6">
                <label for="message" class="form-label">Message</label>
                <textarea class="form-control" required name="message" id="message"
                          rows="5"></textarea>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary"> Send </button>
            </div>
        </form>
    </div>
</main>
</div>
</div>

<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    //function to send broadcast message
    $('#signupForm').on( 'submit' , function (e) {
        e.preventDefault();
        let formData = $(this).serialize();
        $.ajax({
            type: 'POST',
            data: formData,
            url: '<?= BASE_URL?>/php_files/admin/send_broadcast.php',
            success: function(response) {
                alert(response.message);
            },
            error: function(xhr, status, error) {
                alert('An error occurred');
                console.log('An error occurred' , error);
            }
        });
    })
</script>
</body>
</html>