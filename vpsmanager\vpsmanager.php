<?php

require('../assets/admin_authorize.php');

// Start session to ensure admin authentication
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];


$conn = new mysqli($host, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to fetch all records
function getAllData($conn) {
    $sql = "SELECT * FROM cloudflare_data";
    return $conn->query($sql);
}

// Delete record
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $delete_sql = "DELETE FROM cloudflare_data WHERE id = $id";
    if ($conn->query($delete_sql) === TRUE) {
        $_SESSION['delete_message'] = 'Record deleted successfully';
    } else {
        $_SESSION['delete_message'] = 'Error deleting record';
    }
    header('Location: ' . $_SERVER['PHP_SELF']);  // Redirect to refresh the page
    exit();
}

// Update record
if (isset($_POST['update'])) {
    $id = $_POST['id'];
    $main_email = $_POST['main_email'];
    $main_token = $_POST['main_token'];  // Add main_token
    $addon_email = $_POST['addon_email'];
    $addon_token = $_POST['addon_token'];  // Add addon_token
    $redirect_email = $_POST['redirect_email'];
    $redirect_token = $_POST['redirect_token'];  // Add redirect_token
    $attach_email = $_POST['attach_email'];
    $attach_token = $_POST['attach_token'];  // Add attach_token
    $username = $_POST['username'];

    $update_sql = "UPDATE cloudflare_data SET main_email='$main_email', main_token='$main_token', addon_email='$addon_email', 
                    addon_token='$addon_token', redirect_email='$redirect_email', redirect_token='$redirect_token',
                    attach_email='$attach_email', attach_token='$attach_token', username='$username' WHERE id=$id";

    if ($conn->query($update_sql) === TRUE) {
        $_SESSION['update_message'] = 'Record updated successfully';
    } else {
        $_SESSION['update_message'] = 'Error updating record';
    }
    header('Location: ' . $_SERVER['PHP_SELF']);  // Redirect to refresh the page
    exit();
}

// Fetch all records
$data = getAllData($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <style>
        /* Global Styles */
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }

        h2 {
            text-align: center;
            margin: 20px 0;
            color: #333;
        }

        /* Table Styles */
        table {
            width: 90%;
            margin: 20px auto;
            border-collapse: collapse;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #fff;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #007BFF;
            color: white;
            font-size: 16px;
        }

        tr:hover {
            background-color: #f9f9f9;
        }

        /* Button Styles */
        button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:focus {
            outline: none;
        }

        /* Search Box Styles */
        #search {
            width: 90%;
            padding: 8px 12px;
            margin: 20px auto;
            display: block;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            table {
                width: 100%;
                font-size: 14px;
            }

            th, td {
                padding: 8px 10px;
            }

            button {
                font-size: 12px;
                padding: 6px 12px;
            }
        }
    </style>
    <!-- Add jQuery and SweetAlert2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>

<h2>Admin Panel - Cloudflare Data</h2>

<!-- Search Box -->
<input type="text" id="search" placeholder="Search by Username">

<!-- Data Table -->
<table id="data-table">
    <thead>
        <tr>
            <th>ID</th>
            <th>User ID</th>
            <th>Main Email</th>
            <th>Main Token</th>
            <th>Addon Email</th>
            <th>Addon Token</th>
            <th>Redirect Email</th>
            <th>Redirect Token</th>
            <th>Attach Email</th>
            <th>Attach Token</th>
            <th>Username</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($row = $data->fetch_assoc()): ?>
        <tr>
            <td><?php echo $row['id']; ?></td>
            <td><?php echo $row['user_id']; ?></td>
            <td><?php echo $row['main_email']; ?></td>
            <td><?php echo $row['main_token']; ?></td>
            <td><?php echo $row['addon_email']; ?></td>
            <td><?php echo $row['addon_token']; ?></td>
            <td><?php echo $row['redirect_email']; ?></td>
            <td><?php echo $row['redirect_token']; ?></td>
            <td><?php echo $row['attach_email']; ?></td>
            <td><?php echo $row['attach_token']; ?></td>
            <td><?php echo $row['username']; ?></td>
            <td>
                <!-- Edit Button -->
                <button class="edit-btn" data-id="<?php echo $row['id']; ?>" 
                        data-email="<?php echo $row['main_email']; ?>"
                        data-username="<?php echo $row['username']; ?>"
                        data-main_token="<?php echo $row['main_token']; ?>"
                        data-addon_email="<?php echo $row['addon_email']; ?>"
                        data-addon_token="<?php echo $row['addon_token']; ?>"
                        data-redirect_email="<?php echo $row['redirect_email']; ?>"
                        data-redirect_token="<?php echo $row['redirect_token']; ?>"
                        data-attach_email="<?php echo $row['attach_email']; ?>"
                        data-attach_token="<?php echo $row['attach_token']; ?>"
                >Edit</button>
                <!-- Delete Button -->
                <button class="delete-btn" data-id="<?php echo $row['id']; ?>">Delete</button>
            </td>
        </tr>
        <?php endwhile; ?>
    </tbody>
</table>


<script>
// jQuery for filtering data based on username
$(document).ready(function() {
    // Search box event
    $('#search').on('input', function() {
        var searchValue = $(this).val().toLowerCase();
        
        // Loop through table rows and hide those that don't match the search query
        $('#data-table tbody tr').each(function() {
            var username = $(this).find('td:eq(6)').text().toLowerCase();  // Username column is index 6
            if (username.indexOf(searchValue) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Handle Edit button click
    $('.edit-btn').on('click', function() {
        const id = $(this).data('id');
        const mainEmail = $(this).data('email');
        const username = $(this).data('username');
        const mainToken = $(this).data('main_token');
        const addonEmail = $(this).data('addon_email');
        const addonToken = $(this).data('addon_token');
        const redirectEmail = $(this).data('redirect_email');
        const redirectToken = $(this).data('redirect_token');
        const attachEmail = $(this).data('attach_email');
        const attachToken = $(this).data('attach_token');

        // Open the SweetAlert modal for editing
        Swal.fire({
            title: 'Edit Record',
            html: `
                <form id="edit-form">
                    <input type="hidden" name="id" value="${id}">
                    <label for="main_email">Main Email:</label>
                    <input type="email" name="main_email" value="${mainEmail}" required><br>
                    <label for="main_token">Main Token:</label>
                    <input type="text" name="main_token" value="${mainToken}" required><br>
                    <label for="addon_email">Addon Email:</label>
                    <input type="email" name="addon_email" value="${addonEmail}" required><br>
                    <label for="addon_token">Addon Token:</label>
                    <input type="text" name="addon_token" value="${addonToken}" required><br>
                    <label for="redirect_email">Redirect Email:</label>
                    <input type="email" name="redirect_email" value="${redirectEmail}" required><br>
                    <label for="redirect_token">Redirect Token:</label>
                    <input type="text" name="redirect_token" value="${redirectToken}" required><br>
                    <label for="attach_email">Attach Email:</label>
                    <input type="email" name="attach_email" value="${attachEmail}" required><br>
                    <label for="attach_token">Attach Token:</label>
                    <input type="text" name="attach_token" value="${attachToken}" required><br>
                
                    <input type="hidden" name="username" value="${username}" readonly><br>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Save changes',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const form = $('#edit-form').serialize();  // Serialize all form data
                $.post('', form, function(response) {
                    Swal.fire('Updated!', 'The record was updated successfully.', 'success');
                    location.reload();  // Reload the page to reflect changes
                }).fail(function() {
                    Swal.fire('Error!', 'There was an issue updating the record.', 'error');
                });
            }
        });
    });

    // Handle Delete button click
    $('.delete-btn').on('click', function() {
        const id = $(this).data('id');
        
        // Show SweetAlert confirmation for delete
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
        }).then((result) => {
            if (result.isConfirmed) {
                // Redirect to delete action in the same page
                window.location.href = '?delete=' + id;
            }
        });
    });

    // Show SweetAlert message after deleting or updating a record
    <?php if (isset($_SESSION['delete_message'])): ?>
        Swal.fire({
            title: "<?php echo $_SESSION['delete_message']; ?>",
            icon: "<?php echo (strpos($_SESSION['delete_message'], 'Error') !== false) ? 'error' : 'success'; ?>",
        }).then(() => {
            <?php unset($_SESSION['delete_message']); ?>
        });
    <?php endif; ?>

    <?php if (isset($_SESSION['update_message'])): ?>
        Swal.fire({
            title: "<?php echo $_SESSION['update_message']; ?>",
            icon: "<?php echo (strpos($_SESSION['update_message'], 'Error') !== false) ? 'error' : 'success'; ?>",
        }).then(() => {
            <?php unset($_SESSION['update_message']); ?>
        });
    <?php endif; ?>
});
</script>

</body>
</html>

<?php
$conn->close();
?>


