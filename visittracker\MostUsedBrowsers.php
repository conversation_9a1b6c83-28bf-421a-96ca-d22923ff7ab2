<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Access denied. Please log in.");
}

$config = include 'extractedconfig.php';

$host = $config['host']; // Change this to your database host
$dbname = $config['dbname']; // Change this to your database name
$username = $config['username']; // Change this to your database username
$password = $config['password']; // Change this to your database password

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get the logged-in user's ID
$loggedInUserId = $_SESSION['user_id'];

// Fetch most used browsers, considering unique combinations of user ID and fingerprint
$queryBrowsers = "SELECT browser, COUNT(DISTINCT fingerprint) AS count 
                  FROM visitors 
                  WHERE user_id = :user_id
                  GROUP BY browser
                  ORDER BY count DESC";
$stmt = $pdo->prepare($queryBrowsers);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$browserStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch most used countries and country codes, considering unique combinations of user ID and fingerprint
$queryCountries = "SELECT country, countrycode, flag_url, COUNT(DISTINCT fingerprint) AS count 
                   FROM visitors 
                   WHERE user_id = :user_id
                   GROUP BY country, countrycode, flag_url
                   ORDER BY count DESC";
$stmt = $pdo->prepare($queryCountries);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$countryStats = $stmt->fetchAll(PDO::FETCH_ASSOC);






// Fetch IP addresses from the visitors table for the logged-in user
$queryIPs = "SELECT ip FROM visitors WHERE user_id = :user_id ORDER BY fingerprint ASC";
$stmt = $pdo->prepare($queryIPs);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$ipAddresses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Create an array of objects with 'ip' key
$visitorDataArray = array_map(function($row) {
    return ['ip' => $row['ip']]; // Creating an object with 'ip' as the key
}, $ipAddresses);

// Convert the array of objects to JSON format for JavaScript
$visitorDataJson = json_encode($visitorDataArray);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Most Used Browsers and Countries</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #333;
            color: white;
        }
        img {
            width: 30px;
            height: auto;
        }
        
          #map {
            width: 100%;
            height: 300px;
        }

        .map-container {
            max-width: 100%;
            margin: 0 auto;
        }
        
    </style>
    
        <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />

</head>
<body>
    
    
    
    
    <div class="map-container">
    <div id="map"></div>
</div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>


const visitorData = <?php echo $visitorDataJson; ?>;



// Initialize map with the zoom control positioned at the bottom-left
var map = L.map('map', {
    center: [51.505, -0.09],
    zoom: 0,
    zoomControl: false // Disable the default zoom control
});

// Add the zoom control to the bottom-left corner
L.control.zoom({
    position: 'bottomleft' // Change position to bottom-left
}).addTo(map);

// Set up tile layer (OpenStreetMap tiles)
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

// Function to fetch geolocation data from an IP address
function getVisitorLocation(ip) {
    const apiUrl = `https://get.geojs.io/v1/ip/geo/${ip}.json`;
    
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            const { latitude, longitude, country } = data;
            L.marker([latitude, longitude])
                .bindPopup(`<b>${country}</b>`)
                .addTo(map);
        })
        .catch(error => console.log("Error fetching geolocation data:", error));
}



visitorData.forEach(visitor => {
    getVisitorLocation(visitor.ip);
});
</script>


    <h2>Most visited Browsers</h2>
    <?php if (empty($browserStats)): ?>
        <p>No browser data available.</p>
    <?php else: ?>
    <table>
        <tr>
            <th>Browser</th>
            <th>Total number of visitors</th>
        </tr>
        <?php foreach ($browserStats as $browser): ?>
        <tr>
            <td><?= htmlspecialchars($browser['browser']) ?></td>
            <td><?= htmlspecialchars($browser['count']) ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
    <?php endif; ?>

    <h2>Most Visited Countries</h2>
    <?php if (empty($countryStats)): ?>
        <p>No country data available.</p>
    <?php else: ?>
    <table>
        <tr>
            <th>Country</th>
            <th>Country Code</th>
            <th>Flag</th>
            <th>Total number of visitors</th>
        </tr>
        <?php foreach ($countryStats as $country): ?>
        <tr>
            <td><?= htmlspecialchars($country['country']) ?></td>
            <td><?= htmlspecialchars($country['countrycode']) ?></td>
            <td>
                <img src="<?= htmlspecialchars($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $country['flag_url']) ?>" alt="Flag">
            </td>
            <td><?= htmlspecialchars($country['count']) ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
    <?php endif; ?>

</body>
</html>
