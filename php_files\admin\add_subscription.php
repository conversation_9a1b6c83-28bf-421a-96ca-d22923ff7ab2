<?php

require '../db.php';
require '../functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $planName = sanitize($_POST['plan_name']);
    $price = sanitize($_POST['price']);
    $durationDays = sanitize($_POST['duration_days']);
    $description = sanitize($_POST['description']);
    
    // Check if the 'per_day_cost' column exists, and if not, add it
    try {
        
        // Check if the column exists
        $stmt = $pdo->prepare("SHOW COLUMNS FROM subscription_plans LIKE 'price_per_day'");
        $stmt->execute();
        $icolumnExists = $stmt->rowCount() > 0;

        // If the column exists, drop it
        if ($icolumnExists) {
            $pdo->exec("ALTER TABLE subscription_plans DROP COLUMN price_per_day");
        }

        // Check if the column exists
        $stmt = $pdo->prepare("SHOW COLUMNS FROM subscription_plans LIKE 'per_day_cost'");
        $stmt->execute();
        $columnExists = $stmt->rowCount() > 0;

        // If the column doesn't exist, create it
        if (!$columnExists) {
            $pdo->exec("ALTER TABLE subscription_plans ADD COLUMN per_day_cost DECIMAL(10, 2)");
        }

        // Check if the subscription plan already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM subscription_plans WHERE plan_name = :plan_name");
        $stmt->bindParam(':plan_name', $planName);
        $stmt->execute();
        $existingPlanCount = $stmt->fetchColumn();

        // If the plan already exists, return an error
        if ($existingPlanCount > 0) {
            echo json_encode([
                'status' => 'error',
                'statusCode' => 409,
                'message' => 'Subscription plan with this name already exists.'
            ]);
            exit;  // Stop further execution if the plan exists
        }

        // Calculate price per day
        $pricePerDay = $price / $durationDays;

        // Prepare and execute SQL query
        $stmt = $pdo->prepare("
            INSERT INTO subscription_plans (plan_name, price, duration_days, description, per_day_cost)
            VALUES (:plan_name, :price, :duration_days, :description, :per_day_cost)
        ");

        // Bind the parameters to the prepared statement
        $stmt->bindParam(':plan_name', $planName);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':duration_days', $durationDays);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':per_day_cost', $pricePerDay);  // Bind the price per day

        // Execute the query
        if ($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'statusCode' => 201,
                'message' => 'Subscription plan added successfully.'
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'statusCode' => '500',
                'message' => 'Failed to add subscription plan.'
            ]);
        }
    } catch (PDOException $e) {
        echo json_encode([
            'status' => 'error',
            'statusCode' => '501',
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
}
?>
