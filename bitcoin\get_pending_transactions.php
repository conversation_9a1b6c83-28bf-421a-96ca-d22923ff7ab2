<?php
session_start();
$config = include 'sextractedconfig.php';

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Set header to return JSON response
header('Content-Type: application/json');
 

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die(json_encode(["success" => false, "message" => "Database connection failed: " . $e->getMessage()]));
}

if (!isset($_SESSION['user_id'])) {
    die(json_encode(["success" => false, "message" => "User not logged in"]));
}

$user_id = $_SESSION['user_id'];

// Fetch pending transactions
$query = "SELECT transaction_hash FROM blockchaintransactions WHERE status = 'pending' AND user_id = :user_id";
$stmt = $pdo->prepare($query);
$stmt->execute([':user_id' => $user_id]);
$pendingTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode(["success" => true, "pending_transactions" => $pendingTransactions]);
?>
