<?php

$config = include 'sextractedconfig.php';

// $userId = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

// if (empty($userId)) {
//     header("Location: ../logout"); // Replace '/logout' with your actual logout URL
//     exit();
// }

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    // Create PDO instance
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // SQL query to fetch records with user_id = 0 or NULL
    $query = "SELECT * FROM `cloudflare_data` WHERE `user_id` IS NULL OR `user_id` = 0";

    // Execute the query
    $stmt = $pdo->query($query);
    
    // Fetch all results as an array
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($rows) {
        // Randomly choose one record from the results
        $randomRow = $rows[array_rand($rows)];

        // Return the chosen record as a JSON response
        echo json_encode($randomRow);
    } else {
        // Return a message if no records are found
        echo json_encode(["message" => "No records found."]);
    }
} catch (PDOException $e) {
    // Return error message if connection fails
    echo json_encode(["error" => "Connection failed: " . $e->getMessage()]);
}
?>
