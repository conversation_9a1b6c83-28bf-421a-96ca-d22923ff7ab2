<?php
require 'db.php';

//require_once '../vendor/autoload.php';
require __DIR__ . '/../vendor/autoload.php';
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;




// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 



function sanitize ($input) {
    
        global $config; // Make $config available inside the function
        
    $host = $config['host'];
    $dbname = $config['dbname'];
    $username = $config['username'];
    $password = $config['password'];
    
    $connection = mysqli_connect($host , $username ,$password, $dbname);
    return mysqli_real_escape_string($connection , strip_tags(trim($input)));
}


// function to make a notification
function saveNotification( $userId, $title, $message) {
    global $pdo;
    try {
        // Prepare the SQL statement
        $sql = "INSERT INTO notifications (user_id, title, message) VALUES (:user_id, :title, :message)";
        $stmt = $pdo->prepare($sql);

        // Execute the statement with the provided parameters
        $stmt->execute([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message
        ]);

        return ['status' => 'success', 'message' => 'Notification saved successfully'];
    } catch (PDOException $e) {
        // Handle any errors
        return ['status' => 'error', 'message' => 'Error saving notification: ' . $e->getMessage()];
    }
}

function saveWalletTransaction($user_id, $crypto_type, $amount, $tid, $type, $status) {
    global $pdo;

    // Prepare the SQL statement without quotes around placeholders
    $sql = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
    VALUES (:user_id, :crypto_type, :amount, :transaction_id, :type, :status)";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        'user_id' => $user_id,
        'crypto_type' => $crypto_type,
        'amount' => $amount,
        'transaction_id' => $tid,
        'type' => $type,
        'status' => $status, // Correctly bind status here
    ]);

    // Optional: Commit the transaction if this function is part of a larger transaction
    // If you want this function to manage transactions, uncomment the next two lines
    $pdo->beginTransaction(); // Start transaction here if necessary
    $pdo->commit(); // Commit only if a transaction was started in this function

    return [
        'status' => 'success',
        'message' => 'Wallet transaction saved successfully.',
        'transaction_id' => $tid
    ];
}





// Function to generate a secure random password
function generateRandomPassword($length = 12) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?';
    $charactersLength = strlen($characters);
    $randomPassword = '';
    for ($i = 0; $i < $length; $i++) {
        $randomPassword .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomPassword;
}


function sendMail($to, $subject, $message, $tag) {
    global $pdo;

    // Fetch all active SMTP settings based on the specified tag
    $stmt = $pdo->prepare("SELECT smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption FROM smtp_settings WHERE is_active = 1 AND tag = :tag");
    $stmt->execute(['tag' => $tag]);
    $smtpSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($smtpSettings)) {
        return ['status' => 'error', 'message' => 'No active SMTP settings found for the specified tag.'];
    }

    $result = ['status' => 'error', 'message' => 'Failed to send email after trying all SMTP settings.'];

    // Shuffle the SMTP settings to randomize the order
    shuffle($smtpSettings);

    // Try to send the email using each SMTP setting
    foreach ($smtpSettings as $smtp) {
        try {
            // Extract SMTP settings
            $host = $smtp['smtp_host'];
            $port = (int)$smtp['smtp_port'];
            $username = $smtp['smtp_username'];
            $password = $smtp['smtp_password'];
            $encryption = $smtp['smtp_encryption'];

            // Create a new PHPMailer instance
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host = $host;
            $mail->Port = $port;
            $mail->SMTPAuth = true;
            $mail->Username = $username;
            $mail->Password = $password;
            $mail->SMTPSecure = $encryption;

            // Recipients
            $mail->setFrom($username, 'RaccoonO365');

            $mail->addAddress($to);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $message;

            $mail->send();
            $result = ['status' => 'success', 'message' => 'Email sent successfully'];
            break; // Exit the loop since the email was sent successfully
        } catch (Exception $e) {
            // Log the error or handle it in some other way
            error_log('Failed to send email: ' . $mail->ErrorInfo);
        }
    }

    return $result;
}




function addBusinessDays($startDate, $numDays) {
    $currentDate = new DateTime($startDate);
    $daysAdded = 0;

    while ($daysAdded < $numDays) {
        $currentDate->modify('+1 day');
        
        // Skip Saturday and Sunday
        if ($currentDate->format('N') < 6) {
            $daysAdded++;
        }
    }

    return $currentDate->format('Y-m-d'); // Returns the calculated end date
}



function deleteInactivePlans($user_id) {
    try {
        global $pdo;
        
        // Start a transaction
        $pdo->beginTransaction();

        // Delete inactive subscriptions
        $deleteStmt = $pdo->prepare("
            DELETE FROM user_subscriptions
            WHERE user_id = :user_id 
            AND subscription_end_date <= NOW()
        ");
        $deleteStmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $deleteStmt->execute();

        // Check if any active subscriptions remain
        $checkStmt = $pdo->prepare("
            SELECT COUNT(*) FROM user_subscriptions
            WHERE user_id = :user_id 
            AND subscription_end_date > NOW()
        ");
        $checkStmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $checkStmt->execute();
        $remainingSubscriptions = $checkStmt->fetchColumn();

        // If no active subscriptions remain, set 'subscribed' to 0
        if ($remainingSubscriptions == 0) {
            $updateStmt = $pdo->prepare("
                UPDATE user_profiles 
                SET subscribed = 0 
                WHERE user_id = :user_id
            ");
            $updateStmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $updateStmt->execute();
        }

        // Commit the transaction
        $pdo->commit();

    } catch (PDOException $e) {
        // Rollback transaction in case of error
        $pdo->rollBack();
       // echo 'An error occurred: ' . $e->getMessage();
    }
}