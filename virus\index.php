<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In</title>
    <link rel="stylesheet" href="SignUp_LogIn_Form.css">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'><script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

</head>
  <body>

    <!-- credits to the writter @leonam-silva-de-souza -->
      <div class="container">
          <div class="form-box login">
              <form action="#">
                             
             
                  
                  <img src="dsjwl.png" alt="Logo" class="logo" style="width: 273px;height: auto;">
                  
                    
                  
                  <div class="input-box">
                      <input id="username" type="text" placeholder="Username" required>
                      <i class='bx bxs-user'></i>
                  </div>
                  <div class="input-box">
                      <input type="password" id="password" placeholder="Password" required>
                      <i class='bx bxs-lock-alt' ></i>
                  </div>
                        
              
                  <button type="submit" id="loginBtn" class="btn">Login</button>
                  
                  
                  <div class="quote-box" id="quote-box">
       
                                 </div>
                                 
                  
              </form>
          </div>
          
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>





          <div class="form-box register">
              

          </div>

          <div class="toggle-box">
              <div class="toggle-panel toggle-left">
                
                  
         

   <img src="dkjdiem.png" class="quote-image" style="bottom: 52px;position: fixed;">
                               
              </div>

              <div class="toggle-panel toggle-right">
                  <h1>Welcome Back!</h1>
                  <p>Already have an account?</p>
                  <button class="btn login-btn">Login</button>
              </div>
          </div>
      </div>

      <script src="SignUp_LogIn_Form.js"></script>
      
       <script>
    $(document).ready(function() {
    $('#loginBtn').on('click', function(event) {
        event.preventDefault(); // Prevent form from submitting
        $('.spinner-border').removeClass('visually-hidden');
        $('.btn-sm').addClass('disabled');

        // Collect form data
        var formData = {
            username: $('#username').val(),
            password: $('#password').val()
        };

        // Validate form fields
        var isValid = true;
        var username = $('#username').val().trim();
        var password = $('#password').val().trim();

        if (username.length < 3) {
            $('#usernameError').text('Username or email must be at least 3 characters long.');
            isValid = false;
        }

        if (password.length < 3) {
            $('#passwordError').text('Password must be at least 6 characters long.');
            isValid = false;
        }

        if (isValid) {
            // AJAX request to login the user
            $.ajax({
                url: '../php_files/signin.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    $('.spinner-border').addClass('visually-hidden');
                    $('.btn-sm').removeClass('disabled');

                    if (response.status === 'error') {
                        if (response.field === 'username') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Login Error',
                                text: response.message
                            });
                        } else if (response.field === 'password') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Login Error',
                                text: response.message
                            });
                        }
                    } else if (response.status === 'success') {
                        if (response.role === 'superadmin') {
                            window.location.href = '../admin/dashboard.php';  
                        }
                    }
                },
                error: function(error) {
                    $('.spinner-border').addClass('visually-hidden');
                    $('.btn-sm').removeClass('disabled');
                    console.log(error.message);
                    Swal.fire({
                        icon: 'error',
                        title: 'An error occurred',
                        text: 'Please try again.'
                    });
                }
            });
        }
    });
});



        
        function fetchQuote() {
            fetch('signinqoute.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('quote-box').innerHTML = data;
                    document.querySelector('.quote-text').style.opacity = 1;
                    document.querySelector('.quote-text').style.transform = 'translateY(0)';
                    document.querySelector('.quote-author').style.opacity = 1;
                    document.querySelector('.quote-author').style.transform = 'translateY(0)';
                });
        }

        
        fetchQuote();

       
        setInterval(fetchQuote, 10000);
        
        
    </script>
    
    
       <script>
  
  function removeImageOnMobile() {
    
    var userAgent = navigator.userAgent.toLowerCase();

   
    if (/mobile|android|iphone|ipad|ipod|windows phone|blackberry|bb10|opera mini|webos|nokia|htc|samsung|sony|lg|motorola/i.test(userAgent)) {
      
      var image = document.querySelector('.quote-image');
      
      if (image) {
        image.remove();
      }
    }
  }

  
  window.onload = removeImageOnMobile;

  
  window.onresize = removeImageOnMobile;
</script>

  </body>
</html>