<?php
require "../db.php";
function uploadImage () {
    // Check if the file was uploaded without errors
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] == 0) {
        $image = $_FILES['profile_picture'];
        $altText = $_POST['alt'];

        // Validate and handle the upload
        $uploadDir = 'bg/'; // Ensure this directory is writable
        $uploadFile = $uploadDir . basename($image['name']);

        // Check if the upload directory exists, if not, create it
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Move the uploaded file to the desired directory
        if (move_uploaded_file($image['tmp_name'], $uploadFile)) {
            // Connect to your database
            global $pdo;

            $imageUrl = BASE_URL . '/php_files' . '/admin/' . $uploadFile;
            // Prepare and execute the insert statement
            $stmt = $pdo->prepare("INSERT INTO theme_images (imageUrl, altText , isApproved) VALUES (:imageUrl, :altText , 1)");
            $stmt->execute(['imageUrl' => $imageUrl, 'altText' => $altText]);

            return [ "status" => "200" , "message" => "Image uploaded and information saved!" ];
        } else {
            return [ "status" => "500" , "message" => "Error uploading the image." ];
        }
    } else {
        return [ "status" => "500" , "message" => "No image uploaded or there was an error." ];
    }
}

function deleteImage($imageId) {
    // Connect to the database
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT imageUrl FROM theme_images WHERE id = :id");
        $stmt->execute(['id' => $imageId]);
        $image = $stmt->fetch();

        if ($image) {
            $filePath = $image['imageUrl'];

            // Step 2: Delete the file from the server directory if it exists
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Step 3: Delete the image record from the database
            $stmt = $pdo->prepare("DELETE FROM theme_images WHERE id = :id");
            $stmt->execute(['id' => $imageId]);

            return ["status" => "200", "message" => "Image successfully deleted."];
        } else {
            return ["status" => "404", "message" => "Image not found in the database."];
        }
    } catch (Exception $e) {
        return ["status" => "500", "message" => "Error deleting the image: " . $e->getMessage()];
    }
}


// Approve or reject an image
function approveImage($imageId) {
    global $pdo;
    $isApproved = 1;

    $stmt = $pdo->prepare("UPDATE theme_images SET isApproved = :isApproved WHERE id = :id");
    $stmt->execute(['isApproved' => $isApproved, 'id' => $imageId]);

    return ["status" => "200", "message" => "Image successfully approved."];
}



$response = ['status' => 'error', 'message' => 'Invalid action'];

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_FILES['profile_picture'])) {
    $response = uploadImage();
} else if ( $_SERVER['REQUEST_METHOD'] === "POST" && $_GET['action'] === 'delete' ) {
    $response = deleteImage( $_POST['id'] );
} else if ( $_SERVER['REQUEST_METHOD'] === "POST" && $_GET['action'] === 'approve' ) {
    $response = approveImage ( $_POST['id'] );
}

header('Content-Type: application/json');
echo json_encode($response);