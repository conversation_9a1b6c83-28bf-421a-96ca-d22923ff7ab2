<?php

// Start the session to get the user_id from session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}

$pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Function to check if the 'removeoldcookieslinkdomain' column exists
function checkColumnExists($column_name, $table_name) {
    global $pdo;
    $stmt = $pdo->prepare("SHOW COLUMNS FROM $table_name LIKE :column_name");
    $stmt->execute(['column_name' => $column_name]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Function to add the 'removeoldcookieslinkdomain' column if it does not exist
function addRemoveOldCookiesLinkDomainColumn() {
    global $pdo;
    $column_exists = checkColumnExists('removeoldcookieslinkdomain', 'secondcookieslinkdnsdomain_requests');
    
    if (!$column_exists) {
        // Column does not exist, so create it
        $stmt = $pdo->prepare("ALTER TABLE secondcookieslinkdnsdomain_requests ADD removeoldcookieslinkdomain VARCHAR(255) DEFAULT NULL");
        $stmt->execute();
        //echo "Column 'removeoldcookieslinkdomain' created successfully.";
    } else {
       // echo "Column 'removeoldcookieslinkdomain' already exists.";
    }
}

// Call the function to ensure the column exists
addRemoveOldCookiesLinkDomainColumn();

// Function to check if the domain exists in the table
function checkDomainExists($domain_name) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM secondcookieslinkdnsdomain_requests WHERE domain_name = :domain_name OR removeoldcookieslinkdomain = :domain_name");
    $stmt->execute(['domain_name' => $domain_name]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Function to update the removeoldcookieslinkdomain column
function updateRemoveOldCookiesLinkDomain($domain_name, $user_id) {
    global $pdo;
    $stmt = $pdo->prepare("UPDATE secondcookieslinkdnsdomain_requests SET removeoldcookieslinkdomain = :domain_name WHERE user_id = :user_id AND removeoldcookieslinkdomain IS NULL");
    $stmt->execute(['domain_name' => $domain_name, 'user_id' => $user_id]);
}

// Function to add a new domain entry with ensure no existing removeoldcookieslinkdomain
function addDomainEntry($domain_name, $user_id) {
    global $pdo;
    // First ensure removeoldcookieslinkdomain is NULL if no matching domain exists
    $stmt = $pdo->prepare("DELETE FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id AND (domain_name IS NULL OR removeoldcookieslinkdomain IS NULL)");
    $stmt->execute(['user_id' => $user_id]);

    $stmt = $pdo->prepare("INSERT INTO secondcookieslinkdnsdomain_requests (user_id, domain_name, status, created_at) VALUES (:user_id, :domain_name, 'active', NOW())");
    $stmt->execute(['user_id' => $user_id, 'domain_name' => $domain_name]);
}

// Function to track domain changes and manage waiting time
function trackDomainChange($user_id) {
    global $pdo;
    
    // Fetch the domain_name for this user from the database
    $stmt = $pdo->prepare("SELECT domain_name FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $user_id]);
    $domainData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($domainData) {
        $domain_name = $domainData['domain_name'];

        // Fetch the existing entry for this user
        $stmt = $pdo->prepare("SELECT * FROM secondcookieslinkdnsdomain_requests WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $user_id]);
        $existingData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingData) {
            $existing_domain = $existingData['domain_name'];
            $remove_old = $existingData['removeoldcookieslinkdomain'];
            $last_updated = strtotime($existingData['created_at']);
            
            // Case 1: If removeoldcookieslinkdomain is empty
            if (empty($remove_old)) {
                // Update the removeoldcookieslinkdomain column immediately
                updateRemoveOldCookiesLinkDomain($domain_name, $user_id);
            }
            // Case 2: If domain_name has changed and does not match removeoldcookieslinkdomain
            elseif ($existing_domain !== $domain_name && $remove_old !== $domain_name) {
                // If more than 48 hours (172,800 seconds) have passed, proceed with the update
                if (($last_updated + 172800) < time()) {
                    // Update the removeoldcookieslinkdomain column with the new domain_name
                    updateRemoveOldCookiesLinkDomain($domain_name, $user_id);
                }
            }
        } else {
            // Domain does not exist, so add it
            addDomainEntry($domain_name, $user_id);
        }
    } else {
      //  echo "No domain found for this user.";
    }
}

// Usage example
$user_id = $_SESSION['user_id']; // Get user_id from session (make sure user is logged in)

// First, check if the domain already exists and process it
trackDomainChange($user_id);

//echo "Operation completed.";

?>
