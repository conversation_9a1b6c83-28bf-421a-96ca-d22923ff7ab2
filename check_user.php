<?php
require 'db.php'; // Ensure this file contains the correct PDO database connection code

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = filter_var($_POST['username'], FILTER_SANITIZE_STRING);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

    // Prepare and execute SQL statement to check for existing usernames or emails
    $stmt = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = :username OR email = :email");
    $stmt->execute(['username' => $username, 'email' => $email]);

    if ($stmt->rowCount() > 0) {
        // Determine which field caused the issue
        if ($username) {
            echo json_encode(['status' => 'error', 'field' => 'username', 'message' => 'Username already exists.']);
        } else {
            echo json_encode(['status' => 'error', 'field' => 'email', 'message' => 'Email already exists.']);
        }
    } else {
        echo json_encode(['status' => 'success']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method.']);
}
?>
