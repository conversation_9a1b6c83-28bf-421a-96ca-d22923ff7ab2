<?php
require '../assets/admin_header.php';
require '../php_files/db.php';
require '../php_files/functions.php';
global $pdo;


    // Fetch all domain requests
    $stmt = $pdo->prepare("SELECT dr.requested_domain, dr.id, u.user_id, u.username, dr.request_date 
                            FROM domain_requests dr 
                            JOIN user_profiles u ON dr.user_id = u.user_id 
                            ORDER BY dr.request_date DESC");
    $stmt->execute();

    // Assign the fetched results to the $requests variable
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC); // Corrected variable assignment

    // Debugging output
    // var_dump($requests);

?>
<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
    pt-3
    pb-2 mb-3 border-bottom">
        <h4>Google Smart Red Screen fix Requests</h4>
    </div>
            <div class="table-responsive small">
                <table class="table table-striped table-sm">
                    <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col"> Username </th>
                        <th scope="col"> Domain Name </th>
                        <th scope="col"> Request Date </th>
                        <th scope="col"></th>
                    </tr>
                    </thead>
                    <tbody>
                        
                    <?php
                        if ($requests) {
                            foreach ($requests as $request) {
                                $button = '
                                            <div class="d-flex align-items-center">
                                                <button id="approveButton" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#domainChange" onclick="showDomainInModal(\'' . $request['requested_domain'] . '\', ' . $request['user_id'] . ')">
                                                    Approve
                                                </button>
                                                <div id="loader" class="spinner-border text-success ms-2" style="display: none;" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            ';


                                echo '
                                <tr>
                                    <td>' . $request['id'] . '</td>
                                    <td>' . $request['username'] . '</td>
                                    <td>' . $request['requested_domain'] . '</td>
                                    <td>' . $request['request_date'] . '</td>
                                    <td>
                                        '. $button .'
                                    </td>
                                </tr>
                                ';
                            }
                        } else {
                            echo '';
                        }
                    ?>



                    </tbody>
                </table>
            </div>

            <!-- Modal for adding or changing the domain -->
    <div class="modal fade" id="domainChange" aria-hidden="true"
    aria-labelledby="exampleModalToggleLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <form id="domainChangeForm" class="w-100">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalToggleLabel">Update Domains</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    
                    <input type="text" id="updateDomain" class="form-control" readonly>
    <input type="hidden" id="email-update-user-id">
                        <p>Are you sure this link is no longer having issues with the Google Smart Screen? If fixed, click the "Issue Fixed" button. If not, click "Cancel" or close this notice.</p>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="submitFormToAll">Issue Fixed</button>
                    <button class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>



</main>
<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
 // Function to show domain in the modal when clicked
function showDomainInModal(domain, user_id) {
    $('#updateDomain').val(domain); // Set the clicked domain in the input field
    $('#email-update-user-id').val(user_id); // Set the user_id in hidden input
}

// Trigger confirmation alert only when 'Issue Fixed' button is clicked
$('#submitFormToAll').on('click', function(e) {
    e.preventDefault();

    // Get domain and user_id values from the modal
    var domain = $('#updateDomain').val();
    var user_id = $('#email-update-user-id').val();

    // Display SweetAlert confirmation
    Swal.fire({
        title: 'Are you sure?',
        text: "Do you want to mark this user domain as fixed?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, already fixed!',
        cancelButtonText: 'Not Yet'
    }).then((result) => {
        if (result.isConfirmed) {
            // Proceed with AJAX request if confirmed
            $.ajax({
                url: '../php_files/admin/user_actions.php?action=updateDomain',
                type: 'POST',
                data: {
                    domain: domain,   // Pass the domain
                    user_id: user_id   // Pass the user_id
                },
                success: function(result) {
                    Swal.fire(
                        'Success!',
                        result.message,
                        'success'
                    ).then(() => {
                        location.reload();  // Reload the page after the success alert
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                    Swal.fire(
                        'Error!',
                        'An error occurred while updating the domain.',
                        'error'
                    );
                }
            });
        } else {
            // If canceled, show cancellation message
            Swal.fire(
                'Cancelled',
                'The action was cancelled.',
                'info'
            );
        }
    });
});


</script>


</body>
</html>