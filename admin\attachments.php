<?php
require '../assets/admin_header.php';
?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5"> Add an attachment </h1>
    </div>
    <div>
        <form class="row g-3" id="attachmentForm" action="" enctype="multipart/form-data" method="post"
              enctype="multipart/form-data">
            <div class="col-md-6">
                <label for="title" class="form-label">Title</label>
                <input type="file" class="form-control" required accept=".pdf,.htm,.html"
                       name="attachment"
                       id="attachment">
            </div>
            <div class="col-md-6">
                <label for="title" class="form-label">Description</label>
                <input type="text" class="form-control" name="description" id="description">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-plus"></i> Add file
                </button>
            </div>
        </form>
    </div>
</main>
</div>
</div>

<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="../js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        $('#attachmentForm').on('submit', function(e) {
            e.preventDefault(); // Prevent the default form submission

            var formData = new FormData(this); // Create a FormData object from the form

            $.ajax({
                type: 'POST',
                url: '../php_files/admin/send_broadcast.php',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    alert(response.message);
                },
                error: function(xhr, status, error) {
                    alert('An error occurred');
                    console.log('Error:', error);
                }
            });
        });
    });
</script>
</body>
</html>