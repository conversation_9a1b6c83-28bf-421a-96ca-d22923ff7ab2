<?php

// Include database connection or other necessary files
include '../db.php';
include '../functions.php';

function addManualWallet () {
    global $pdo;

    $coinType = $_POST['coinType'];
    $walletAddress = $_POST['walletAddress'];
    $qrCodeImage = $_FILES['qrCode'];

    // Save the QR code image to a directory
    $uploadDir = 'uploads/'; // Path to the upload directory
    $uploadFile = $uploadDir . basename($qrCodeImage['name']);
    move_uploaded_file($qrCodeImage['tmp_name'], $uploadFile);

    $uploadedLocation = BASE_URL . '/php_files/admin/' . $uploadDir;

    // Create JSON data for the wallet
    $walletData = json_encode([
        'coin_type' => $coinType,
        'wallet_address' => $walletAddress,
        'qr_code' => $uploadedLocation
    ]);

    // Prepare the SQL query to insert the new wallet into the settings table
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
    $settingKey = 'payment_wallet_' . uniqid(); // You can use a unique ID for each wallet
    $stmt->execute([$settingKey, $walletData]);

    // Check if the query was successful
    if ($stmt) {
        return ['status' => 'success' , 'message' => 'Wallet added successfully!'];
    } else {
        return ['status' => 'error', 'message' => 'Error adding wallet.'];
    }
}

function updateActivePayment() {
    $type = $_POST['type'];
    global $pdo;
    
    // Allow only 'manual' or 'plisio' as valid input
    $allowedTypes = ['manual', 'plisio'];
    if (!in_array($type, $allowedTypes)) {
        return json_encode(['status' => 'error', 'message' => 'Invalid payment type']);
    }

    // Prepare SQL to insert or update `active_payment_method`
    $query = "UPDATE settings SET setting_value = :type, updated_at = NOW() WHERE setting_key = 'active_payment_method'";

    try {
        // Prepare statement with PDO
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':type', $type);

        // Execute the query
        $stmt->execute();
        
        return ['status' => 'success', 'message' => 'Payment method updated successfully'];
        
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
}



// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

if ( isset($_GET['action']) && $_GET['action'] === "addWallet" ) {
    $response = addManualWallet();
} else if ( isset($_GET['action']) && $_GET['action'] === "updateActivePayment" ) {
    $response = updateActivePayment();
}

header('Content-Type: application/json');
echo json_encode($response);