<?php
// Assuming session holds logged-in user info
session_start();

// Connect to your database
require('db.php');
$response= '';

function get_notification (  ) {
    $userId = $_SESSION['user_id'];
    global $pdo;

    try {
        // Fetch all notifications for the user
        $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = :user_id ORDER BY created_at DESC");
        $stmt->execute(['user_id' => $userId]);

        // Get all notifications
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get the count of notifications
        $notificationCount = count($notifications);

        // Return JSON response with notification count and notifications data
        return [
            'count' => $notificationCount,
            'notifications' => $notifications
        ];
    } catch (PDOException $e) {
        echo $e->getMessage();
        return ['count' => 0, 'notifications' => [], 'error' => $e->getMessage()];
    }
}

 $response = get_notification();


header('Content-Type: application/json');
echo json_encode($response);
?>