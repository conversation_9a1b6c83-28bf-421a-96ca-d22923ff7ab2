<?php

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');

$config = include 'sextractedconfig.php';

$conn = new mysqli($config['host'], $config['username'], $config['password'], $config['dbname']);

if ($conn->connect_error) {
    die(json_encode(["status" => "error", "message" => "Database connection failed: " . $conn->connect_error]));
}

// Check for unused wallets
$sql_check_unused_wallets = "
    SELECT wallet_address 
    FROM bitcoinwalletaddressmag 
    WHERE user_id IS NULL
    ORDER BY RAND()
    LIMIT 1
";
$unused_wallet_result = $conn->query($sql_check_unused_wallets);

$response = [];

if ($unused_wallet_result && $unused_wallet_result->num_rows > 0) {
    $wallet_row = $unused_wallet_result->fetch_assoc();
    $walletAddress = $wallet_row['wallet_address'];

    // Check for a user without a wallet
    $sql_check_users_without_wallet = "SELECT user_id FROM user_profiles WHERE bitcoin_wallet IS NULL LIMIT 1";
    $users_without_wallet_result = $conn->query($sql_check_users_without_wallet);

    if ($users_without_wallet_result && $users_without_wallet_result->num_rows > 0) {
        $user_row = $users_without_wallet_result->fetch_assoc();
        $userId = $user_row['user_id'];

        // Assign wallet to the user
        $stmt1 = $conn->prepare("UPDATE user_profiles SET bitcoin_wallet = ? WHERE user_id = ?");
        $stmt1->bind_param('si', $walletAddress, $userId);

        $stmt2 = $conn->prepare("UPDATE bitcoinwalletaddressmag SET user_id = ? WHERE wallet_address = ?");
        $stmt2->bind_param('is', $userId, $walletAddress);

        if ($stmt1->execute() && $stmt2->execute()) {
            $response = [
                "status" => "success",
                "message" => "Wallet assigned successfully.",
                "user_id" => $userId,
                "wallet_address" => $walletAddress
            ];
        } else {
            $response = [
                "status" => "error",
                "message" => "Failed to update records: " . $conn->error
            ];
        }

        $stmt1->close();
        $stmt2->close();
    } else {
        // If no user without a wallet, fallback to unassigned wallets
        $sql_get_unassigned_wallets = "
            SELECT wallet_address 
            FROM bitcoinwalletaddressmag 
            WHERE user_id IS NULL
            ORDER BY RAND()
        ";

        $unassigned_result = $conn->query($sql_get_unassigned_wallets);

        if ($unassigned_result && $unassigned_result->num_rows > 0) {
            $unassigned_wallets = $unassigned_result->fetch_all(MYSQLI_ASSOC);
            if (count($unassigned_wallets) > 0) {
                $random_index = array_rand($unassigned_wallets);
                $oneoftheunsigned_wallet = $unassigned_wallets[$random_index]['wallet_address'];

                $response = [
                    "status" => "success",
                    "unassignedwallet_address" => $oneoftheunsigned_wallet
                ];
            } else {
                $response = [
                    "status" => "error",
                    "message" => "No available wallets found."
                ];
            }
        } else {
            $response = [
                "status" => "error",
                "message" => "No unassigned wallets available."
            ];
        }
    }
} else {
    $response = [
        "status" => "error",
        "message" => "No unused wallet found."
    ];
}

$conn->close();

echo json_encode($response, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
?>
