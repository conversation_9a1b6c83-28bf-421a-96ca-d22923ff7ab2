<?php

$email = '<EMAIL>';  // Cloudflare email
$apiKey = '00c343d7e53c0c64183b9199b8ce6d277056a';  // Global API Key
$domain = 'officecloudfiles.com';

/**
 * Get the Zone ID
 */
function getZoneId($domain, $apiKey, $email) {
    $apiUrl = "https://api.cloudflare.com/client/v4/zones?name=" . urlencode($domain);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Auth-Email: $email",
        "X-Auth-Key: $apiKey",
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo "<span class='error'>cURL Error: " . curl_error($ch) . "</span><br>";
    }
    $data = json_decode($response, true);
    curl_close($ch);

    return $data['success'] ? $data['result'][0]['id'] : null;
}

/**
 * Enable Universal SSL (Edge Certificate)
 */
function enableUniversalSSL($zoneId, $apiKey, $email) {
    $apiUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/settings/ssl";

    $data = json_encode(["value" => "full"]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Auth-Email: $email",
        "X-Auth-Key: $apiKey",
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo "<span class='error'>SSL Error: " . curl_error($ch) . "</span><br>";
    }
    curl_close($ch);

    return $response;
}


/**
 * Purge Cache (No Cache)
 */
function purgeCache($zoneId, $apiKey, $email) {
    $apiUrl = "https://api.cloudflare.com/client/v4/zones/$zoneId/purge_cache";

    // Prepare purge cache data
    $data = json_encode([
        "purge_everything" => true  // Purges all cache in the zone
    ]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Auth-Email: $email",
        "X-Auth-Key: $apiKey",
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    curl_close($ch);

    return $response;
}

// 1. Get Zone ID
$zoneId = getZoneId($domain, $apiKey, $email);

if ($zoneId) {
  //  echo "<span class='success'>Zone ID: $zoneId retrieved successfully.</span><br>";
    
    // 2. Enable Universal SSL
    $sslResponse = enableUniversalSSL($zoneId, $apiKey, $email);
   // echo "<span class='success'>Universal SSL Enabled: $sslResponse</span><br>";




    // 3. Purge Cache
    $purgeResponse = purgeCache($zoneId, $apiKey, $email);
    //echo "<span class='success'>Cache purged: $purgeResponse</span><br>";
    
    
    echo "SSL Installed";

} else {
    //echo "<span class='error'>Failed to retrieve Zone ID for $domain.</span><br>";
}

?>
