<?php
$config = include 'sextractedconfig.php';

// Database connection
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Records per page
$records_per_page = 10; // You can change this value to set how many records per page

// Current page number (default to 1)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Handle deletion if the delete request is made
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];
    
    try {
        // Create a PDO instance
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // SQL query to delete the record
        $sql = "DELETE FROM signinpagemanager WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $delete_id, PDO::PARAM_INT);
        $stmt->execute();
        
        // Redirect back after deletion
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    } catch (PDOException $e) {
        echo 'Error: ' . $e->getMessage();
        exit;
    }
}

// Handle edit form submission if the request is made
if (isset($_POST['update'])) {
    $id = $_POST['id'];
    $signinpageurl = $_POST['signinpageurl']; // Only the signinpageurl field

    try {
        // Create a PDO instance
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // SQL query to update the signinpageurl only
        $sql = "UPDATE signinpagemanager SET signinpageurl = :signinpageurl WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':signinpageurl', $signinpageurl, PDO::PARAM_STR);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        // Redirect back after update
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    } catch (PDOException $e) {
        echo 'Error: ' . $e->getMessage();
        exit;
    }
}

// Fetch the rows to display with pagination
try {
    // Create a PDO instance
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // SQL query to fetch rows where user_id is 0 with LIMIT and OFFSET for pagination
    $sql = "SELECT * FROM signinpagemanager WHERE user_id = 0 LIMIT :limit OFFSET :offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':limit', $records_per_page, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();

    // Fetch the results
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch total number of records for pagination
    $count_sql = "SELECT COUNT(*) FROM signinpagemanager WHERE user_id = 0";
    $count_stmt = $pdo->query($count_sql);
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $records_per_page);
} catch (PDOException $e) {
    // Handle any errors
    echo 'Error: ' . $e->getMessage();
    exit;
}

// Handle editing a specific record
if (isset($_GET['edit_id'])) {
    $edit_id = $_GET['edit_id'];
    
    try {
        // Create a PDO instance
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // SQL query to fetch the record for editing
        $sql = "SELECT * FROM signinpagemanager WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $edit_id, PDO::PARAM_INT);
        $stmt->execute();

        // Fetch the record
        $edit_row = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        echo 'Error: ' . $e->getMessage();
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signin Page Manager</title>
</head>
<body>

    <h1>Unused Signin Pages URL</h1>

    <?php if (isset($edit_row)): ?>
        <h2>Edit Signin Page URL</h2>
        <form action="" method="post">
            <input type="hidden" name="id" value="<?php echo $edit_row['id']; ?>">
            <label for="signinpageurl">Signin Page URL:</label>
            <input type="text" name="signinpageurl" value="<?php echo htmlspecialchars($edit_row['signinpageurl']); ?>" required><br><br>
            <input type="submit" name="update" value="Update">
            
            <button id="cancelButton">Cancel Modification</button>

   <script>
        $('#cancelButton').click(function() {
            // Remove query parameters from the current URL
            window.history.pushState({}, '', window.location.pathname);
        });
    </script>
        </form>
    <?php endif; ?>

    <?php if (count($rows) > 0): ?>
        <table border="1">
            <thead>
                <tr>
                    <th>Signin Page URL</th>
                    <th>Expired</th>
                    <th>Created At</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($rows as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['signinpageurl']); ?></td>
                        <td><?php echo htmlspecialchars($row['expired']); ?></td>
                        <td><?php echo htmlspecialchars($row['created_at']); ?></td>
                        <td>
                            <a href="?edit_id=<?php echo $row['id']; ?>">Edit</a> | 
                            <a href="?delete_id=<?php echo $row['id']; ?>" onclick="return confirm('Are you sure you want to delete this signin page?');">Delete</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="pagination">
            <a href="?page=1">&laquo; First</a> |
            <a href="?page=<?php echo max(1, $page - 1); ?>">Previous</a> |
            <a href="?page=<?php echo min($total_pages, $page + 1); ?>">Next</a> |
            <a href="?page=<?php echo $total_pages; ?>">Last &raquo;</a>
        </div>

    <?php else: ?>
        <p>No unused signin pages URL found.</p>
    <?php endif; ?>

</body>
</html>
