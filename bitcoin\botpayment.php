<?php
// session_start();
$config = include 'sextractedconfig.php';



//  // Get the user ID from the session
//     $user_id = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

 

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo = $db; 
} catch (PDOException $e) {
    die("Could not connect to the database $dbname :" . $e->getMessage());
}

function checkAndCreateTables() {
    global $db;

    $tablesToCheck = [
        "wallet",
        "wallet_transactions",
        "api_requests",
        "awaitingcryptovalue",
        "bitcointransaction"
    ];

    foreach ($tablesToCheck as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $db->query($query);
        if ($result->rowCount() === 0) {
            $createQueries = [
                "api_requests" => "CREATE TABLE api_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );",
                "awaitingcryptovalue" => "CREATE TABLE awaitingcryptovalue (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    transaction_hash VARCHAR(255) NOT NULL,
                    user_id INT NOT NULL,
                    crypto_type VARCHAR(10) NOT NULL,
                    target_wallet VARCHAR(255) NOT NULL,
                    receiver_wallets VARCHAR(255) NOT NULL,
                    status ENUM('pending', 'processed') NOT NULL DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );",
                "bitcointransaction" => "CREATE TABLE bitcointransaction (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    transaction_hash VARCHAR(255) NOT NULL,
                    user_id INT NOT NULL,
                    total_received_crypto DECIMAL(20, 8) NOT NULL,
                    total_received_usd DECIMAL(20, 8) NOT NULL,
                    transaction_date DATE NOT NULL,
                    crypto_type VARCHAR(10) NOT NULL,
                    status ENUM('confirmed', 'pending') NOT NULL DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );"
            ];
            $db->exec($createQueries[$table]);
        }
    }
}

function checkRateLimit($userId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM api_requests WHERE user_id = ? AND request_time > NOW() - INTERVAL 1 MINUTE");
    $stmt->execute([$userId]);
    $requestCount = $stmt->fetchColumn();

    if ($requestCount >= 10) { 
        return false;
    }

    $stmt = $pdo->prepare("INSERT INTO api_requests (user_id, request_time) VALUES (?, NOW())");
    $stmt->execute([$userId]);

    return true;
}

function logMessage($message) {
    error_log($message, 3, 'logfile.log');
}

function checkCryptoTransaction($cryptoType, $transactionHash, $targetWalletAddress, $userId) {
    global $pdo;

    $apiBaseUrls = [
        'bitcoin' => 'https://api.blockchair.com/bitcoin',
        'ethereum' => 'https://api.blockchair.com/ethereum',
    ];

    if (!checkRateLimit($userId)) {
        logMessage("Rate limit exceeded for user $userId.");
        return json_encode(["error" => "Rate limit exceeded."]);
    }


    $apiUrl = $apiBaseUrls[$cryptoType] . "/dashboards/transaction/$transactionHash";
    
    
       // Generate a random IP address
    $randomIP = implode('.', [
        rand(1, 254), // First octet: Avoid 0 and 255
        rand(0, 254), 
        rand(0, 254), 
        rand(1, 254)  // Last octet: Avoid 0 and 255
    ]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "X-Forwarded-For: $randomIP", // Spoofed IP address
        "Client-IP: $randomIP",       // Spoofed IP address
        "Referer: ",                 // Empty referer
        "Origin: "                   // Empty origin
    ]);
    

    $maxRetries = 10;
    $retryDelay = 2; 
    $retryCount = 0;

    while ($retryCount < $maxRetries) {
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headers = getRequestHeaders();

        if ($httpCode === 200) {
            $data = json_decode($response, true);
            curl_close($ch);

            if (isset($data['data'][$transactionHash])) {
                $transaction = $data['data'][$transactionHash]['transaction'];
                $outputs = $data['data'][$transactionHash]['outputs'];

            
                $transactionDate = $transaction['date'];

                if ($transactionDate !== $data['data'][$transactionHash]['transaction']['date']) {
                    return json_encode(["error" => "Transaction date mismatch."]);
                }

                $totalReceivedCrypto = 0;
                $totalReceivedUSD = 0;
                $filteredWallets = array_filter($outputs, function($output) use ($targetWalletAddress) {
                    return $output['recipient'] === $targetWalletAddress;
                });

                foreach ($filteredWallets as $output) {
                    $totalReceivedCrypto += $output['value'] / 100000000; 
                    $totalReceivedUSD += $output['value_usd'] ?? 0;
                }

                $receiverWallets = array_map(function ($output) {
                    return [
                        'address' => $output['recipient'],
                        'received_amount_crypto' => $output['value'] / 100000000,
                        'received_amount_usd' => $output['value_usd'] ?? 0,
                    ];
                }, $filteredWallets);
                
                
            
                
                
                
                
                
checkTransactionOwnership($transactionHash, $userId);
                
                

                displayWalletMatchStatus($targetWalletAddress, $receiverWallets);

                if ($totalReceivedCrypto === 0 || $totalReceivedUSD === 0) {
                    logMessage("Transaction hash $transactionHash has no valid funds for target wallet. Storing as awaiting.");
                    $stmt = $pdo->prepare("INSERT INTO awaitingcryptovalue (transaction_hash, user_id, crypto_type, target_wallet, receiver_wallets, status) VALUES (?, ?, ?, ?, 'pending')");
                    $stmt->execute([$transactionHash, $userId, $cryptoType, $targetWalletAddress, json_encode($receiverWallets)]);
                    return json_encode(["error" => "Transaction stored for later confirmation."]);
                }

                $stmt = $pdo->prepare("SELECT * FROM bitcointransaction WHERE transaction_hash = ? AND status = 'confirmed' AND transaction_date > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
                $stmt->execute([$transactionHash]);
                $existingTransaction = $stmt->fetch();

                if ($totalReceivedCrypto > 0 && $totalReceivedUSD > 0) {
                    if ($existingTransaction) {
                        logMessage("Transaction hash already used to fund your wallet: $transactionHash");
                        
                         // Delete the `user_id` entry from `usercurrenttransactionHash`
                $stmt = $pdo->prepare("DELETE FROM usercurrenttransactionHash WHERE user_id = :user_id AND transactionHash = :transactionHash");
                $stmt->execute(['user_id' => $userId, 'transactionHash' => $transactionHash]);


                        return json_encode(["error" => "Transaction hash already used to fund your wallet: $transactionHash"]);
                    }

                    $stmt = $pdo->prepare("SELECT * FROM bitcointransaction WHERE transaction_hash = ? AND status = 'confirmed' AND transaction_date > DATE_SUB(NOW(), INTERVAL 1 DAY)");
                    $stmt->execute([$transactionHash]);
                    $dailyConfirmedTransaction = $stmt->fetch();

                    if ($dailyConfirmedTransaction) {
                        logMessage("Transaction hash already confirmed within the last 24 hours: $transactionHash");
                        return json_encode(["error" => "Transaction hash already confirmed within the last 24 hours."]);
                    }

                    if ($totalReceivedUSD >= 0.01) {
                        logMessage("Valid transaction: $transactionHash with matching wallet address.");
                        autoFundWallet($userId, $totalReceivedUSD, $cryptoType);
                        
                         // Delete the `user_id` entry from `usercurrenttransactionHash`
                $stmt = $pdo->prepare("DELETE FROM usercurrenttransactionHash WHERE user_id = :user_id AND transactionHash = :transactionHash");
                $stmt->execute(['user_id' => $userId, 'transactionHash' => $transactionHash]);



                        
                        $stmt = $pdo->prepare(
                            "INSERT INTO bitcointransaction (transaction_hash, user_id, total_received_crypto, total_received_usd, transaction_date, crypto_type, status) 
                            VALUES (?, ?, ?, ?, ?, ?, 'confirmed')"
                        );
                        $stmt->execute([$transactionHash, $userId, $totalReceivedCrypto, $totalReceivedUSD, $transactionDate, $cryptoType]);
                        
                $result = [
    'transaction_hash' => $transaction['hash'],
    'total_received_crypto' => $totalReceivedCrypto,
    'total_received_usd' => $totalReceivedUSD,
    'receiver_wallets' => $receiverWallets,
    'transaction_date' => $transactionDate,
    'crypto_type' => ucfirst($cryptoType),
];

return json_encode([
    'success' => "Payment confirmed and wallet funded with the sum of $$totalReceivedUSD using $transactionHash.",
    'data' => $result
]);
                    }
                }

                logMessage("Bad transaction detected: $transactionHash");
                return json_encode(["error" => "Transaction does not meet the good transaction criteria."]);
            } else {
                return json_encode(["error" => "Transaction not found."]);
            }
        } elseif ($httpCode == 429) { 
            $retryAfter = isset($headers['Rate-Limit-Reset']) ? (int)$headers['Rate-Limit-Reset'] : 60;
            sleep($retryAfter); 
            $retryCount++;
        } else {
            return json_encode(["error" => "Invalid transaction hash. Unable to fetch transaction details."]);
        }
    }

    curl_close($ch);
    return json_encode(["error" => "Max retries exceeded or an unknown error occurred."]);
}

function displayWalletMatchStatus($targetWalletAddress, $receiverWallets) {
    if (checkWalletMatch($targetWalletAddress, $receiverWallets)) {
       
    } else {
        echo "No match";
    }
}

function checkWalletMatch($targetWalletAddress, $receiverWallets) {
    foreach ($receiverWallets as $wallet) {
        if ($wallet['address'] === $targetWalletAddress) {
            return true;
        }
    }
    return false;
}





function checkTransactionOwnership($transactionHash, $userId) {
    global $pdo;
    
    

 // Ensure the table exists and create it if necessary
    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS usercurrenttransactionHash (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            transactionHash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ";
    $pdo->exec($createTableQuery);

    // Check if the transaction already exists for the given user
    $stmt = $pdo->prepare("SELECT id FROM usercurrenttransactionHash WHERE user_id = :user_id AND transactionHash = :transactionHash");
    $stmt->execute(['user_id' => $userId, 'transactionHash' => $transactionHash]);

    if ($stmt->rowCount() > 0) {
        logMessage("Transaction hash $transactionHash already exists for user $userId.");
        return; // Transaction already exists, do nothing
    }
    
    
    

    // Prepare the query to select user_id from both tables
    $stmt = $pdo->prepare("
        SELECT user_id 
        FROM bitcointransaction 
        WHERE transaction_hash = :transactionHash
        UNION ALL
        SELECT user_id 
        FROM awaitingcryptovalue 
        WHERE transaction_hash = :transactionHash
        LIMIT 1
    ");

    // Execute the query
    $stmt->execute(['transactionHash' => $transactionHash]);

    // Fetch the first column of the first row
    $dbUserId = $stmt->fetchColumn();

    // Debugging: Log the raw value of $dbUserId
    logMessage("Value of dbUserId: " . var_export($dbUserId, true));

    // If $dbUserId is empty (null, false, or an empty string), do nothing
    if (empty($dbUserId)) {
        logMessage("No transaction found for hash $transactionHash.");
        return;
    }

    // Check if the transaction belongs to another user
    if ($dbUserId != $userId) {
        logMessage("Transaction hash $transactionHash belongs to user $dbUserId, not $userId.");
        echo json_encode(["error" => "Transaction belongs to another user."]);
        exit();
    }



// If no issues, insert the transaction
    $stmt = $pdo->prepare("INSERT INTO usercurrenttransactionHash (user_id, transactionHash) VALUES (:user_id, :transactionHash)");
    $stmt->execute(['user_id' => $userId, 'transactionHash' => $transactionHash]);

    logMessage("Transaction hash $transactionHash successfully saved for user $userId.");


    // If no issues, do nothing
    return;
}









function autoFundWallet($userId, $amount, $cryptoType = 'USD') {
    global $db;

    try {
        $db->beginTransaction();

        // Check if wallet exists for the user
        $query = $db->prepare("SELECT balance FROM wallet WHERE user_id = :userId");
        $query->execute(['userId' => $userId]);
        $wallet = $query->fetch();

        if ($wallet) {
            // Wallet exists, update balance
            $newBalance = $wallet['balance'] + $amount;
            $updateQuery = $db->prepare("UPDATE wallet SET balance = :newBalance WHERE user_id = :userId");
            $updateQuery->execute(['newBalance' => $newBalance, 'userId' => $userId]);
        } else {
            // Wallet does not exist, insert new wallet with initial balance
            $insertQuery = $db->prepare("INSERT INTO wallet (user_id, balance) VALUES (:userId, :amount)");
            $insertQuery->execute(['userId' => $userId, 'amount' => $amount]);
        }

        // Record the transaction
        $transactionQuery = $db->prepare("
            INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
            VALUES (:userId, :cryptoType, :amount, :transactionId, 'credit', 'completed')
        ");
        $transactionQuery->execute([
            'userId' => $userId,
            'cryptoType' => $cryptoType,
            'amount' => $amount,
            'transactionId' => uniqid('txn_')
        ]);

        $db->commit();

        // Return the success response
        return json_encode([
            'success' => true,
            'message' => "Wallet funded successfully with the sum of $$amount  in $cryptoType."
        ]);
    } catch (Exception $e) {
        $db->rollBack();
        return json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

checkAndCreateTables();
header('Content-Type: application/json');



// Function to process the pending crypto values
function processAwaitingCryptoValues() {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM awaitingcryptovalue WHERE status = 'pending'");
    $stmt->execute();
    $awaitingRows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($awaitingRows as $row) {
        $userId = $row['user_id'];
        $transactionHash = $row['transaction_hash'];
        $cryptoType = $row['crypto_type'];
        $targetWallet = $row['target_wallet'];
        $receiverWallets = json_decode($row['receiver_wallets'], true);

        $checkResult = checkCryptoTransaction($cryptoType, $transactionHash, $targetWallet, $userId);
        $checkResultData = json_decode($checkResult, true);

        if (isset($checkResultData['success'])) {
            // Transaction confirmed, auto fund the wallet
            $fundResult = autoFundWallet($userId, $checkResultData['data']['total_received_usd'], $cryptoType);
            logMessage("Transaction confirmed and wallet funded for user $userId: $transactionHash");
        } else {
            // Transaction not confirmed, log the error
            logMessage("Transaction not confirmed for user $userId: $transactionHash - Error: " . $checkResultData['error']);
        }
    }
}

// Run the function to process pending transactions
processAwaitingCryptoValues();


function getRequestHeaders() {
    $headers = [];
    foreach ($_SERVER as $name => $value) {
        if (strpos($name, 'HTTP_') === 0) {
            $header = str_replace('HTTP_', '', $name);
            $header = str_replace('_', '-', strtolower($header));
            $headers[$header] = $value;
        }
    }
    return $headers;
}

$headers = getRequestHeaders();


$cryptoType = isset($headers['crypto-type']) ? $headers['crypto-type'] : '';



// // Query to get bitcoin_wallet for the signed-in user
// $sql = "SELECT bitcoin_wallet FROM user_profiles WHERE user_id = ?";
// $stmt = $pdo->prepare($sql);
// $stmt->execute([$user_id]);
// $bitcoin_wallet = $stmt->fetchColumn();

// // Assign the value to a globally accessible variable
// $GLOBALS['bitcoin_wallet'] = $bitcoin_wallet;




// $walletAddress = $GLOBALS['bitcoin_wallet'];

$walletAddress = isset($headers['Wallet-Address']) ? $headers['Wallet-Address'] : '';



$transactionHash = isset($headers['transaction-hash']) ? $headers['transaction-hash'] : '';

$userId = $_SESSION['user_id'];



echo checkCryptoTransaction($cryptoType, $transactionHash, $walletAddress, $userId);
?>
