<?php
require '../functions.php';
require '../db.php';
// Define your encryption key
$encryptionKey = 'RaccoonO365'; // Replace this with a strong, secure key

function send_broadcast() {
    global $pdo, $encryptionKey;
    $message = sanitize($_POST['message']);
    $title = sanitize($_POST['title']);

    // Encrypt the message
    $encryptedMessage = encryptMessage($message, $encryptionKey);

    $sql = "INSERT INTO general_messages (message, title) VALUES (:message, :title)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['message' => $encryptedMessage, 'title' => $title]);

    return ['status' => 'success', 'message' => 'Message sent successfully'];
}

// Encryption function
function encryptMessage($message, $key) {
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));
    return openssl_encrypt($message, 'aes-256-cbc', $key, 0, $iv) . '::' . bin2hex($iv);
}


function addAttachment () {
    global $pdo;
    $file = $_FILES['attachment'];
    $fileName = $file['name'];
    $fileTmpName = $file['tmp_name'];
    $fileError = $file['error'];
    $fileType = $file['type'];
    $description = sanitize($_POST['description']);

    // Validate file type
    $allowedTypes = ['application/pdf', 'text/html', 'text/htm'];
    if (!in_array($fileType, $allowedTypes)) {
        return [ 'status' => 'error' , 'message' => 'Invalid file type'];
    }

    // Move file to uploads directory
    $uploadDir = 'uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    $filePath = $uploadDir . basename($fileName);

    if (move_uploaded_file($fileTmpName, $filePath)) {
        // Insert file info into the database
        $sql = "INSERT INTO attachments (file_name, file_path, file_type , description) VALUES (:file_name, :file_path, :file_type , :description)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_type' => $fileType,
            'description' => $description
        ]);
        return [ 'status' => 'success' , 'message' => 'File uploaded successfully'];
    } else {
        return [ 'status' => 'error' , 'message' => 'Failed to upload file'];
    }
}

// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

// Handle password reset requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['message'])) {
    $response = send_broadcast();
} elseif ( $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['attachment']) ) {
    $response = addAttachment();
}

// Send a JSON response
header('Content-Type: application/json');
echo json_encode($response);