<?php

// Allow CORS from any origin
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Get the user_id from the query parameters
$user_id = isset($_GET['user_id']) ? $_GET['user_id'] : '';


// Get the current domain
$currentDomain = $_SERVER['HTTP_HOST'];

// Define the target URL
$targetUrl = 'https://' . $currentDomain . '/get_background.php?user_id=' . urlencode($user_id);

// Initialize a cURL session
$ch = curl_init($targetUrl);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

// Execute the request
$response = curl_exec($ch);

// Check for cURL errors
if (curl_errno($ch)) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => curl_error($ch)]);
    exit;
}

// Close the cURL session
curl_close($ch);

// Decode the JSON response
$responseData = json_decode($response, true);
$imageUrl = $responseData['background_image'] ?? '';

// Check if the image URL is valid
if ($imageUrl) {
    // Use cURL again to fetch the actual image data
    $imageCh = curl_init($imageUrl);
    curl_setopt($imageCh, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($imageCh, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($imageCh, CURLOPT_BINARYTRANSFER, true);
    
    // Get the image data
    $imageData = curl_exec($imageCh);
    
    // Check for errors while fetching the image
    if (curl_errno($imageCh)) {
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => curl_error($imageCh)]);
        curl_close($imageCh);
        exit;
    }

    // Get the content type of the image
    $contentType = curl_getinfo($imageCh, CURLINFO_CONTENT_TYPE);
    curl_close($imageCh);

    // Output the image data
    header('Content-Type: ' . $contentType);
    header('Cache-Control: no-cache, no-store, must-revalidate'); // Prevent caching
    header('Pragma: no-cache');
    header('Expires: 0');
    
    echo $imageData;
} else {
    http_response_code(404);
    echo json_encode(['status' => 'error', 'message' => 'Image not found']);
}
?>