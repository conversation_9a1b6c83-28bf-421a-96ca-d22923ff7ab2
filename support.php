<?php
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');

if (session_status() == PHP_SESSION_NONE) {
    session_start();  // Start session if not already started elsewhere
}

// Retrieve the username from session
$dsusername = isset($_SESSION['username']) ? $_SESSION['username'] : '';

// var_dump($dsusername);

if (empty($dsusername)) {
    // Handle the case where the username is not set in the session
    echo 'Error: Username is not available in the session.';
    exit;
}

// Function to create the required tables (only chats table in this case)
function createChatsTable($db) {
    try {
        // Create chats table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS chats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                username VARCHA<PERSON>(255) NOT NULL,
                support_agent_id INT DEFAULT NULL,
                status ENUM('open', 'closed', 'pending') DEFAULT 'open',
                last_message TEXT NOT NULL,
                support_agentmessage TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,  -- Track if the message is read
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                closed_at TIMESTAMP DEFAULT NULL
            )
        ");
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}

// Ensure the 'chats' table exists
createChatsTable($pdo);

function startChat($userId, $db) {
    // Check if a chat exists
    $stmt = $db->prepare("SELECT * FROM chats WHERE user_id = ?");
    $stmt->execute([$userId]);
    $chat = $stmt->fetch();

    if ($chat) {
        return $chat['id'];  // Return existing chat ID
    } else {
        // Create a new chat
        $stmt = $db->prepare("INSERT INTO chats (user_id) VALUES (?)");
        $stmt->execute([$userId]);
        return $db->lastInsertId();  // Return new chat ID
    }
}
    $chat_id = startChat($user_id , $pdo);
 $_SESSION['chat_id'] = $chat_id;


    $stmt = $pdo->prepare("SELECT * FROM messages WHERE chat_id = ?");
    $stmt->execute([$chat_id]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    /*var_dump($messages);*/
    

?>

<?php require('assets/header.php') ?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h4">Chat with customer support</h1>
    </div>
    <div class="card-body h-full">
        <div class="overflow-y-scroll" id="chat-window" style="height: 250px;"></div>

        <div data-mdb-input-init class="form-outline">
            <textarea class="form-control bg-body-tertiary" id="textAreaExample" rows="3"></textarea>
            <button class="btn btn-primary" onclick="send_messages()">Send</button>
        </div>
    </div>
</main>

<script src="<?= BASE_URL ?>/js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js"></script>
<script src="<?= BASE_URL ?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
const send_messages = () => {
    let message = $('#textAreaExample').val();
    
    if (message.trim() === "") {
        Swal.fire('Error!', 'Message cannot be empty', 'error');
        return;
    }
    
    $.ajax({
        url: '<?= BASE_URL ?>/php_files/send_message.php',
        type: 'POST',
        data: {
            message: message
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                $('#textAreaExample').val('');
                fetchMessages();  // Re-fetch messages to include the new unread message
                Swal.fire('Sent!', 'Your message has been sent.', 'success');
            } else {
                Swal.fire('Error!', response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            Swal.fire('Error!', 'An error occurred: ' + error, 'error');
        }
    });
}

const fetchMessages = () => {
    $.ajax({
        url: '<?= BASE_URL ?>/php_files/fetch_messages.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                let messagesHtml = '';

                // Check if the response contains messages and loop through them
                if (response.messages && response.messages.length > 0) {
                    $.each(response.messages, function(index, message) {
                        // Extract both messages
                        let displayMessage = message.last_message;
                        let displaySupportMessage = message.support_agentmessage;

                        // Skip the message if both are empty
                        if (!displayMessage && !displaySupportMessage) {
                            return true;  // Skip this iteration
                        }

                        // Reset messageBackground for each message
                        let messageBackground = 'bg-body-tertiary'; // Default background for messages
                        let readStatusClass = message.is_read ? 'read' : 'unread'; 

                        // Check and ensure that we do not display 'Decryption failed' messages
                        if (displayMessage === "Decryption failed for last message") {
                            displayMessage = "";  // Clear the message if it's a decryption failure
                        }

                        if (displaySupportMessage === "Decryption failed for agent message") {
                            displaySupportMessage = "";  // Clear the support agent message if it's a decryption failure
                        }

                        // Display support agent message if it exists
                        if (displaySupportMessage) {
                            messagesHtml += `
                                <div class="d-flex flex-row justify-content-start mb-4">
                                    <div class="p-3 ms-3" style="border-radius: 15px; background-color: rgba(57, 192, 237, .2);">
                                        <p class="small mb-0">${displaySupportMessage}</p>
                                    </div>
                                </div>
                            `;
                        } 
                        
                        // Display the current user's message if it exists
                        if (displayMessage && message.username === '<?= $dsusername ?>') {
                            messagesHtml += `
                                <div class="d-flex flex-row justify-content-end mb-4">
                                    <div class="p-3 me-3 border ${messageBackground} message ${readStatusClass}" style="border-radius: 15px;">
                                        <p class="small mb-0"><strong>${message.username}</strong>: ${displayMessage}</p>
                                    </div>
                                </div>
                            `;
                        }
                    });

                    // Update the chat window with the new messages
                    $('#chat-window').html(messagesHtml);

                    // Scroll the chat window to the bottom
                    $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);
                } else {
                    $('#chat-window').html('<p>No messages available.</p>');  // Display a message when there are no messages
                }
            } else {
                Swal.fire('Error!', 'Failed to fetch messages: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            Swal.fire('Error!', 'An error occurred while fetching messages: ' + error, 'error');
        }
    });
}




// Fetch messages every 10 seconds
setInterval(fetchMessages, 10000); // 10000 milliseconds = 10 seconds

// Fetch messages immediately when the page loads
fetchMessages();
</script>
</body>
</html>
