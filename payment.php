<?php
   require('php_files/authorizer.php');
    require('php_files/db.php');
    require('php_files/functions.php');
    $user_id = $_SESSION['user_id'];

    $amount = $_GET['amount'];


    $wallets = [];
        global $pdo;
        $statement = $pdo->prepare("SELECT id, setting_value FROM settings WHERE setting_key LIKE 'payment_wallet_%'");
        $statement->execute();
        while ($row = $statement->fetch(PDO::FETCH_ASSOC)) {
            $walletData = json_decode($row['setting_value'], true); // Adjust if using a different format
            $walletData['id'] = $row['id']; // Include the ID in the result
            $wallets[] = $walletData;
        }


    // var_dump( $wallets );


?>
<?php require('assets/header.php') ?>
<style>
    .blur {
        background-color: rgba( 0,0,0,0.4 ) ;
        backdrop-filter: blur(8px);
    }
</style>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h5"> Wallet Addresses </h1> <br>
            <h5> Please make a transfer of <?= $amount ?> to one of these crypto wallets. </h5>
        </div>
        <!-- Wallets List -->
        <div class="wallets">
    <h5>Available Wallets</h5>
    <?php foreach ($wallets as $wallet): ?>
        <div class="wallet border my-1 blur p-2">
            <p><strong>Coin Type:</strong> <?= htmlspecialchars($wallet['coin_type']) ?></p>
            <p><strong>Wallet Address:</strong> <?= htmlspecialchars($wallet['wallet_address']) ?></p>
            <?php if (!empty($wallet['qr_code'])): ?>
                <img required src="<?= htmlspecialchars($wallet['qr_code']) ?>" alt="QR Code" width="100">
            <?php endif; ?>
            <!-- Add data attribute for wallet ID -->
            <button 
                class="btn btn-success" 
                data-bs-toggle="modal" 
                data-bs-target="#exampleModal" 
                data-wallet-id="<?= $wallet['id'] ?>"
                onclick="setWalletId(this)"> 
                I have sent the money 
            </button>
        </div>
    <?php endforeach; ?>
</div>

<!-- Receipt Upload Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form id="receiptForm" method="post" enctype="multipart/form-data">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Upload Payment Receipt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- This hidden input will receive the wallet ID -->
                    <input type="hidden" name="wallet_id" id="wallet_id">
                    <input type="hidden" name="user_id" value="<?= htmlspecialchars($user_id) ?>">
                    <div class="mb-3">
                        <label for="receiptImage" class="form-label">Upload Receipt</label>
                        <input class="form-control" type="file" name="receiptImage" id="receiptImage" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>

</main>

<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

    function setWalletId(button) {
        const walletId = button.getAttribute('data-wallet-id');
        document.getElementById('wallet_id').value = walletId;
    }

    $(document).ready(function() {
    $('#receiptForm').on('submit', function (e) {
        e.preventDefault(); 
        var amount = <?= $amount ?>; 
        var formData = new FormData(this); 
        formData.append('amount', amount);
        
        console.log(formData); 
        
        $.ajax({
            url: '../php_files/user_actions.php?action=sendReceipt',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.status == "success") {
                    alert(response.message);
                    window.location.href = `<?= BASE_URL?>/wallet.php` ;
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
                console.log('An error occurred: ', error);
            }
        });
    });
});


</script>