/**** Chat Popup Layout******/
body{
	background:#e5e5e5;	
	font-family: sans-serif;
}

.msg_box {
    position: fixed;
    bottom: 20px;
    width: 100%;
    max-width: 300px; /* Max width for better responsiveness */
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    right: 20px;
}

.msg_head {
    background: #007bff; /* A more vibrant blue */
    color: white;
    padding: 12px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.msg_body {
    background: white;
    padding: 20px;
    height: 250px; /* Increased height for better visibility */
    font-size: 14px; /* Slightly larger font size */
    overflow-y: auto;
}

.msg_input {
    width: calc(100% - 4px); /* Adjust width to fit properly */
    height: 60px; /* Increased height for better input space */
    border: 1px solid #ddd;
    border-top: none;
    box-sizing: border-box;
    padding: 10px;
    background: rgba(0, 123, 255, 0.1); /* Slightly transparent background */
    color: black;
    font-size: 14px;
    border-radius: 0 0 8px 8px;
}


.close,
.minimize {
    cursor: pointer;
    font-size: 18px;
    color: #007bff;
}

.minimize {
    margin-right: 10px;
}


.msg-left{
	position:relative;
	background:#e2e2e2;
	padding:5px;
	min-height:10px;
	margin-bottom:5px;
	margin-right:10px;
	border-radius:5px;
	word-break: break-all;
	color: black;
}

.msg-right{
	background:#d4e7fa;
	padding:5px;
	min-height:15px;
	margin-bottom:5px;
	position:relative;
	margin-left:10px;
	border-radius:5px;
	word-break: break-all;
	color: black;
}
/**** Slider Layout Popup *********/

 #chat-sidebar {
     width: 200px;
     position: absolute;
     height: 100%;
     left: 0px;
     top: 0px;
     padding-top: 10px;
     padding-bottom: 10px;
 }
 #sidebar-user-box {
     padding: 4px;
     margin-bottom: 4px;
     font-size: 15px;
     font-family: Calibri;
     font-weight:bold;
     cursor:pointer;
}
 #sidebar-user-box:hover {
     background-color:#999999 ;
}
 #sidebar-user-box:after {
     content: ".";
     display: block;
     height: 0;
     clear: both;
     visibility: hidden;
}
 img{
     width:35px;
     height:35px;
     border-radius:50%;
     float:left;
}
 #slider-username{
     float:left;
     line-height:30px;
     margin-left:5px;
}
 








