<?php
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection settings
$db_host =  $config['host']; // Replace with your database host
$db_user = $config['username']; // Replace with your database username
$db_pass = $config['password']; // Replace with your database password
$db_name = $config['dbname']; // Replace with your database name

// Establish a database connection
function getDatabaseConnection() {
    global $db_host, $db_user, $db_pass, $db_name;

    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($conn->connect_error) {
        die(json_encode(["success" => false, "message" => "Database connection failed: " . $conn->connect_error]));
    }

    return $conn;
}

// Get the action from the query string
$action = $_GET['action'] ?? null;

if ($action === 'fetch_domain') {
    // Fetch the user's domain
    $user_id = $_SESSION['user_id'] ?? null;

    if (!$user_id) {
        echo json_encode(["success" => false, "message" => "User not signed in."]);
        exit;
    }

    $conn = getDatabaseConnection();
    $stmt = $conn->prepare("SELECT domain_name FROM dnsdomain_requests WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");

    if (!$stmt) {
        echo json_encode(["success" => false, "message" => "Failed to prepare query: " . $conn->error]);
        exit;
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo json_encode(["success" => true, "domain" => $row['domain_name']]);
    } else {
        echo json_encode(["success" => false, "message" => "No domain found for user."]);
    }

    $stmt->close();
    $conn->close();
    exit;
} elseif ($action === 'fetch_google_key') {
    // Fetch the user's Google Site Key
    $user_id = $_SESSION['user_id'] ?? null;

    if (!$user_id) {
        echo json_encode(["success" => false, "message" => "User not signed in."]);
        exit;
    }

    $conn = getDatabaseConnection();
    $stmt = $conn->prepare("SELECT googlekey FROM user_profiles WHERE user_id = ? LIMIT 1");

    if (!$stmt) {
        echo json_encode(["success" => false, "message" => "Failed to prepare query: " . $conn->error]);
        exit;
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo json_encode(["success" => true, "google_key" => $row['googlekey']]);
    } else {
        echo json_encode(["success" => false, "message" => "Google Site Key not found for user."]);
    }

    $stmt->close();
    $conn->close();
    exit;
} elseif ($action === 'update_google_key') {
    // Update the Google Site Key
    $user_id = $_SESSION['user_id'] ?? null;

    if (!$user_id) {
        die("User not signed in.");
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $googleKey = $_POST['google_site_key'] ?? '';
        $googleDomain = $_POST['google_domain'] ?? ''; // You might not need this now

        if (empty($googleKey)) {
            die("Google Site Key cannot be empty.");
        }

        $conn = getDatabaseConnection();

        // Check if the `googlekey` column exists
        $checkColumnQuery = "SHOW COLUMNS FROM `user_profiles` LIKE 'googlekey'";
        $columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

        if (!$columnExists) {
            $addColumnQuery = "ALTER TABLE `user_profiles` ADD `googlekey` VARCHAR(255) DEFAULT NULL";
            if (!$conn->query($addColumnQuery)) {
                die("Failed to add column: " . $conn->error);
            }
        }

        // Update the user's Google Site Key without the domain column
        $stmt = $conn->prepare("UPDATE `user_profiles` SET `googlekey` = ? WHERE `user_id` = ?");
        if (!$stmt) {
            die("Prepare failed: " . $conn->error);
        }

        $stmt->bind_param("si", $googleKey, $user_id);
        if ($stmt->execute()) {
            echo "Google Site Key updated successfully.";
        } else {
            echo "Failed to update Google Site Key: " . $stmt->error;
        }

        $stmt->close();
        $conn->close();
    }
    exit;
} else {
    die("Invalid action.");
}
?>
