<?php
$config = include 'sextractedconfig.php';  // Database connection
$servername =  $config['host'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

session_start();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not authenticated.']);
        exit;
    }

    $user_id = $_SESSION['user_id'];
    $amount = floatval($_POST['amount']);

    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid donation amount.']);
        exit;
    }

    try {
        // Set up PDO connection using provided config values
        $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $pdo->beginTransaction();

        // Fetch user's wallet balance
        $stmt = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = :user_id FOR UPDATE");
        $stmt->execute(['user_id' => $user_id]);
        $wallet = $stmt->fetch(PDO::FETCH_ASSOC);

        // If wallet doesn't exist, inform the user to fund it
        if (!$wallet) {
            echo json_encode([
                'success' => false, 
                'message' => 'Your wallet is empty or not found. Please fund your wallet with BTC or USDT to continue.'
            ]);
            exit;
        }

        // Check if balance is sufficient
        if ($wallet['balance'] < $amount) {
            $remaining_balance = $wallet['balance'];
            echo json_encode([
                'success' => false, 
                'message' => "Insufficient balance. Your remaining balance is $remaining_balance. Please fund your wallet with BTC or USDT and try again."
            ]);
            exit;
        }

        // Deduct amount from wallet
        $stmt = $pdo->prepare("UPDATE wallet SET balance = balance - :amount WHERE user_id = :user_id");
        $stmt->execute(['amount' => $amount, 'user_id' => $user_id]);

        // Record transaction
        $transaction_id = 'txn_' . uniqid();
        $stmt = $pdo->prepare("INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status, created_at, updated_at) 
                               VALUES (:user_id, 'USD', :amount, :transaction_id, 'debit', 'completed', NOW(), NOW())");
        $stmt->execute(['user_id' => $user_id, 'amount' => $amount, 'transaction_id' => $transaction_id]);

        $pdo->commit();

        echo json_encode(['success' => true, 'message' => "Donation successful! Thank you for your contribution. Your support is greatly appreciated."]);
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        echo json_encode(['success' => false, 'message' => 'Transaction failed.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request.']);
}
