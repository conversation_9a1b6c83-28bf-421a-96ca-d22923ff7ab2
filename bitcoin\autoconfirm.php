<?php
// Output JSON response
header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$config = include 'sextractedconfig.php';

$userId = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

if (empty($userId)) {
    error_log("User ID is not set, redirecting to logout.");
    header("Location: ../logout.php"); // Replace '/logout' with your actual logout URL
    exit();
}

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Database connection
try {
    $pdo = new PDO('mysql:host=' . $host . ';dbname=' . $dbname, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    error_log("Database connection established successfully.");
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    exit("Database connection failed.");
}




// Get the user_id from session
$user_id = $_SESSION['user_id'];

// Query to get bitcoin_wallet for the signed-in user
$sql = "SELECT bitcoin_wallet FROM user_profiles WHERE user_id = ?";
$stmt = $pdo->prepare($sql);
$stmt->execute([$user_id]);
$bitcoin_wallet = $stmt->fetchColumn();

// Assign the value to a globally accessible variable
$GLOBALS['bitcoin_wallet'] = $bitcoin_wallet;




// Bitcoin address to check
$bitcoinAddress = $GLOBALS['bitcoin_wallet']; // Replace with your desired Bitcoin address




// Create blockchaintransactions table if it does not exist
createBlockchainTransactionsTable($pdo);

// Main function to process blockchain transactions
function processBlockchainTransactions($bitcoinAddress, $userId, $pdo) {
    // Blockchain explorer URLs
    $explorers = [
        "Blockchain.com" => "https://blockchain.info/rawaddr/$bitcoinAddress",
    ];

    // Function to fetch data from an explorer
    function fetchExplorerData($url) {
        $ch = curl_init();

        // Set options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        // Execute and fetch response
        $processBlockchainresponse = curl_exec($ch);
        if (curl_errno($ch)) {
            error_log("cURL error: " . curl_error($ch));
            return ["error" => curl_error($ch)];
        }
        curl_close($ch);

        return json_decode($processBlockchainresponse, true);
    }

    // Function to filter transactions for a specific Bitcoin address
    function getMatchingTransactions($bitcoinAddress, $transactions) {
        $matchingTransactions = [];

        foreach ($transactions as $transaction) {
            foreach ($transaction['out'] as $output) {
                if ($output['addr'] === $bitcoinAddress) {
                    $matchingTransactions[] = [
                        'receiver_wallet' => $output['addr'],
                        'transaction_hash' => $transaction['hash'],
                        'amount_received' => $output['value'] / 100000000, // Convert satoshi to BTC
                    ];
                }
            }
        }

        return $matchingTransactions;
    }

    $processBlockchainresponse = [];

    foreach ($explorers as $name => $url) {
        $result = fetchExplorerData($url);

        if (isset($result['error'])) {
            $processBlockchainresponse[] = [
                'explorer' => $name,
                'status' => 'error',
                'message' => $result['error'],
            ];
            error_log("Explorer error from $name: " . $result['error']);
        } elseif (isset($result['txs'])) {
            $matchingTransactions = getMatchingTransactions($bitcoinAddress, $result['txs']);
            error_log("Found " . count($matchingTransactions) . " matching transactions from $name.");

            foreach ($matchingTransactions as $transaction) {
                $transactionHash = $transaction['transaction_hash'];
                $receiverWallet = $transaction['receiver_wallet'];
                $amountReceived = $transaction['amount_received'];

                if (isTransactionStored($pdo, $transactionHash, $userId)) {
                    error_log("Transaction hash $transactionHash already stored.");
                    continue; // Skip already stored transactions
                }

                // Check if the transaction status is 'confirmed' or 'already_confirmed'
                if (isTransactionPendingOrConfirmed($pdo, $transactionHash, $userId)) {
                    error_log("Transaction hash $transactionHash is already confirmed or pending.");
                    continue; // Skip if already confirmed or pending
                }

                // Store transaction with pending status
                if (storeTransaction($pdo, $transactionHash, $receiverWallet, $userId, 'pending', $amountReceived)) {
                    error_log("Transaction hash $transactionHash stored successfully with status pending. Receiver: $receiverWallet, Amount: $amountReceived BTC.");
                } else {
                    error_log("Failed to store transaction hash $transactionHash.");
                }
            }

            $processBlockchainresponse[] = [
                'explorer' => $name,
                'status' => 'success',
                'transactions' => $matchingTransactions,
            ];
        } else {
            $processBlockchainresponse[] = [
                'explorer' => $name,
                'status' => 'error',
                'message' => 'Invalid data format received',
            ];
            error_log("Invalid data format from $name.");
        }
    }

    echo json_encode($processBlockchainresponse); // Return the response as JSON
}

// Call the function
processBlockchainTransactions($bitcoinAddress, $userId, $pdo);

// Function to check if a transaction is already stored
function isTransactionStored($pdo, $transactionHash, $userId) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM blockchaintransactions WHERE transaction_hash = :transaction_hash AND user_id = :user_id");
    $stmt->execute(['transaction_hash' => $transactionHash, 'user_id' => $userId]);
    return $stmt->fetchColumn() > 0;
}

// Function to check if a transaction is already confirmed or pending
function isTransactionPendingOrConfirmed($pdo, $transactionHash, $userId) {
    $stmt = $pdo->prepare("SELECT status FROM blockchaintransactions WHERE transaction_hash = :transaction_hash AND user_id = :user_id");
    $stmt->execute(['transaction_hash' => $transactionHash, 'user_id' => $userId]);
    $status = $stmt->fetchColumn();
    return !empty($status); // Return true if status exists (confirmed or pending)
}

// Function to store a transaction
function storeTransaction($pdo, $transactionHash, $walletAddress, $userId, $status, $amount) {
    $stmt = $pdo->prepare("
        INSERT INTO blockchaintransactions (transaction_hash, walletAddress, user_id, status, amount_received) 
        VALUES (:transaction_hash, :walletAddress, :user_id, :status, :amount)
    ");
    try {
        $stmt->execute([
            'transaction_hash' => $transactionHash,
            'walletAddress' => $walletAddress,
            'user_id' => $userId,
            'status' => $status,
            'amount' => $amount,
        ]);
        error_log("Transaction $transactionHash stored successfully.");
        return true;
    } catch (PDOException $e) {
        error_log("Error storing transaction: " . $e->getMessage());
        return false;
    }
}

// Function to create blockchaintransactions table if it doesn't exist
function createBlockchainTransactionsTable($pdo) {
    $createTableSql = "
        CREATE TABLE IF NOT EXISTS blockchaintransactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transaction_hash VARCHAR(255) NOT NULL UNIQUE,
            walletAddress VARCHAR(255) NOT NULL,
            user_id INT NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'pending',
            amount_received DECIMAL(20, 8) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ";

    try {
        $pdo->exec($createTableSql);
        error_log("Blockchain transactions table ensured.");
    } catch (PDOException $e) {
        error_log("Error creating blockchaintransactions table: " . $e->getMessage());
    }
}
?>
