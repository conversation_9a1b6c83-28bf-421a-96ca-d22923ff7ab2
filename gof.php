<?php
// Include the database connection file
require_once __DIR__ . '/php_files/db.php'; // Ensure db.php is correctly included

session_start(); // Start session if not already started

// Make sure you have a valid user ID (from the session or authentication system)
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];

    try {
        // Prepare and execute the query to get all URLs from the glitch_urls table
        $stmt = $pdo->prepare("SELECT url FROM glitch_urls");
        $stmt->execute();

        // Initialize the array of URLs
        $urls = [];

        // Fetch all URLs (returns an array of URLs)
        $urls = $stmt->fetchAll(PDO::FETCH_COLUMN); // Fetch all values from the column

        // Clean the URLs by removing the protocol (https:// or http://)
        $cleanedUrls = [];
        foreach ($urls as $url) {
            if (!empty($url)) {
                // Remove the protocol (https:// or http://)
                $cleanedUrl = preg_replace('#^https?://#', '', $url);
                $cleanedUrls[] = $cleanedUrl; // Add the cleaned URL to the array
            }
        }

        // Ensure we have at least one URL to assign
        if (!empty($cleanedUrls)) {
            // Shuffle the URLs before selecting one
            shuffle($cleanedUrls); // Randomly shuffle the array of URLs

            // Check if there are previously used URLs in the session
            if (!isset($_SESSION['used_urls'])) {
                $_SESSION['used_urls'] = []; // Initialize the session variable if it doesn't exist
            }

            // Filter out URLs that have been used
            $remainingUrls = array_diff($cleanedUrls, $_SESSION['used_urls']);

            // If all URLs have been used, reset the used URLs
            if (empty($remainingUrls)) {
                $_SESSION['used_urls'] = [];
                $remainingUrls = $cleanedUrls; // Reset remaining URLs to all cleaned URLs
            }

            // Pick a random URL from the remaining URLs
            $randomKey = array_rand($remainingUrls); // Get a random key from the remaining URLs array
            $makeuseof = $remainingUrls[$randomKey]; // Select the URL using the random key

            // Add the selected URL to the used URLs list
            $_SESSION['used_urls'][] = $makeuseof;
        }

    } catch (PDOException $e) {
        echo "Error: " . $e->getMessage(); // Handle any errors with the database query
    }
} else {
    // Handle the case where the user is not logged in
    echo "User not logged in.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Generator</title>
    <style>
        .container {
            text-align: center;
            padding: 20px;
            background-color: transparent;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .url {
            margin: 10px 0;
            padding: 10px;
            background-color: transparent;
            border: 1px solid #ddd;
            border-radius: 5px;
            word-wrap: break-word;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        .copy-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 5px;
            margin-top: 5px;
        }
        .copy-btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>FREE Google Redirect</h2>

    <!-- Display the randomly selected URL -->
    <?php if (isset($makeuseof)): ?>
        <div class="url">
            <p><?php echo $makeuseof; ?></p>
            <button class="copy-btn" onclick="copyToClipboard('<?php echo $makeuseof; ?>')">Copy</button>
        </div>
    <?php endif; ?>
</div>

<script>
    function copyToClipboard(url) {
        // Create a temporary input element
        var tempInput = document.createElement('input');
        tempInput.value = url;
        document.body.appendChild(tempInput);
        
        // Select the text and copy to clipboard
        tempInput.select();
        document.execCommand('copy');
        
        // Remove the temporary input element
        document.body.removeChild(tempInput);
        
        // Optional: alert the user that the URL is copied
        alert("URL copied to clipboard!");
    }
</script>

</body>
</html>
