<?php
require 'db.php';
session_start();

$user_id = $_SESSION['user_id']; // Get user ID from session

$query = "SELECT subscription_end_date, paused_at FROM subscribeopenredirect WHERE user_id = ?";
$stmt = $pdo->prepare($query);
$stmt->execute([$user_id]);
$subscription = $stmt->fetch();

if ($subscription) {
    $subscription_end_date = $subscription['subscription_end_date'];
    $paused_at = $subscription['paused_at'];

    $currentDate = new DateTime();
    $endDate = new DateTime($subscription_end_date);

    // Check if subscription is already expired
    if ($currentDate > $endDate) {
        echo json_encode(["status" => "error", "message" => "Subscription expired. Cannot pause."]);
        exit;
    }

    // Check if already paused
    if ($paused_at) {
        echo json_encode(["status" => "error", "message" => "Subscription already paused."]);
        exit;
    }

    // Pause subscription
    $pauseDate = $currentDate->format("Y-m-d");
    $updateQuery = "UPDATE subscribeopenredirect SET paused_at = ? WHERE user_id = ?";
    $updateStmt = $pdo->prepare($updateQuery);
    $updateStmt->execute([$pauseDate, $user_id]);

    echo json_encode(["status" => "success", "message" => "Subscription paused successfully."]);
} else {
    echo json_encode(["status" => "error", "message" => "No active subscription found."]);
}
?>
