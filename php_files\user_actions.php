<?php
// Include database connection
require_once 'db.php';
require_once 'functions.php';
// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
       header("Location: ../logout.php");  // Redirects to Google
    exit;
}


function updateDomain() {
    global $pdo;
    $user_id = $_SESSION['user_id'];
    $new_domain = $_POST['new_domain'];

    $stmt = $pdo->prepare("INSERT INTO domain_requests (user_id, requested_domain) VALUES (:uid, :new_domain)");
    $stmt->bindParam(':uid', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':new_domain', $new_domain);
    $stmt->execute();

    saveNotification($user_id, "Your red screen issue", "Your red screen issue is being resolved");

    // return ['message' => 'Your request for fixing your link red screen has been submitted.'];
}



function addResultEmail() {
    global $pdo; 
    ob_start(); // Start output buffering
    header('Content-Type: application/json'); // Ensure JSON response
    
    
      $e_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('email_unlock_fee')");
    $email_fee = $e_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

//    var_dump($email_fee);

    $fee = $email_fee['email_unlock_fee'];
    

    $requiredFunds = $fee;
    $userId = $_SESSION['user_id'];
    $email = $_POST['email'];

    // Check if email is a Yandex email
    if (preg_match('/@yandex\. |@ya\.ru$/i', $email)) {
        echo json_encode(['success' => false, 'message' => 'We do not support Yandex email addresses. Please try another email service.']);
        exit; // Stop execution
    }

    try {
        $pdo->beginTransaction();

        // Check if the email already exists in user_profiles
        $emailCheck = $pdo->prepare("SELECT user_id FROM user_profiles WHERE result_mail = :email");
        $emailCheck->execute(['email' => $email]);
        $existingEmail = $emailCheck->fetch(PDO::FETCH_ASSOC);

        if ($existingEmail) {
            echo json_encode(['success' => false, 'message' => 'This email is already in use. Please try another email.']);
            exit; // Stop execution
        }

        // Check user's wallet balance
        $query = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = :userId");
        $query->execute(['userId' => $userId]);
        $wallet = $query->fetch(PDO::FETCH_ASSOC);

        if (!$wallet || $wallet['balance'] < $requiredFunds) {
            echo json_encode(['success' => false, 'message' => 'Insufficient funds. Please fund your wallet to proceed.']);
            exit; // Stop execution
        }

        // Deduct funds
        $newBalance = $wallet['balance'] - $requiredFunds;
        $updateWallet = $pdo->prepare("UPDATE wallet SET balance = :newBalance WHERE user_id = :userId");
        $updateWallet->execute(['newBalance' => $newBalance, 'userId' => $userId]);

        // Update profile
        $updateProfile = $pdo->prepare("UPDATE user_profiles SET result_mail = :email, email_log_unlocked = 1 WHERE user_id = :userId");
        $updateProfile->execute(['email' => $email, 'userId' => $userId]);

        // Log transaction
        $logTransaction = $pdo->prepare("
        INSERT INTO wallet_transactions (
            user_id, crypto_type, amount, transaction_id, type, status, created_at, updated_at
        ) VALUES (
            :userId, :cryptoType, :amount, :transactionId, :type, :status, NOW(), NOW()
        )");

        $transactionId = uniqid('tx_');

        $logTransaction->execute([
            'userId' => $userId,
            'cryptoType' => 'Result email payment', 
            'amount' => $requiredFunds,
            'transactionId' => $transactionId,
            'type' => 'debit',
            'status' => 'success'
        ]);

        $pdo->commit();

        echo json_encode(['success' => true, 'message' => 'The result email has been updated successfully.']);
        exit; // Stop execution
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
        exit; // Stop execution
    }
}




function updateProfilePicture() {
    global $pdo; // Ensure you have access to the PDO connection

    // Check if the form was submitted and if the file was uploaded
    if (isset($_FILES['profilePicture']) && $_FILES['profilePicture']['error'] === UPLOAD_ERR_OK) {
        $fileTmpPath = $_FILES['profilePicture']['tmp_name'];
        $fileName = $_FILES['profilePicture']['name'];
        $fileSize = $_FILES['profilePicture']['size'];
        $fileType = $_FILES['profilePicture']['type'];

        // You can add validation here for file types and size, e.g.:
        $allowedFileTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($fileType, $allowedFileTypes)) {
            return ['status' => 'error', 'message' => 'Invalid file type'];
        }

        $uploadsDir = __DIR__ . '/../uploads/';

        // Generate a unique file name to avoid overwriting
        $newFileName = uniqid() . '-' . $fileName;

        // Full path to save the image
        $fileDestination = $uploadsDir . $newFileName;

        // Move the uploaded file to the destination directory
        if (move_uploaded_file($fileTmpPath, $fileDestination)) {

            $userId = $_SESSION['user_id'];
            $profilePictureUrl = BASE_URL . '/uploads/' . $newFileName;

            // Update the user_profiles table
            $stmt = $pdo->prepare("UPDATE user_profiles SET profile_picture = ? WHERE user_id = ?");
            $stmt->execute([$profilePictureUrl, $userId]);

            if ($stmt->rowCount() > 0) {
                return ['status' => 'success', 'message' => 'Profile picture updated successfully'];
            } else {
                return ['status' => 'error', 'message' => 'Failed to update profile picture in the database'];
            }
        } else {
            return ['status' => 'error', 'message' => 'There was an error uploading the file'];
        }
    } else {
        return ['status' => 'error', 'message' => 'No file uploaded or an error occurred during upload'];
    }
}



function makeManualPayment () {
    global $pdo;
    $userId =  $_SESSION['user_id'];
    $walletId = $_POST['wallet_id'];
    $receiptImage = $_FILES['receiptImage'];
    $amount = $_POST['amount'];

    if ( !$amount ) {
        return ['status' => 'error', 'message' => 'Inavlid or no amount specified'];
    }

    // Validate and upload receipt image
    $targetDir = "uploads/receipts/";
    if (!file_exists($targetDir)) {
        mkdir($targetDir, 0777, true);  // Create the directory if it doesn't exist
    }
    $targetFile = $targetDir . basename($receiptImage["name"]);
    move_uploaded_file($receiptImage["tmp_name"], $targetFile);

    $upload = BASE_URL . '/php_files/' . $targetFile ;

    // Insert into payments table
    $stmt = $pdo->prepare("INSERT INTO payments (user_id, wallet_id, receipt_image, amount, status) VALUES (?, ?, ?, ?, 'pending')");
    $stmt->execute([$userId, $walletId, $upload , $amount]);

    return ['status' => 'success', 'message' => 'Your payment receipt has been sent. please wait for the payment approval'];
}


// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

if (isset($_POST['new_domain']) &&  $_POST['action'] ===  "redscreen") {
    $response = updateDomain();
} else if ( isset($_POST['action']) && $_POST['action'] === 'add_result_mail' ) {
    $response = addResultEmail();
} else if ( isset($_POST['action']) && $_POST['action'] === 'updateProfilePicture' ) {
    $response = updateProfilePicture();
} else if ( isset($_GET['action']) && $_GET['action'] === 'sendReceipt' ) {
    $response = makeManualPayment();
}

// Send a JSON response
header('Content-Type: application/json');
echo json_encode($response);