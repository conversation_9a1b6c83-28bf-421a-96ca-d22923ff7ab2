<?php
// Specify the directory to scan
$directory = __DIR__;  // Current directory where the script is running

// Function to check if the file content is likely malicious (checking if it's a real image)
function isRealImage($filePath) {
    // Get the file's first few bytes (magic numbers)
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $filePath);
    finfo_close($finfo);

    // Check if the mime type is a valid image
    $validMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff', 'image/heif', 'image/heic'];
    return in_array($mimeType, $validMimeTypes);
}

// Check if the directory exists
if (is_dir($directory)) {
    // Open the directory
    $dir = opendir($directory);

    // Loop through the directory
    while (($file = readdir($dir)) !== false) {
        // Skip the current script itself
        if ($file === basename(__FILE__)) continue;

        $filePath = $directory . DIRECTORY_SEPARATOR . $file;
        
        // Skip directories
        if (is_dir($filePath)) continue;

        // Get the file extension
        $pathInfo = pathinfo($file);
        $extension = strtolower($pathInfo['extension']);
        
        // Define the allowed valid image extensions (ensure bmp, tiff, tiff, heic, heif are valid)
        $validExtensions = ['webp', 'jpeg', 'jpg', 'png', 'bmp', 'tiff', 'tif', 'heic', 'heif'];

        // Check if the file is a valid image type or has a misleading extension (e.g., cookie_data.txt.png)
        if (!in_array($extension, $validExtensions) || !isRealImage($filePath)) {
            // Attempt to delete the file
            if (unlink($filePath)) {
             
            } else {
                echo "Failed to delete: $file<br>";
            }
        }
    }

    // Close the directory
    closedir($dir);
} else {
    echo "The specified directory does not exist.<br>";
}
?>
