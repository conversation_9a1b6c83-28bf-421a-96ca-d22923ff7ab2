<?php
// Start session to access session variables
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php");
    exit(); // Ensure script stops executing after redirect
}

// Include the extracted config file for database connection
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a new PDO instance for the database connection
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
    exit;
}

// Check if the table exists, if not create it
$table_check_query = "SHOW TABLES LIKE 'choosenuser_redirects'";
$table_check_stmt = $pdo->query($table_check_query);

if ($table_check_stmt->rowCount() == 0) {
    // Table doesn't exist, create it without the foreign key
    $create_table_query = "
        CREATE TABLE choosenuser_redirects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            choosenredirecturl VARCHAR(255) NOT NULL,
            reddomain VARCHAR(255) DEFAULT NULL
        )
    ";
    $pdo->exec($create_table_query);
} else {
    // Check if the `reddomain` column exists, if not, add it
    $column_check_query = "SHOW COLUMNS FROM choosenuser_redirects LIKE 'reddomain'";
    $column_check_stmt = $pdo->query($column_check_query);

    if ($column_check_stmt->rowCount() == 0) {
        // Add the `reddomain` column if it doesn't exist
        $alter_table_query = "ALTER TABLE choosenuser_redirects ADD COLUMN reddomain VARCHAR(255) DEFAULT NULL";
        $pdo->exec($alter_table_query);
    }
}

// Check if the form is submitted via AJAX (POST request)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ensure user_id is set (could be from session or other method)
    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
    
    if ($user_id && isset($_POST['url'])) {
        $selectedUrl = $_POST['url']; // Get the selected URL from the form
        
        $_SESSION['selectedUrl'] = $selectedUrl;

        // Extract the domain from the URL
        $parsedUrl = parse_url($selectedUrl);
        $host = $parsedUrl['host'] ?? '';
        $domainParts = explode('.', $host);
        
        // Handle edge cases like subdomains, and ensure it contains the domain and TLD
        $domain = count($domainParts) > 1 ? implode('.', array_slice($domainParts, -2)) : $host;





        // Check if the selected URL exists for this user
        $check_existing_query = "SELECT * FROM choosenuser_redirects WHERE user_id = :user_id";
        $stmt = $pdo->prepare($check_existing_query);
        $stmt->execute(['user_id' => $user_id]);

        if ($stmt->rowCount() > 0) {
            // Record exists, update the choosenredirecturl and reddomain
            $update_query = "UPDATE choosenuser_redirects SET choosenredirecturl = :choosenredirecturl, reddomain = :reddomain WHERE user_id = :user_id";
            $stmt = $pdo->prepare($update_query);
            $stmt->execute(['choosenredirecturl' => $selectedUrl, 'reddomain' => $domain, 'user_id' => $user_id]);
            echo "URL and domain updated successfully.";
        } else {
            // Record doesn't exist, insert a new one
            $insert_query = "INSERT INTO choosenuser_redirects (user_id, choosenredirecturl, reddomain) VALUES (:user_id, :choosenredirecturl, :reddomain)";
            $stmt = $pdo->prepare($insert_query);
            $stmt->execute(['user_id' => $user_id, 'choosenredirecturl' => $selectedUrl, 'reddomain' => $domain]);
            echo "URL and domain inserted successfully.";
        }

        // Optionally return a success message or updated URL
        echo $selectedUrl;
    } else {
        echo "User ID or URL not provided.";
    }
}
?>
