<?php
    require('php_files/authorizer.php');
    require('php_files/db.php');
    require('php_files/functions.php');
    // Prepare and execute SQL query
    $stmt = $pdo->prepare("
    SELECT user_data.*, user_profiles.*, 
           user_data.password AS user_data_password, 
           user_profiles.password AS user_profiles_password 
    FROM user_data
    INNER JOIN user_profiles ON user_data.user_id = user_profiles.user_id 
    WHERE user_data.user_id = :user_id
    AND user_data.status = 'inactive'
");



    // Bind the user ID to the query to avoid SQL injection
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);

    // Execute the statement
    $stmt->execute();

    // Fetch all results

    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total_visits = count($users);
?>
<?php require('assets/header.php') ?>


        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Trash Log</h1>
            </div>

            <h3> Deleted Accounts</h3>
            <div class="table-responsive small">
                <table class="table table-striped table-sm">
                    <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Email</th>
                        <th scope="col">Password</th>
                        <th scope="col">Browser</th>
                        <th scope="col">IP</th>
                        <th scope="col"></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    foreach ( $users as $user ) {
                        echo '<tr>
                        <td>' . htmlspecialchars($user['user_id']) . '</td>
                        <td>' . htmlspecialchars($user['email']) . '</td>
                        <td> ' . htmlspecialchars($user['user_data_password']) . '</td>
                        <td> ' . substr($user['user_agent'] , 0 , 7) . ' </td>
                        <td> ' .  htmlspecialchars($user['ip']) . ' </td>
                        <td> <button onclick="restoreUserData('. $user['id'] .')" class="btn btn-primary"> Restore </button> </td>
                        </tr> '
                        ;
                    }
                    ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>
</div>
<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>


    function restoreUserData(id) {
        $.ajax({
        url: '<?= BASE_URL?>/php_files/user_data_actions.php?action=restore',
        type: 'POST',
        data: {
            id
        },
        success: function(response) {
            location.reload();
        },
        error: function(xhr, status, error) {
            alert('An error occurred: ' + error);
        }
    });
}

</script>
</body>
</html>