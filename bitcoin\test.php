<?php
session_start();
$config = include 'sextractedconfig.php';


require 'payment.php';


// Get the user ID from the session
$user_id = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

if (empty($user_id)) {
    // Redirect to logout or login page if user_id is not present
    header("Location: ../logout"); // Replace '/logout' with your actual logout URL
    exit();
}

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Query transactions for the specific user
    $stmt = $pdo->prepare("SELECT id, transaction_hash, walletAddress FROM blockchaintransactions WHERE status = 'pending' AND user_id = :user_id");
    $stmt->execute(['user_id' => $user_id]);
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($transactions)) {
        error_log("No pending transactions found for user_id: $user_id");
    } else {
        $response = []; // Initialize response array

        foreach ($transactions as $transaction) {
            $response[] = [
                'crypto-type' => "bitcoin",
                'wallet-address' => $transaction['walletAddress'],
                'transaction-hash' => $transaction['transaction_hash']
            ];
        }

        // Output JSON response
        header('Content-Type: application/json');
        echo json_encode($response);
    }
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    exit();
}
?>
