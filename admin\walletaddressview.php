<?php
$config = include 'sextractedconfig.php';

// Database connection details
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Records per page
$records_per_page = 10; // You can change this value to set how many records per page

// Current page number (default to 1)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Handle deletion if the delete request is made
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];
    
    try {
        // Create a PDO instance
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // // SQL query to delete the record (note: this uses the bitcoinwalletaddressmag table)
        // $sql = "DELETE FROM bitcoinwalletaddressmag WHERE id = :id";
        // $stmt = $pdo->prepare($sql);
        // $stmt->bindParam(':id', $delete_id, PDO::PARAM_INT);
        // $stmt->execute();
        
        
  // Fetch the wallet address based on the given ID
$fetchWalletSQL = "SELECT wallet_address FROM bitcoinwalletaddressmag WHERE id = :id";
$fetchWalletStmt = $pdo->prepare($fetchWalletSQL);
$fetchWalletStmt->bindParam(':id', $delete_id, PDO::PARAM_INT);
$fetchWalletStmt->execute();
$walletData = $fetchWalletStmt->fetch(PDO::FETCH_ASSOC);

if ($walletData) {
    $wallet_address = $walletData['wallet_address'];
    
    // SQL query to delete the record from signinpagemanager
    $sql = "DELETE FROM bitcoinwalletaddressmag WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $delete_id, PDO::PARAM_INT);
    $stmt->execute();
    
    // Delete matching wallet entry from user_profiles if a valid wallet address exists
    if (!empty($user_id) && !empty($wallet_address)) {
        $deleteProfileSQL = "DELETE FROM user_profiles WHERE user_id = :user_id AND bitcoin_wallet = :wallet_address";
        $deleteProfileStmt = $pdo->prepare($deleteProfileSQL);
        $deleteProfileStmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $deleteProfileStmt->bindParam(':wallet_address', $wallet_address, PDO::PARAM_STR);
        $deleteProfileStmt->execute();
    }
} else {
    //echo "Wallet address not found for the given ID.";
}

        
        // Redirect back after deletion
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    } catch (PDOException $e) {
        echo 'Error: ' . $e->getMessage();
        exit;
    }
}

// Handle edit form submission if the request is made
if (isset($_POST['update'])) {
    $id = $_POST['id'];
    $signinpageurl = $_POST['wallet_address']; // Only the wallet_address field

    try {
        // Create a PDO instance
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // SQL query to update the wallet_address field in the bitcoinwalletaddressmag table
        $sql = "UPDATE bitcoinwalletaddressmag SET wallet_address = :wallet_address WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':wallet_address', $signinpageurl, PDO::PARAM_STR);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        // Redirect back after update
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    } catch (PDOException $e) {
        echo 'Error: ' . $e->getMessage();
        exit;
    }
}

// Fetch the rows to display with pagination (show all records regardless of user_id)
try {
    // Create a PDO instance
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // SQL query to fetch rows with LIMIT and OFFSET for pagination (no filtering)
    $sql = "SELECT * FROM bitcoinwalletaddressmag LIMIT :limit OFFSET :offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':limit', $records_per_page, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();

    // Fetch the results
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch total number of records for pagination (all records)
    $count_sql = "SELECT COUNT(*) FROM bitcoinwalletaddressmag";
    $count_stmt = $pdo->query($count_sql);
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $records_per_page);
} catch (PDOException $e) {
    // Handle any errors
    echo 'Error: ' . $e->getMessage();
    exit;
}

// Handle editing a specific record
if (isset($_GET['edit_id'])) {
    $edit_id = $_GET['edit_id'];
    
    try {
        // Create a PDO instance
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // SQL query to fetch the record for editing
        $sql = "SELECT * FROM bitcoinwalletaddressmag WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $edit_id, PDO::PARAM_INT);
        $stmt->execute();

        // Fetch the record
        $edit_row = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        echo 'Error: ' . $e->getMessage();
        exit;
    }
}
?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Wallet Manager</title>
    
    
    
    
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>



<script>
$(document).ready(function () {
    function getPendingTransactions() {
        $.ajax({
            url: '../bitcoin/get_pending_transactions.php',
            method: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response && response.success && Array.isArray(response.pending_transactions) && response.pending_transactions.length > 0) {
                    processTransactionsSequentially(response.pending_transactions);
                } else {
                    console.log("No pending transactions found.");
                    sendRequest();
                }
            },
            error: function (xhr, status, error) {
                
            }
        });
    }

    function processTransactionsSequentially(transactions, index = 0) {
        if (index >= transactions.length) {
            console.log("All transactions processed.");
            sendRequest();
            return;
        }

        let transaction = transactions[index];

        $.ajax({
            url: '../bitcoin/payment.php',
            method: 'GET',
            headers: {
                "Transaction-Hash": transaction.transaction_hash
            },
            dataType: 'json', 
            success: function (response) {
                sendRequest();
                console.log(`Processed transaction ${transaction.transaction_hash}`);
                processTransactionsSequentially(transactions, index + 1); 
            },
            error: function (xhr, status, error) {
                processTransactionsSequentially(transactions, index + 1);
            }
        });
    }

    setInterval(getPendingTransactions, 1800000);
    getPendingTransactions();
});


function sendRequest() {
    $.ajax({
        url: '../bitcoin/checker.php',
        method: 'GET',
        dataType: 'json',
        success: function (response) {
            if (response && response.status) {
             
            }
        },
        error: function (xhr, status, error) {
           
        }
    });
}

sendRequest();

</script>

</head>
<body>

    <h1>System Bitcoin Wallet Address Manager</h1>

    <?php if (isset($edit_row)): ?>
        <h2>Edit Signin Page URL</h2>
        <form action="" method="post">
            <input type="hidden" name="id" value="<?php echo $edit_row['id']; ?>">
            <label for="wallet_address">BTC Wallet Address:</label>
            <input type="text" name="wallet_address" value="<?php echo htmlspecialchars($edit_row['wallet_address']); ?>" required><br><br>
            <input type="submit" name="update" value="Update Wallet">
            
            <button id="cancelButton">Cancel Modification</button>

    
        </form>
    <?php endif; ?>

    <?php if (count($rows) > 0): ?>
        <table border="1">
            <thead>
                <tr>
                    <th>System Bitcoin Wallet address</th>
                    <th>User ID</th>
                    <th>Created At</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($rows as $row): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['wallet_address']); ?></td>
                        <td><?php echo htmlspecialchars($row['user_id']); ?></td>
                        <td><?php echo htmlspecialchars($row['created_at']); ?></td>
                        <td>
                            <a href="?edit_id=<?php echo $row['id']; ?>">Edit</a> | 
                            <a href="?delete_id=<?php echo $row['id']; ?>" onclick="return confirm('Are you sure you want to delete this signin page?');">Delete</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="pagination">
            <a href="?page=1">&laquo; First</a> |
            <a href="?page=<?php echo max(1, $page - 1); ?>">Previous</a> |
            <a href="?page=<?php echo min($total_pages, $page + 1); ?>">Next</a> |
            <a href="?page=<?php echo $total_pages; ?>">Last &raquo;</a>
        </div>

    <?php else: ?>
        <p>No bitcoin wallet address found.</p>
    <?php endif; ?>
    
    
    <script>
        $('#cancelButton').click(function() {
            // Remove query parameters from the current URL
            window.history.pushState({}, '', window.location.pathname);
        });
    </script>
    
    
<script>
    
    fetch('../admin/signinpagemanagerapi.php', {
    method: 'GET',
    headers: {
        'Accept': 'application/json'
    }
}).then(response => response.json())
  .then(data => {}) 
  .catch(error => {});

</script>






<script>
    
    fetch('../admin/bitcoinwalletmanagerapi.php', {
    method: 'GET',
    headers: {
        'Accept': 'application/json'
    }
}).then(response => response.json())
  .then(data => {}) 
  .catch(error => {});

</script>


</body>
</html>
