<?php
require_once '../db.php';


function getUserSettingsByUsername( $username ) {
    global $pdo;
    // Step 1: Retrieve user_id based on the provided username
    $userQuery = "SELECT user_id FROM user_profiles WHERE username = :username";
    $stmt = $pdo->prepare($userQuery);
    $stmt->execute(['username' => $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        return ['status' => 'error', 'message' => 'User not found'];
    }

    $user_id = $user['user_id'];

    // Step 2: Retrieve all settings for this user_id
    $settingsQuery = "
        SELECT ats.type, uas.state
        FROM user_antibot_settings uas
        JOIN antibottoggle_states ats ON uas.antibot_id = ats.id
        WHERE uas.user_id = :user_id
    ";
    $stmt = $pdo->prepare($settingsQuery);
    $stmt->execute(['user_id' => $user_id]);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!$settings) {
        return ['status' => 'error', 'message' => 'No settings found for this user'];
    }
    return ['status' => 'success', 'username' => $username, 'settings' => $settings];
}


// Initialize response variable
$response = ['status' => 'error', 'message' => 'Invalid action'];

if ( isset($_GET['username']) ) {
    $response = getUserSettingsByUsername( $_GET['username'] );
}

header('Content-Type: application/json');
echo json_encode($response);