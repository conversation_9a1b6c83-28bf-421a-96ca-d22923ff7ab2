<?php
require '../assets/admin_header.php';
require '../php_files/db.php';
require '../php_files/functions.php';
global $pdo;

try {
// Fetch current settings from the database
    $stmt = $pdo->query("SELECT * FROM smtp_settings");
    $smtp_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $t_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('transfer_fee')");
    $transfer_fee = $t_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    $e_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('email_unlock_fee')");
    $email_fee = $e_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    $api_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('api_key')");
    $api_key = $api_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
//     var_dump($email_fee);
} catch (PDOException $e) {
    echo 'Connection failed: ' . $e->getMessage();
}

function getActivePaymentMethod() {
    global $pdo;

    $query = "SELECT setting_value FROM settings WHERE setting_key = 'active_payment_method' LIMIT 1";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return $result['setting_value'] ?? 'plisio'; // Default to 'plisio' if not set
}

function getAllWallets() {
    global $pdo;

    $query = "SELECT setting_value FROM settings WHERE setting_key LIKE 'wallet_%'";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $wallets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return array_map(function($row) {
        return json_decode($row['setting_value'], true); // Decode JSON data for each wallet
    }, $wallets);
}

$activePaymentMethod = getActivePaymentMethod();
$wallets = getAllWallets();


?>
<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
    pt-3
    pb-2 mb-3 border-bottom">
        <h4> SMTP </h4>
    </div>

    <h5 class="mt-2 h6">All SMTP</h5>
            <div class="table-responsive small">
                <table class="table table-striped table-sm">
                    <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col"> Host </th>
                        <th scope="col"> Port </th>
                        <th scope="col"> Username </th>
                        <th scope="col"> Password </th>
                        <th scope="col"> Encryption </th>
                        <th scope="col"> Status </th>
                        <th scope="col"> Tag </th>
                        <th scope="col"></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                        if ($smtp_settings) {
                            foreach ($smtp_settings as $setting) {
                                if ($setting['is_active'] === 1) {
                                    $status = 'Active';
                                    $buttonText = 'Deactivate';
                                    $buttonAction = 'deactivate'; // Action for deactivation
                                    $buttonClass = 'btn-danger'; // Red button for deactivation
                                } else {
                                    $status = 'Inactive';
                                    $buttonText = 'Activate';
                                    $buttonAction = 'activate'; // Action for activation
                                    $buttonClass = 'btn-success'; // Green button for activation
                                }
                                echo '
                                <tr>
                                    <td>' . $setting['id'] . '</td>
                                    <td>' . $setting['smtp_host'] . '</td>
                                    <td>' . $setting['smtp_port'] . '</td>
                                    <td>' . $setting['smtp_username'] . '</td>
                                    <td>' . $setting['smtp_password'] . '</td>
                                    <td>' . $setting['smtp_encryption'] . '</td>
                                    <td>' . $status . '</td>
                                    <td>' . $setting['tag'] . '</td>
                                    <td>
                                        <button class="btn ' . $buttonClass . '" onclick="changeStatus(' . $setting['id'] . ', \'' . $buttonAction . '\')">' . $buttonText . '</button>
                                    </td>
                                </tr>
                                ';
                            }
                        } else {
                            echo '';
                        }
                    ?>



                    </tbody>
                </table>
            </div>
    

    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
    pt-3 pb-2 mb-3 border-bottom">
    <h4> Add SMTP Settings </h4>
</div>

<div>
    <form class="row g-3" id="smtpForm" method="post">
        <div class="col-md-3 col-6">
            <label for="inputEmail4" class="form-label">Host</label>
            <input type="text" class="form-control" name="host" required id="inputEmail4">
        </div>
        <div class="col-md-3 col-6">
            <label for="inputPassword4" class="form-label">Port</label>
            <input type="number" class="form-control" name="port" required id="smtpPort">
        </div>
        <div class="col-md-3 col-6">
            <label for="username" class="form-label">Username</label>
            <input type="text" required class="form-control" name="username" id="username" placeholder="SMTP Username">
        </div>
        <div class="col-md-3 col-6">
            <label for="inputPassword" class="form-label">Password</label>
            <input type="password" class="form-control" id="inputPassword" name="password" required>
        </div>
        <div class="col-md-6">
            <label for="encryptionSelect" class="form-label">Encryption</label>
            <select name="encryption" required class="form-select" id="encryptionSelect">
                <option value="tls">TLS</option>
                <option value="ssl">SSL</option>
            </select>
        </div>
        <div class="col-md-6">
            <label for="smtpPurpose" class="form-label">Purpose</label>
            <select name="purpose" class="form-select" id="smtpPurpose" required>
                <option value="system_mail">System Mail</option>
                <option value="logs">Logs</option>
            </select>
        </div>
        <div class="col-md-6">
            <label for="smtpExpireDate" class="form-label">Expiration Date</label>
            <input type="text" class="form-control" name="expire_date" id="smtpExpireDate" readonly>
        </div>
        <div class="col-12">
            <button type="submit" class="btn btn-primary">Save</button>
        </div>
    </form>
</div>

    <div class="row">
        <div class="col-md-6">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
            pt-3
            pb-2 mb-3 border-bottom">
                <h4> Add API Key </h4>
            </div>

                <div>
                        <form class="row g-3" id="apiForm" method="post" >
                            <div class="col-12">
                                <label for="apiKey" class="form-label"> Plisio API Key </label>
                                <input type="text" value="<?= $api_key['api_key'] ?>" class="form-control" name="apiKey" required id="apiKey">
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Save</button>
                            </div>
                        </form>
                </div>
        </div>             
        <div class="col-md-6">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
            pt-3
            pb-2 mb-3 border-bottom">
                <h4> Toggle Payment Setting </h4>
            </div>

                <div>
                <form class="row g-3" id="apiForm" method="post">
    <div class="col-md-6 border p-1">
        <!-- Payment method toggle -->
        <div class="form-check form-switch">
            <input class="form-check-input" <?php echo $activePaymentMethod == "plisio" ? "checked" : ""  ?> type="checkbox" id="paymentMethodToggle" onchange="toggleDetection()">
            <label class="form-check-label" for="paymentMethodToggle">Enable Plisio Payment</label>
        </div>
    </div>

    <!-- Manual Payment Fields (Initially hidden) -->
    <div id="manualPaymentFields">
       <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#exampleModal"> Add an address </button>
    </div>

    <!-- Plisio Payment Fields (Initially visible) -->
    <div id="plisioPaymentFields">
        <!-- Plisio related fields go here -->
    </div>
</form>

                </div>
        </div>             
    </div>
    

    <div class="row">
       <div class="col-md-6">
                   <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
            pt-3
            pb-2 mb-3 border-bottom">
                       <h4>Transfer Fee Settings</h4>
                   </div>

                   <div>
                       <form class="row g-3" id="smtpForm" >
                           <div class="col-md-6">
                               <label for="inputTransfer" class="form-label">Transfer Fee</label>
                               <input type="number" class="form-control w-100" disabled value="<?php echo isset
                               ($transfer_fee['transfer_fee']) ? htmlspecialchars($transfer_fee['transfer_fee']) : 0; ?>" name="transfer_fee" required id="inputTransfer">
                           </div>
                           <div class="col-12">
                               <button type="button" class="btn btn-primary" id="t_btn" onclick="transferBtn(event)">Edit</button>
                           </div>
                       </form>

                   </div>
       </div>
       <div class="col-md-6">
                   <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center
            pt-3
            pb-2 mb-3 border-bottom">
                       <h4>Email Unlock Fee </h4>
                   </div>

                   <div>
                       <form class="row g-3" id="emailUnlockForm" >
                           <div class=" col-md-6">
                               <label for="inputEmail" class="form-label">Email Unlock Fee</label>
                               <input type="number" class="form-control  w-100" disabled value="<?php echo isset
                               ($email_fee['email_unlock_fee']) ? htmlspecialchars($email_fee['email_unlock_fee']) : 0; ?>"
                                      name="email_fee" required id="inputEmail">
                           </div>
                           <div class="col-12">
                               <button type="button" class="btn btn-primary" id="e_btn" onclick="emailFeeBtn(event)
">Edit</button>
                           </div>
                       </form>

                   </div>
       </div>
    </div>



<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="exampleModalLabel"> Add a wallet address </h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
      <form id="manualWalletForm" enctype="multipart/form-data" method="post">
            <div id="manualPaymentFields">
                <div class="border p-1">
                    <label for="btcWalletAddress" class="form-label"> Wallet Address</label>
                    <input type="text" required class="form-control" id="btcWalletAddress" name="walletAddress">
                </div>

                <div class="border p-1">
                    <label for="btcWalletAddress" class="form-label"> Coin Type</label>
                    <input type="text" required class="form-control" id="btcWalletAddress" name="coinType">
                </div>

                <div class="border p-1">
                    <label for="qrCode" class="form-label">Upload Wallet QR Code</label>
                    <input type="file" accept="image/*" required class="form-control" id="qrCode" name="qrCode">
                </div>
            </div>
        
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="submit" class="btn btn-primary">Save changes</button>
    </form>
      </div>
    </div>
  </div>
</div>


</main>
<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script><script src="dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

    const paymentMeth = "<?= $activePaymentMethod ?>"

    const paymentMethodToggle = document.getElementById('paymentMethodToggle');
    const manualPaymentFields = document.getElementById('manualPaymentFields');
    const plisioPaymentFields = document.getElementById('plisioPaymentFields');

    if (paymentMeth == "plisio") {
        // Enable Plisio payment
        plisioPaymentFields.style.display = 'block';
        manualPaymentFields.style.display = 'none';
    } else {
        // Enable Manual payment
        plisioPaymentFields.style.display = 'none';
        manualPaymentFields.style.display = 'block';
    }
    

function togglePaymentMethod() {
    const paymentMethodToggle = document.getElementById('paymentMethodToggle');
    const manualPaymentFields = document.getElementById('manualPaymentFields');
    const plisioPaymentFields = document.getElementById('plisioPaymentFields');

    if (paymentMethodToggle.checked) {
        // Enable Plisio payment
        plisioPaymentFields.style.display = 'block';
        manualPaymentFields.style.display = 'none';
    } else {
        // Enable Manual payment
        plisioPaymentFields.style.display = 'none';
        manualPaymentFields.style.display = 'block';
    }
}


   $('#manualWalletForm').on('submit', function (e) {
    e.preventDefault();
    var formData = new FormData(this); 
    console.log(formData);
    $.ajax({
        url: '../php_files/admin/payment.php?action=addWallet',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(response) {
            // Using SweetAlert for success message with reload on confirmation
            Swal.fire({
                title: 'Success!',
                text: response.message,
                icon: 'success',
                confirmButtonText: 'OK'
            }).then((result) => {
                if (result.isConfirmed) {
                    location.reload();
                }
            });
        },
        error: function(xhr, status, error) {
            // Using SweetAlert for error message
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred: ' + error,
                icon: 'error',
                confirmButtonText: 'OK'
            });
            console.log('An error occurred: ', error);
        }
    });
});


    function toggleDetection() {
    const checkbox = document.getElementById(`paymentMethodToggle`);
    const value = checkbox.checked ? 'plisio' : 'manual';

    console.log(value);
    $.ajax({
        type: 'POST',
        url: '<?= BASE_URL?>/php_files/admin/payment.php?action=updateActivePayment',
        data: { type: value },
        success: function(response) {
            if (response.status === "success") {
                togglePaymentMethod();
                // Using SweetAlert for success feedback with reload on confirmation
                Swal.fire({
                    title: 'Success!',
                    text: 'Payment method updated successfully.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    }
                });
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error storing toggle state:', textStatus, errorThrown);
            // Using SweetAlert for error feedback
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred while updating the payment method.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}


$('#smtpForm').on('submit', function (e) {
    e.preventDefault();
    const formData = $('#smtpForm').serialize();
    $.ajax({
        url: '../php_files/admin/settings.php?action=updateSmtp',
        type: 'POST',
        data: formData,
        success: function(response) {
            // Using SweetAlert for success message with reload on confirmation
            Swal.fire({
                title: 'Success!',
                text: response.message,
                icon: 'success',
                confirmButtonText: 'OK'
            }).then((result) => {
                if (result.isConfirmed) {
                    location.reload();
                }
            });
        },
        error: function(xhr, status, error) {
            // Using SweetAlert for error message
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred while updating the SMTP settings.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            console.log('An error occurred: ', error);
        }
    });
});



    $('#apiForm').on('submit', function (e) {
    e.preventDefault();
    const formData = $('#apiForm').serialize();
    $.ajax({
        url: '../php_files/admin/settings.php?action=updateApi',
        type: 'POST',
        data: formData,
        success: function(response) {
            // Using SweetAlert for success message with reload on confirmation
            Swal.fire({
                title: 'Success!',
                text: response.message,
                icon: 'success',
                confirmButtonText: 'OK'
            }).then((result) => {
                if (result.isConfirmed) {
                    location.reload();
                }
            });
        },
        error: function(xhr, status, error) {
            // Using SweetAlert for error message
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred while updating the API settings.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            console.log('An error occurred: ', error);
        }
    });
});




 const transferBtn = (e) => {
    e.preventDefault(); // Prevent the default form submission behavior
    
    if ($('#t_btn').text() === 'Edit') {
        $('#inputTransfer').removeAttr('disabled'); // Enable the input field
        $('#t_btn').text('Save'); // Change button text to 'Save'
    } else {
        // Gather the form data (transfer fee value)
        const transferFee = $('#inputTransfer').val(); // Get the value of the input field
        
        // Prepare the data to send via AJAX
        const formData = {
            transfer_fee: transferFee
        };
        
        // Send AJAX request to update the transfer fee
        $.ajax({
            url: '../php_files/admin/settings.php?action=updateTransferFee',
            type: 'POST',
            data: formData, // Send form data
            success: function(response) {
                if (response.status === 'success') {
                    // Using SweetAlert for success message with reload on confirmation
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            location.reload();
                        }
                    });
                    $('#inputTransfer').attr('disabled', 'disabled'); // Disable the input field again
                    $('#t_btn').text('Edit'); // Change the button text back to 'Edit'
                } else {
                    // Using SweetAlert for error message
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                // Using SweetAlert for error message
                Swal.fire({
                    title: 'An error occurred',
                    text: 'Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                console.log('Error:', error);
            }
        });
    }
};


    const emailFeeBtn = (e) => {
    e.preventDefault(); // Prevent the default form submission behavior

    if ($('#e_btn').text() === 'Edit') {
        $('#inputEmail').removeAttr('disabled'); // Enable the input field
        $('#e_btn').text('Save'); // Change button text to 'Save'
    } else {
        // Gather the form data (email fee value)
        const email_fee = $('#inputEmail').val(); // Get the value of the input field

        // Prepare the data to send via AJAX
        const formData = {
            email_fee: email_fee
        };

        // Send AJAX request to update the email fee
        $.ajax({
            url: '../php_files/admin/settings.php?action=updateEUnlockFee',
            type: 'POST',
            data: formData, // Send form data
            success: function(response) {
                if (response.status === 'success') {
                    // Using SweetAlert for success message with reload on confirmation
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            location.reload();
                        }
                    });
                    $('#inputEmail').attr('disabled', 'disabled'); // Disable the input field again
                    $('#e_btn').text('Edit'); // Change the button text back to 'Edit'
                } else {
                    // Using SweetAlert for error message
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                // Using SweetAlert for error message
                Swal.fire({
                    title: 'An error occurred',
                    text: 'Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                console.log('Error:', error);
            }
        });
    }
};


    function changeStatus(id, action) {
    console.log(action);
    $.ajax({
        url: '<?= BASE_URL ?>/php_files/admin/settings.php', // Adjust URL as necessary
        type: 'POST',
        data: {
            id: id,
            action: action
        },
        success: function(response) {
            // Using SweetAlert for success message with reload on confirmation
            Swal.fire({
                title: 'Success!',
                text: response.message,
                icon: 'success',
                confirmButtonText: 'OK'
            }).then(() => {
                location.reload(); // Reload the page after user confirms the success message
            });
        },
        error: function(xhr, status, error) {
            // Using SweetAlert for error message
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred: ' + error,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}




</script>





<script>
document.addEventListener("DOMContentLoaded", function () {
    const encryptionSelect = document.getElementById("encryptionSelect");
    const portInput = document.getElementById("smtpPort");
    const expireDateInput = document.getElementById("smtpExpireDate");

    // Function to update the port based on encryption selection
    function updatePort() {
        const selectedEncryption = encryptionSelect.value;
        if (selectedEncryption === "tls") {
            portInput.value = 587; // Default TLS port
        } else if (selectedEncryption === "ssl") {
            portInput.value = 465; // Default SSL port
        }
    }

    // Function to calculate expiration date (300 days from today)
    function setExpireDate() {
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() + 300); // Add 300 days
        const formattedDate = currentDate.toISOString().split("T")[0]; // Format YYYY-MM-DD
        expireDateInput.value = formattedDate;
    }

    // Auto-update port when encryption is changed
    encryptionSelect.addEventListener("change", updatePort);

    // Set default values on page load
    updatePort();
    setExpireDate();
});
</script>
</body>
</html>