<!--<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Telegram Settings</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>Update Telegram Settings</h2>
    <form id="telegram-settings-form">
        <label for="telegram_id">Telegram ID:</label>
        <input type="text" id="telegram_id" name="telegram_id" required>
        <br><br>
        <label for="telegram_token">Telegram Token:</label>
        <input type="text" id="telegram_token" name="telegram_token" required>
        <br><br>
        <input type="submit" value="Update Settings">
    </form>
    <div id="response-message"></div>

    <script>
        $(document).ready(function() {
            $('#telegram-settings-form').on('submit', function(event) {
                event.preventDefault(); // Prevent the default form submission

                $.ajax({
                    url: 'update_telegram_settings.php',
                    type: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        $('#response-message').html('<p style="color: green;">Settings updated successfully!</p>');
                    },
                    error: function(xhr, status, error) {
                        $('#response-message').html('<p style="color: red;">An error occurred: ' + error + '</p>');
                    }
                });
            });
        });
    </script>
</body>
</html>
-->