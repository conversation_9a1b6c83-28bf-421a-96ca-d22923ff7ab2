<?php
// Include database connection and any required functions
require_once 'db.php';
require_once 'functions.php';


// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);



// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../php_files/logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}



global $pdo;

function updateBackgroundImage($pdo) {
    $user_id = $_SESSION['user_id'];
    
    // Check if the file was uploaded without errors
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] == 0) {
        $image = $_FILES['profile_picture'];

        // Allowed file extensions and MIME types
        $allowedExtensions = ['png', 'jpeg', 'jpg', 'webp'];
        $allowedMimeTypes = ['image/png', 'image/jpeg', 'image/webp'];

        // Get file extension and MIME type
        $fileExtension = strtolower(pathinfo($image['name'], PATHINFO_EXTENSION));
        $fileMimeType = mime_content_type($image['tmp_name']);

        // Validate file type
        if (!in_array($fileExtension, $allowedExtensions) || !in_array($fileMimeType, $allowedMimeTypes)) {
            return ["status" => "400", "error" => "Invalid file type. Only PNG, JPEG, and WEBP formats are allowed. Unauthorized file types are not permitted. Are you attempting to upload a shell to our system?"];
        }

        // Set the upload directory
        $uploadDir = 'admin/bg/'; // Ensure this directory is writable

        // Generate a random filename using user ID, timestamp, and random string
        $uniqueId = uniqid($user_id . '_', true);
        $safeFilename = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $uniqueId) . '.' . $fileExtension;
        $uploadFile = $uploadDir . $safeFilename;

        // Check if the upload directory exists, if not, create it
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Move the uploaded file to the desired directory
        if (move_uploaded_file($image['tmp_name'], $uploadFile)) {
            // Create the URL for the uploaded image
            $imageUrl = BASE_URL . '/php_files/' . $uploadFile;

            // Prepare and execute the update statement for user_profiles
            $stmt = $pdo->prepare("UPDATE user_profiles SET background_image = :imageUrl WHERE user_id = :user_id");
            $stmt->bindValue(':imageUrl', $imageUrl);
            $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                // Insert into theme_images table for approval process
                $theme_file = $pdo->prepare("INSERT INTO theme_images (imageUrl, altText, user_id, isApproved) VALUES (:imageUrl, :altText, :user_id, :isApproved)");
                $theme_file->execute([
                    'imageUrl' => $imageUrl,
                    'altText' => "user profile",
                    'user_id' => $user_id,
                    'isApproved' => false
                ]);

                return ["status" => "200", "message" => "Image uploaded and background updated successfully. It is awaiting approval from the RaccoonO365 team. Please be patient while our team reviews it. Do not upload the image again!"];
            } else {
                return ["status" => "500", "error" => "Failed to update background image in the database."];
            }
        } else {
            return ["status" => "500", "error" => "Error uploading the image."];
        }
    } else {
        return ["status" => "500", "error" => "No image uploaded or there was an error."];
    }
}





function addBackgroundImage($pdo, $user_id, $image) {
 // Sanitize inputs
 $user_id = $_SESSION['user_id'];
  
   // Replace spaces with '%20' without altering other characters in the URL
        $image = str_replace(' ', '%20', trim($image));

  
 // Prepare and execute the update query
 $sql = "UPDATE user_profiles SET background_image = :image WHERE user_id = :user_id";
 $stmt = $pdo->prepare($sql);
 $stmt->bindValue(':image', $image);
 $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);

 if ($stmt->execute()) {
     return ['status' => 'success', 'message' => 'Your selected background image has been applied successfully!'];
 } else {
     // Return a detailed error message
     return ['status' => 'error', 'error' => 'Failed to update background image: ' . implode(', ', $stmt->errorInfo())];
 }
}

// Get action from query string or request
$action = $_GET['action'] ?? '';

// Get user ID and selected image from POST data
$user_id = $_SESSION['user_id'];
$image = $_POST['image'] ?? null;

$response = ['status' => 'error', 'error' => 'Invalid request'];

if ($action === 'add') {
        $response = addBackgroundImage($pdo, $user_id, $image);
} elseif ($action === 'update') {
        $response = updateBackgroundImage($pdo);
} else {
    $response['message'] = 'Invalid action';
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);

// Close the connection
$pdo = null;
