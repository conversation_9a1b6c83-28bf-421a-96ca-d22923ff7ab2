<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tracking Script</title>
</head>
<body>

<script src="https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js"></script>
<script>
    async function initializeTracking() {
        try {
            const fp = await FingerprintJS.load(); // Load FingerprintJS
            const result = await fp.get(); // Get the visitor's fingerprint

            const ip = '*************'; 
            const userAgent = navigator.userAgent; // Get the user's browser information
            const username = 'tramadol'; // Replace with the actual username

            // Function to send visit data
            function trackVisit() {
                try {
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '../track_visit.php', true);
                    xhr.setRequestHeader('Content-Type', 'application/json');

                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === XMLHttpRequest.DONE) {
                            if (xhr.status === 200) {
                                console.log('Visitor data sent successfully.');
                            } else {
                                console.error('Error sending request:', xhr.status, xhr.responseText);
                            }
                        }
                    };

                    const payload = {
                        fingerprint: result.visitorId,
                        ip: ip,
                        user_agent: userAgent,
                        pages_visited: [window.location.href],
                        username: username, // Added username to the POST data
                        Region: 'North Holland',
                        City: 'Amsterdam',
                        ecountryCode: 'NL',
                        Zip: '1101',
                        Timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    };

                    xhr.send(JSON.stringify(payload));

                } catch (error) {
                    console.error('Error sending request:', error);
                }
            }

            // Call the function to send the visit data
            trackVisit();
        } catch (error) {
            console.error('Error initializing tracking:', error);
        }
    }

    // Start the tracking process once the document is fully loaded
    document.addEventListener('DOMContentLoaded', () => {
        initializeTracking();
        
    });
</script>

</body>
</html>
