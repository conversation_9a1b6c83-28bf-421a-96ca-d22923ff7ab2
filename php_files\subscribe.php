<?php

session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php");  
    exit(); // Make sure the script stops executing after redirect
}

require 'db.php'; // Your database connection script
$userId = intval($_SESSION['user_id']);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $planId = intval($_POST['plan_id']);

    // Validate input data
    if (empty($planId)) {
        echo json_encode(['status' => 'error', 'message' => 'Invalid input data']);
        exit;
    }

    try {
        // Fetch user's current active subscription details
        $stmt = $pdo->prepare("SELECT * FROM user_subscriptions WHERE user_id = ? AND subscription_end_date > CURDATE()");
        $stmt->execute([$userId]);
        $activeSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

        // If an active subscription exists, inform the user
        if ($activeSubscription) {
            echo json_encode(['status' => 'error', 'message' => 'You already have an active subscription. Please wait until it expires before subscribing again or upgrading.']);
            exit;
        }

        // Fetch plan details
        $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        $plan = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$plan) {
            echo json_encode(['status' => 'error', 'message' => 'Plan not found']);
            exit;
        }

        $subscriptionPrice = floatval($plan['price']); // Assume the subscription price is stored in the 'price' column

        // Fetch user's wallet balance
        $stmt = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = ?");
        $stmt->execute([$userId]);
        $wallet = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$wallet || floatval($wallet['balance']) < $subscriptionPrice) {
            echo json_encode(['status' => 'error', 'message' => 'Insufficient wallet balance']);
            exit;
        }

        // Deduct the subscription price from the wallet
        $newBalance = floatval($wallet['balance']) - $subscriptionPrice;
        $stmt = $pdo->prepare("UPDATE wallet SET balance = ? WHERE user_id = ?");
        $stmt->execute([$newBalance, $userId]);

        $transactionId = uniqid('txn_');

        // Proceed with subscription logic
        $subscriptionStartDate = date('Y-m-d');

        // Calculate the subscription end date excluding weekends, with 2 extra bonus days
        $subscriptionEndDate = addDaysSkippingWeekends($subscriptionStartDate, $plan['duration_days'] + 2);

        $sql = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
                VALUES (:user_id, :crypto_type, :amount, :transaction_id, :type, 'completed')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'user_id' => $userId,
            'crypto_type' => 'subscription',
            'amount' => $subscriptionPrice,
            'transaction_id' => $transactionId,
            'type' => 'debit'
        ]);

        // Insert subscription
        $stmt = $pdo->prepare("INSERT INTO user_subscriptions (user_id, plan_id, subscription_start_date, subscription_end_date) VALUES (?, ?, ?, ?)");
        $stmt->execute([$userId, $planId, $subscriptionStartDate, $subscriptionEndDate]);

        // Increment the number of subscriptions for the user
        $stmt = $pdo->prepare("UPDATE user_profiles SET subscribed = subscribed + 1 WHERE user_id = ?");
        $stmt->execute([$userId]);

        echo json_encode(['status' => 'success', 'message' => 'Subscription activated successfully']);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

/**
 * Add days to a given date, skipping Saturdays and Sundays.
 *
 * @param string $startDate
 * @param int $daysToAdd
 * @return string
 */
function addDaysSkippingWeekends($startDate, $daysToAdd) {
    $date = new DateTime($startDate);
    $addedDays = 0;

    while ($addedDays < $daysToAdd) {
        $date->modify('+1 day');
        // Skip Saturday and Sunday
        if ($date->format('N') < 6) { 
            $addedDays++;
        }
    }

    return $date->format('Y-m-d');
}
