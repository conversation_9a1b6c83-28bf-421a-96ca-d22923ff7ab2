<?php
require_once __DIR__ . '/../php_files/db.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize variables with default values
$user_id = $_SESSION['user_id'] ?? null;
$email = $_SESSION['email'] ?? null;
$user_type = $_SESSION['user_type'] ?? null;
$username = $_SESSION['username'] ?? null;

// Check user type and redirect if unauthorized
if (!in_array($user_type, ['superadmin', 'admin'])) {
    // Redirect back to the previous page if referer exists
    if (!empty($_SERVER['HTTP_REFERER'])) {
        header('Location: ' . $_SERVER['HTTP_REFERER']);
    } else {
        // Redirect to login page if no referer
        header('Location: ' . BASE_URL . '/signin.htm');
    }
    exit;
}

// Check if user ID is valid
if (empty($user_id) || $user_id <= 0) {
    session_destroy();
    header('Location: ' . BASE_URL . '/signin.htm');
    exit;
}
