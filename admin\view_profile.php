<?php
require '../assets/admin_header.php';
require '../php_files/db.php';
require '../php_files/functions.php';

    $user_id = $_GET['user_id'];
    if (!$user_id) {
        header('Location', BASE_URL . 'admin/dashboard');
        exit;
    }
    global $pdo;

    try {
        global $pdo;
        // Query to fetch all users
        $users_query = "SELECT * FROM user_profiles WHERE user_type = 'regular' AND status = 'active' AND user_id = $user_id";
        $stmt = $pdo->prepare($users_query);
        $stmt->execute();
        $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $subscribers_query = "SELECT * FROM subscription_plans";
        $stmt_subs = $pdo->prepare($subscribers_query);
        $stmt_subs->execute();
        $all_subscriptions = $stmt_subs->fetchAll(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        echo 'Connection failed: ' . $e->getMessage();
    }

    // 1. Fetch the user profile data
    $stmt_profile = $pdo->prepare("
        SELECT 
            up.user_id,
            up.username,
            up.email,
            up.profile_picture,
            up.bio,
            up.background_image,
            up.user_type,
            up.subscribed,
            up.status,
            up.result_mail,
            up.email_log_unlocked,
            up.domain_change_count,
            up.last_domain_change,
            w.balance
        FROM user_profiles AS up
        LEFT JOIN wallet AS w ON up.user_id = w.user_id
        WHERE up.user_id = :user_id
    ");

    // Bind and execute the user profile query
    $stmt_profile->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt_profile->execute();
    $user_profile = $stmt_profile->fetch(PDO::FETCH_ASSOC);

    // 2. Fetch the user subscriptions (if any)
    $stmt_subscriptions = $pdo->prepare("
        SELECT
            us.plan_id,
            us.subscription_start_date,
            us.subscription_end_date,
            sp.plan_name,
            sp.price,
            sp.duration_days,
            sp.description
        FROM user_subscriptions AS us
        LEFT JOIN subscription_plans AS sp ON us.plan_id = sp.id
        WHERE us.user_id = :user_id
        AND us.subscription_end_date > NOW()
    ");

    // Bind and execute the user subscription query
    $stmt_subscriptions->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt_subscriptions->execute();
    $active_subscriptions = $stmt_subscriptions->fetchAll(PDO::FETCH_ASSOC);

    // Prepare the final data array
    $data = [
        'user_profile' => $user_profile,
        'active_subscriptions' => $active_subscriptions
    ];

    $currentDate = new DateTime();
    $currentDate->setTime(0, 0); // Set current date to midnight

    // Now the $data array contains both the user profile and their active subscriptions
    // You can proceed to use this data as needed, for example:
    // echo '<pre>'; print_r($data); echo '</pre>';

    // Optional: Delete inactive plans for the user
    deleteInactivePlans($user_id);

if ( $user_profile['status'] == 'locked' ) {
    $button_action = '<li><button id="key-'. $user_profile['user_id'] .'" onclick="unlockUser('. $user_profile['user_id']
        .')" class="dropdown-item" type="button"> Unlock User </button></li>';
} else {
    $button_action = '<li><button id="key-'. $user_profile['user_id'] .'" onclick="lockUser('. $user_profile['user_id'] .')" class="dropdown-item" type="button"> Lock User </button></li>';
}

    // Fetch anti-bot settings for a specific user
    $userSettingsStmt = $pdo->prepare("
        SELECT u.user_id AS user_id, u.username, a.type, us.state
        FROM user_profiles u
        JOIN user_antibot_settings us ON u.user_id = us.user_id
        JOIN antibottoggle_states a ON us.antibot_id = a.id
        WHERE u.user_id = :user_id
    ");
    $userSettingsStmt->execute([':user_id' => $user_id]);
    $userSettings = $userSettingsStmt->fetchAll(PDO::FETCH_ASSOC);

   // echo '<pre>'; print_r($userSettings); echo '</pre>';
?>
<style>
    #image {
        height: 100px;
        width: 100px;
        border-radius: 10px;
        object-fit: contain;
    }
</style>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h4">User Profile</h1>
        <div class="btn-group">
            <button class="btn btn-secondary border-0 btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                User Actions
            </button>
            <ul class="dropdown-menu">

                <?= $button_action; ?>

                <?php if ($user_profile['subscribed'] > 0): ?>
                    <li>
                        <button type="button" class="open-modal-extra dropdown-item" data-bs-toggle="modal" data-user-id="<?= $user_profile['user_id'] ?>" data-bs-target="#addExtraDays">Add extra days</button>
                    </li>
                    <li>
                        <button class="dropdown-item" type="button" onclick="cancelAllSubsForUser(<?= $user_profile['user_id'] ?>)">Cancel subscriptions</button>
                    </li>
                    <li>
                        <button class="dropdown-item" type="button" onclick="resetSubscription(<?= $user_profile['user_id'] ?>)">Reset subscriptions</button>
                    </li>
                <?php endif; ?>

                <li>
                    <button class="dropdown-item" type="button" onclick="resetPassword(<?= $user_profile['user_id'] ?>)">Reset password</button>
                </li>
                <li>
                    <button type="button" class="open-modal-extra open-email-model dropdown-item" data-bs-toggle="modal" data-user-id="<?= $user_profile['user_id'] ?>" data-email="<?= $user_profile['email'] ?>" data-bs-target="#emailUpdate">Update email</button>
                </li>
                <li>
                    <button type="button" class="open-modal-extra open-wallet-model dropdown-item" data-bs-toggle="modal" data-user-id="<?= $user_profile['user_id'] ?>" data-bs-target="#walletUpdate">Fund user wallet</button>
                </li>
                <li>
                    <button type="button" class="open-modal-extra open-subscribe-model dropdown-item" data-bs-toggle="modal" data-user-id="<?= $user_profile['user_id'] ?>" data-bs-target="#subscribeModal">Subscribe user</button>
                </li>
            </ul>
        </div>
    </div>
    <div class="container mt-5">
        <div class="profile-header d-flex flex-row justify-content-between align-items-center py-2">
            <img id="image"
                 src="<?= !empty($user_profile['profile_picture']) ? $user_profile['profile_picture'] : BASE_URL . '/uploads/blankprofile.webp' ?>"
                 alt="Profile Picture"
                 class="profile-picture img-thumbnail">
            <div class="card">
                <div class="card-header">
                    Wallet Balance
                </div>
                <div class="card-body">
                    <blockquote class="blockquote mb-0">
                        <p>$<?= isset($user_profile['balance']) ? htmlspecialchars($user_profile['balance']) : 'null' ?></p>
                    </blockquote>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="mb-3 col-md-6">
                <label for="firstName" class="form-label">Username</label>
                <input disabled readonly type="text" class="form-control" id="firstName" value="<?= isset($user_profile['username']) ? htmlspecialchars($user_profile['username']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-6">
                <label for="lastName" class="form-label">Email</label>
                <input disabled readonly type="text" class="form-control" id="lastName" value="<?= isset($user_profile['email']) ? htmlspecialchars($user_profile['email']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-4">
                <label for="organization" class="form-label">User Type</label>
                <input disabled readonly type="text" class="form-control" id="organization" value="<?= isset($user_profile['user_type']) ? htmlspecialchars($user_profile['user_type']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-4">
                <label for="email" class="form-label">Email Log Unlocked</label>
                <input disabled readonly type="text" class="form-control" id="email" value="<?= isset($user_profile['email_log_unlocked']) ? htmlspecialchars($user_profile['email_log_unlocked']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-4">
                <label for="phone" class="form-label">Domain Change Count</label>
                <input disabled readonly type="text" class="form-control" id="phone" value="<?= isset($user_profile['domain_change_count']) ? htmlspecialchars($user_profile['domain_change_count']) : 'null' ?>">
            </div>

            <div class="mb-3 col-md-6">
                <label for="address" class="form-label">Bio</label>
                <textarea class="form-control" id="address" rows="3" readonly disabled><?= isset($user_profile['bio']) ? htmlspecialchars($user_profile['bio']) : 'null' ?></textarea>
            </div>
        </div>

        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h5">Active Subscriptions</h1>
        </div>
        <div class="row row-cols-1 row-cols-md-3 mb-3 text-center">
            <?php
            if (isset($data['active_subscriptions']) && is_array($data['active_subscriptions'])) {
                foreach ($data['active_subscriptions'] as $plan):
                    $endDate = new DateTime($plan['subscription_end_date']);
                    $endDate->setTime(0, 0); // Set end date to midnight
                    $interval = $currentDate->diff($endDate);
                    $plan['days_left'] = $interval->days; // Number of days left
                    ?>
                    <div class="col">
                        <div class="card mb-4 rounded-3 shadow-sm">
                            <div class="card-header py-3">
                                <h4 class="my-0 fw-normal"><?= htmlspecialchars($plan['plan_name'] ?? 'N/A'); ?></h4>
                            </div>
                            <div class="card-body">
                                <h1 class="card-title pricing-card-title">$<?= htmlspecialchars($plan['price'] ?? '0.00'); ?></h1>
                                <h1 class="card-title pricing-card-title">
                                    <small class="text-body-secondary fw-light">Remaining Days: <?= $plan['days_left'] ?? 'N/A'; ?></small>
                                </h1>
                            </div>
                        </div>
                    </div>
                <?php endforeach;
            } else {
                echo '<div class="col"><p>No active subscriptions found.</p></div>';
            } ?>
        </div>


        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h5"> User AntiBot Settings </h1>
        </div>
                    <div class="row p-2">
                    <?php 
                        foreach ($userSettings as $setting) {
                            echo '
                            <div class="col-md-6 border p-1">
                                <div class="form-check form-switch disabled-checkbox">
                                    <input 
                                        class="form-check-input" 
                                        type="checkbox" 
                                        id="' . $setting['type'] . 'Toggle" 
                                        ' . ($setting['state'] === 'on' ? 'checked' : '') . ' 
                                        onclick="toggleDetection(\'' . $setting['type'] . '\')"
                                    />
                                    <label class="form-check-label" for="' . $setting['type'] . 'Toggle">' . $setting['type'] . '</label>
                                </div>
                            </div>';
                        }
                    ?>

                    </div>
            </div>
    </div>


    <!-- Modal -->
    <div class="modal fade" id="addExtraDays" aria-hidden="true"
         aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <form id="addDaysForm" class="w-100">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add extra days
                        </h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="exampleFormControlInput1" class="form-label"> Add extra
                                subscription days</label>
                            <input type="number" required class="form-control"
                                   id="exampleFormControlInput1" name="days"
                                   placeholder="add days">
                            <input type="hidden" readonly value="" id="user_id"
                                   name="user_id">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" id="submitForm"> Save </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade" id="addExtraDaysToAll" aria-hidden="true"
         aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <form id="addDaysToAllForm" class="w-100">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Add extra
                            days for all subscribers
                        </h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="exampleFormControlInput1" class="form-label"> Add
                                extra subscription days to all</label>
                            <input type="number" required class="form-control"
                                   id="exampleFormControlInput1" name="days"
                                   placeholder="add days">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" id="submitFormToAll"> Save </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade" id="emailUpdate" aria-hidden="true"
         aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <form id="emailUpdateForm" class="w-100">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalToggleLabel"> Update email
                        </h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <input type="email" required class="form-control"
                                   id="updateEmail" name="email"
                                   placeholder="add days">
                            <input id="email-update-user-id" type="hidden" name="user-id">

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" id="submitForEmail"> Save </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade" id="walletUpdate" aria-hidden="true"
         aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <form id="walletUpdateForm" class="w-100">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalToggleLabel">Fund user
                            wallet
                        </h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="exampleInputEmail1" class="form-label">Enter amount
                                (USDT)</label>
                            <input type="number" required name="amount" class="form-control"
                                   id="walletInput"
                                   aria-describedby="emailHelp">
                            <input id="wallet-user-id" type="hidden" name="user-id">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" id="submitWalletForm"> Save </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade" id="subscribeModal" aria-hidden="true"
         aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <form id="subscribeForm" class="w-100">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalToggleLabel">Fund user
                            wallet
                        </h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="exampleInputEmail1" class="form-label"> Choose a
                                subscription plan </label>
                            <select name="plan_id" class="form-select" aria-label="Default
                                select
                                example">
                                <?php
                                foreach (  $all_subscriptions as $sub ) {
                                    echo '
                                                <option value="'. $sub['id'] .'">'. $sub['plan_name']
                                        .'</option>  
                                            ';
                                }
                                ?>
                            </select>
                            <input id="sub-user-id" type="hidden" name="user_id">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary"
                                id="submitSubscribeForm">Subscribe</button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal"> Cancel </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    </div>



</main>

<script src="../js/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

    //function to lock a user with the user-id
    const lockUser = ( user_id ) => {
        $.ajax({
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php',
            type: 'POST',
            data: {
                user_id ,
                action: 'lock'
            },
            dataType: 'json',
            success: function(response) {
                $('#key-' + user_id).text('locked');
                alert( response.message )
                window.location.reload()
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ');
                console.log('An error occurred: ' , error);
            }
        });
    }


    //function to unlock a user with the user-id
    const unlockUser = ( user_id ) => {
        $.ajax({
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php',
            type: 'POST',
            data: {
                user_id ,
                action: 'unlock'
            },
            dataType: 'json',
            success: function(response) {
                $('#key-' + user_id).text('active');
                alert( response.message );
                window.location.reload();
                console.log(response);
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ');
                console.log('An error occurred: ' , error);
            }
        });
    }


    //function to set the user-id into a hidden input when opening a modal
    $('.open-modal-extra').on('click', function() {
        let user_id = $(this).data('user-id');
        // Set the form fields in the modal
        $('#user_id').val(user_id);
    });

    //function to set the user-id into a hidden input when clicked to update user email
    $('.open-email-model').on('click', function() {
        let user_id = $(this).data('user-id');
        let email = $(this).data('email');
        // Set the form fields in the modal
        $('#email-update-user-id').val(user_id);
        $('#updateEmail').val(email);
    });

    //function to set the user-id into a hidden input when clicked to fund user wallet
    $('.open-wallet-model').on('click', function() {
        let user_id = $(this).data('user-id');
        $('#wallet-user-id').val(user_id);
    });

    //function to set the user-id into a hidden input when clicked to subscribe for a user
    $('.open-subscribe-model').on('click', function() {
        let user_id = $(this).data('user-id');
        $('#sub-user-id').val(user_id);
    });

    //function to add days to the subscription of a user
    $('#submitSubscribeForm').on('click', function(e) {
        e.preventDefault();
        let formData = $('#subscribeForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=subscribe',
            data: formData,
            success: function(response) {
                alert(response.message);
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });


    //function to add days to the subscription of a user
    $('#submitForm').on('click', function(e) {
        e.preventDefault();
        let formData = $('#addDaysForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=addDays',
            data: formData,
            success: function(response) {
                $('#walletInput').val('');
                alert('Operation successful');
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });


    //function to add days to the subscription of a user
    $('#submitWalletForm').on('click', function(e) {
        e.preventDefault();
        let formData = $('#walletUpdateForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=fund',
            data: formData,
            success: function(response) {
                alert('Operation successful');
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });


    //function to add days to the subscription of all users
    $('#submitFormToAll').on('click', function(e) {
        e.preventDefault();
        let formData = $('#addDaysToAllForm').serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=addDaysToAll',
            data: formData,
            success: function(response) {
                alert('Operation successful');
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });


    //function to update the email of a user
    $('#emailUpdateForm').on('submit', function(e) {
        e.preventDefault();
        let formData = $(this).serialize(); // Serialize the form data

        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=updateEmail',
            data: formData,
            success: function(response) {
                alert(response.message);
                location.reload(); // Reload the page
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });

    //function to cancel the subscription of a user
    const cancelAllSubsForUser = ( user_id ) => {
        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=cancelSubscription',
            data: {
                user_id
            },
            success: function(response) {
                alert('Subscription Cancelled. Please reload the page to see the effect');
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    }

    //function to reset the subscription of a user
    const resetSubscription = ( user_id ) => {
        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/subscription_actions.php?action=resetSubscription',
            data: {
                user_id
            },
            success: function(response) {
                alert('Subscription reset successful.');
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    }

    //function to reset the password of a user and mail it to them
    const resetPassword = ( user_id ) => {
        $.ajax({
            type: 'POST',
            url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=resetPassword',
            data: {
                user_id
            },
            success: function(response) {
                alert(response.message);
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    }
    
    function toggleDetection(type) {
            const checkbox = document.getElementById(`${type}Toggle`);
            const value = checkbox.checked ? 'on' : 'off';

            const user_id = <?= $user_id ?>
            
            console.log( type , value , user_id );
            
            
            $.ajax({
                type: 'GET',
                url: '<?= BASE_URL?>/php_files/admin/user_actions.php?action=updateAntiBotSetting',
                data: { type: type, value: value , user_id },
                success: function(response) {
                    if ( response.status === "success" ) {
                        console.log( response )
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error storing toggle state:', textStatus, errorThrown);
                }
            });
        }

        $.ajax({
                type: 'GET',
                url: '<?= BASE_URL?>/php_files/admin/api.php',
                success: function(response) {
                    console.log( response )
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error storing toggle state:', textStatus, errorThrown);
                }
            });

</script>