<?php



 require "../db.php";
 require "../functions.php";
 
 
 

function addSubscriptionDays($userId, $days) {
    global $pdo;

    // Fetch current subscription end date
    $stmt = $pdo->prepare("SELECT subscription_end_date FROM user_subscriptions WHERE user_id = ?");
    $stmt->execute([$userId]);
    $currentEndDate = $stmt->fetchColumn() ?? date('Y-m-d');

    // Calculate new subscription end date
    $newEndDate = addBusinessDays($currentEndDate, $days);

    // Update subscription
    $stmt = $pdo->prepare("UPDATE user_subscriptions SET subscription_end_date = ? WHERE user_id = ?");
    $stmt->execute([$newEndDate, $userId]);

    return json_encode(['status' => $stmt->rowCount() > 0 ? 'success' : 'failure', 'message' => 'Subscription updated.']);
}
  


function addSubscriptionDaysToAll($days) {
    global $pdo;

    // Fetch user subscription data
    $stmt = $pdo->query("SELECT user_id, subscription_end_date FROM user_subscriptions");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Loop over each user and update their subscription end date with business days
    foreach ($users as $user) {
        $currentEndDate = $user['subscription_end_date'] ?? date('Y-m-d'); // If null, use today's date
        $newEndDate = addBusinessDays($currentEndDate, $days); // Calculate new end date

        // Update the subscription end date for the user
        $updateStmt = $pdo->prepare("
            UPDATE user_subscriptions 
            SET subscription_end_date = :new_end_date 
            WHERE user_id = :user_id
        ");

        $updateStmt->execute([
            'new_end_date' => $newEndDate,
            'user_id' => $user['user_id']
        ]);
    }

    if ($updateStmt->rowCount() > 0) {
        return json_encode(['status' => 'success', 'message' => 'Subscription extended for all users successfully.']);
    } else {
        return json_encode(['status' => 'failure', 'message' => 'No updates made to any user.']);
    }
}


function cancelSubscription($userId) {
    global $pdo;
    $pdo->beginTransaction();

    try {
        // Cancel all subscriptions for this user
        $stmt = $pdo->prepare("DELETE FROM user_subscriptions WHERE user_id = ?");
        $stmt->execute([$userId]);

        if ($stmt->rowCount() > 0) {
            // Set subscribed value to 0 in user_profiles
            $stmt = $pdo->prepare("UPDATE user_profiles SET subscribed = 0 WHERE user_id = ?");
            $stmt->execute([$userId]);

            // Commit the transaction
            $pdo->commit();

            return ['status' => 'success', 'message' => 'User subscriptions cancelled successfully, subscribed status reset to 0'];
        } else {
            // No subscriptions found for this user
            $pdo->rollBack();
            return ['status' => 'error', 'message' => 'No subscriptions found for this user'];
        }
    } catch (PDOException $e) {
        // Rollback transaction in case of error
        $pdo->rollBack();
        return ['status' => 'error', 'message' => 'An error occurred: ' . $e->getMessage()];
    }
}



function resetSubscriptionPlans($userId) {
    global $pdo;
    try {
        // Begin transaction
        $pdo->beginTransaction();

        // Fetch all current subscriptions for the user
        $stmt = $pdo->prepare("SELECT id, plan_id FROM user_subscriptions WHERE user_id = ?");
        $stmt->execute([$userId]);
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!$subscriptions) {
            $pdo->rollBack();
            return ['status' => 'error', 'message' => 'No subscriptions found for this user'];
        }

        // Iterate through each subscription and update it
        foreach ($subscriptions as $subscription) {
            $planId = $subscription['plan_id'];

            // Fetch plan details
            $stmt = $pdo->prepare("SELECT duration_days FROM subscription_plans WHERE id = ?");
            $stmt->execute([$planId]);
            $plan = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($plan) {
                $subscriptionStartDate = date('Y-m-d');
                $subscriptionEndDate = date('Y-m-d', strtotime("+{$plan['duration_days']} days"));

                // Update subscription
                $stmt = $pdo->prepare("
                    UPDATE user_subscriptions 
                    SET subscription_start_date = ?, subscription_end_date = ?
                    WHERE id = ?");
                $stmt->execute([$subscriptionStartDate, $subscriptionEndDate, $subscription['id']]);
            }
        }

        // Commit the transaction
        $pdo->commit();

        return ['status' => 'success', 'message' => 'All subscriptions reset successfully'];
    } catch (PDOException $e) {
        $pdo->rollBack();
        return ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
}



function addSubForUser($userId , $planId) {

    global $pdo;

    // Validate input data
    if (empty($planId)) {
        return ['status' => 'error', 'message' => 'Invalid input data'];
        exit;
    }

    try {
        // Fetch plan details
        $stmt = $pdo->prepare("SELECT * FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        $plan = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$plan) {
            return ['status' => 'error', 'message' => 'Plan not found'];
            exit;
        }

        $subscriptionPrice = floatval($plan['price']); // Assume the subscription price is stored in the 'price' column

        // Fetch user's wallet balance
        $stmt = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = ?");
        $stmt->execute([$userId]);
        $wallet = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$wallet || floatval($wallet['balance']) < $subscriptionPrice) {
            return ['status' => 'error', 'message' => 'Insufficient wallet balance'];
            exit;
        }

        // Deduct the subscription price from the wallet
        $newBalance = floatval($wallet['balance']) - $subscriptionPrice;
        $stmt = $pdo->prepare("UPDATE wallet SET balance = ? WHERE user_id = ?");
        $stmt->execute([$newBalance, $userId]);

        $transactionId = uniqid('txn_');

        // Proceed with subscription logic
        $subscriptionStartDate = date('Y-m-d');
        $subscriptionEndDate = date('Y-m-d', strtotime("+{$plan['duration_days']} days", strtotime($subscriptionStartDate)));

        $sql = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
                VALUES (:user_id, :crypto_type, :amount, :transaction_id, :type, 'completed')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'user_id' => $userId,
            'crypto_type' => 'subscription',
            'amount' => $subscriptionPrice,
            'transaction_id' => $transactionId,
            'type' => 'debit'
        ]);

        // Insert subscription
        $stmt = $pdo->prepare("INSERT INTO user_subscriptions (user_id, plan_id, subscription_start_date, subscription_end_date) VALUES (?, ?, ?, ?)");
        $stmt->execute([$userId, $planId, $subscriptionStartDate, $subscriptionEndDate]);

        // Increment the number of subscriptions for the user
        $stmt = $pdo->prepare("UPDATE user_profiles SET subscribed = subscribed + 1 WHERE user_id = ?");
        $stmt->execute([$userId]);

        return ['status' => 'success', 'message' => 'Subscription activated successfully'];
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
}




$action = $_GET['action'] ?? null;


$response = null;  // Initialize response to avoid undefined variable error

// Your conditional checks here (addDays, cancelSubscription, etc.)

if ($action === 'addDays') {
    if (!isset($_POST['user_id'], $_POST['days'])) {
        $response = ['status' => 'error', 'message' => 'Missing user_id or days'];
    } else {
        $response = addSubscriptionDays($_POST['user_id'], $_POST['days']);
    }
} elseif ($action === 'addDaysToAll') {
    $response = addSubscriptionDaysToAll($_POST['days']);
} elseif ($action === 'cancelSubscription') {
    $response = cancelSubscription($_POST['user_id']);
} elseif ($action === 'resetSubscription') {
    $response = resetSubscriptionPlans($_POST['user_id']);
} elseif ($action === 'subscribe') {
    $response = addSubForUser($_POST['user_id'], $_POST['plan_id']);
} else {
    $response = ['status' => 'error', 'message' => "Invalid action: " . $action];
}

header('Content-Type: application/json');
echo json_encode($response);
