<?php
// Include database connection or other necessary files
include '../db.php';
include '../functions.php';

/**
 * Ensure the database has the per_day_cost column.
 */
function ensureColumnExists() {
    global $pdo;
    $query = "SHOW COLUMNS FROM subscription_plans LIKE 'per_day_cost'";
    $stmt = $pdo->query($query);
    if ($stmt->rowCount() === 0) {
        $pdo->query("ALTER TABLE subscription_plans ADD COLUMN per_day_cost DECIMAL(10,2) NOT NULL DEFAULT 0");
    }
}

/**
 * Edit a subscription plan and update per-day cost automatically.
 */
function editPlan($planId, $planName, $price, $durationDays, $description) {
    global $pdo;

    if ($durationDays <= 0) {
        return ['status' => 'error', 'message' => 'Duration must be greater than zero.'];
    }

    $perDayCost = round($price / $durationDays, 2); // Calculate per-day cost

    $stmt = $pdo->prepare("UPDATE subscription_plans SET plan_name = ?, price = ?, duration_days = ?, per_day_cost = ?, description = ? WHERE id = ?");
    return $stmt->execute([$planName, $price, $durationDays, $perDayCost, $description, $planId]);
}

/**
 * Delete a subscription plan only if it is not being used.
 * If in use, return an error message.
 */
function deletePlan($planId) {
    global $pdo;

    // Check if any user subscriptions reference the plan
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_subscriptions WHERE plan_id = ?");
    $stmt->execute([$planId]);
    $count = $stmt->fetchColumn();

    if ($count > 0) {
        return ['status' => 'error', 'message' => 'Plan cannot be deleted because it has active subscriptions.'];
    }

    // If no subscriptions reference the plan, proceed with deletion.
    $stmt = $pdo->prepare("DELETE FROM subscription_plans WHERE id = ?");
    if ($stmt->execute([$planId])) {
        return ['status' => 'success'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to delete the plan due to a database error.'];
    }
}

// Ensure the required column exists
ensureColumnExists();

// Handle AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'];

    if ($action === 'edit') {
        $planId       = sanitize($_POST['plan_id']);
        $planName     = sanitize($_POST['plan_name']);
        $price        = sanitize($_POST['price']);
        $durationDays = sanitize($_POST['duration_days']);
        $description  = sanitize($_POST['description']);

        $result = editPlan($planId, $planName, $price, $durationDays, $description);
        if ($result) {
            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update the plan.']);
        }
    } elseif ($action === 'delete') {
        $planId = sanitize($_POST['plan_id']);
        $result = deletePlan($planId);
        echo json_encode($result);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Invalid action.']);
    }
}
?>
