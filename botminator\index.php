

<?php
session_start();
// Include the extracted config.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a connection to the MySQL database
$conn = new mysqli($host, $username, $password, $dbname);

// Check if the connection was successful
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set the character set to UTF-8 for proper handling of special characters
$conn->set_charset("utf8");

// Optionally, you can define a function to close the connection when done
function closeDbConnection() {
    global $conn;
    $conn->close();
}

$user_id = $_SESSION['user_id']; // Get signed-in user ID from session

// Display available plans based on user's current plan
$currentPlanDetails = displayAvailablePlans($user_id);

function displayAvailablePlans($user_id) {
    global $conn;

    // Fetch the user's current subscription plan from `user_subscriptions`
    // Join with subscription_plans to get the correct plan name
    $query = "SELECT us.plan_id, sp.plan_name, sp.price, sp.duration_days
              FROM user_subscriptions us
              JOIN subscription_plans sp ON us.plan_id = sp.id
              WHERE us.user_id = $user_id
              ORDER BY us.subscription_end_date DESC LIMIT 1";

    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        // Fetch the plan details
        $row = $result->fetch_assoc();
        $currentPlan = $row['plan_name']; // Correct plan name from subscription_plans
        $price = $row['price'];
        $duration = $row['duration_days'];

        
        
         // Return the active subscription details as an array
        return [
            'plan_name' => $currentPlan,
            'price' => $price,
            'duration_days' => $duration
        ];
        
    } else {
      
    }
}


// Query to retrieve all plans from the subscribeSBBsaSplans table
$query = "SELECT plan_name, duration, price FROM subscribeSBBsaSplans";
$result = $conn->query($query);

$currentPlanDetails = displayAvailablePlans($user_id);



// Extract the current subscription details from the returned array
$currentPlanName = $currentPlanDetails['plan_name'];
$currentPlanDuration = $currentPlanDetails['duration_days'];


// Query to check if the user has an active subscription
$activeSubscriptionQuery = "SELECT subscribeopenredirectplan, subscription_end_date 
                            FROM subscribeSBBsaS 
                            WHERE user_id = $user_id AND subscription_end_date >= CURDATE() 
                            LIMIT 1";
$activeSubscriptionResult = $conn->query($activeSubscriptionQuery);

$activeSubscription = null;
if ($activeSubscriptionResult->num_rows > 0) {
    $activeSubscription = $activeSubscriptionResult->fetch_assoc();
}

// If there is no active subscription, show the specific plan format
if (!$activeSubscription) {
    


// Check if any rows are returned
if ($result->num_rows > 0) {
    // Loop through the results and store values in variables
    while ($row = $result->fetch_assoc()) {
        $planName = $row['plan_name'];
        $planDuration = $row['duration'];
        $planPrice = $row['price'];


        // Check if the plan name and duration match with the active subscription
        if (stripos($planName, $planName) !== false && $currentPlanDuration == $planDuration) {
            
        // Echo the plan details without 'id'
    echo "<script>";
    echo "var plan = {";
    echo "name: '" . $planName . "',";
    echo "duration: " . $planDuration . ",";
    echo "price: " . $planPrice;
    echo "};";
    echo "</script>";

            
            
         
        }
    }
} else {
    
}

} else {
    
    
}


?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Page</title>
    <style>
        /* Simple styles for layout */
        .plan-container {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
        }

        .plan-container button {
            margin-top: 10px;
        }
    </style>
 <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 45%;
        }
        .input {
            padding: 10px;
            font-size: 16px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 80%;
        }
        .btn {
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background: #007bff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
           
            text-align: left;
        }
        .result h4 {
            margin-top: 10px;
        }
        .result p {
            margin: 5px 0;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>Botminator - Powerful security evasion tool</h1>
           
            <p>Blocks automated bots that categorize links as spam or phishing. Stops bots from scanning RaccoonO365 link, we use advanced server side techniques to hide phishing content from automated bot-driven analysis. 

</p>


<div id="subscriptionStatus"></div>
    <div id="plansContainer"></div>
    
        
    </div>
    

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    // Fetch the available plans and display them
    function fetchPlans() {
        $.ajax({
            url: 'botminatorapi.php',
            method: 'GET',
            success: function(data) {
                $('#plansContainer').html(data);
            },
            error: function(xhr, status, error) {
                console.error('Error fetching plans:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to fetch plans. Please try again later.'
                });
            }
        });
    }

    // Handle subscription
    function subscribe(plan) {
        $.ajax({
            url: 'botminatorapi.php',
            method: 'POST',
            data: {
                action: 'subscribe',
                plan_name: plan.name,
                price: plan.price
            },
            success: function(data) {
                Swal.fire({
                    icon: 'success',
                    title: 'Subscription Successful',
                    text: data
                });
                fetchPlans(); // Reload the plan view to show updated status
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Subscription Failed',
                    text: 'An error occurred while subscribing. Please try again.'
                });
            }
        });
    }

    // Handle renewal
    function renewSubscription() {
        $.ajax({
            url: 'botminatorapi.php',
            method: 'POST',
            data: { action: 'renew' },
            success: function(data) {
                Swal.fire({
                    icon: 'success',
                    title: 'Renewal Successful',
                    text: data
                });
                fetchPlans(); // Refresh the plan view
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Renewal Failed',
                    text: 'An error occurred while renewing. Please try again.'
                });
            }
        });
    }

    // Fetch the initial status and plans on page load
    $(document).ready(function() {
        fetchPlans(); // Load plans on page load
    });
</script>



    
</body>
</html>
