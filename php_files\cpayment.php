<?php
function createPayment($amount, $currency = 'USD') {
    $api_key = 'fcccc1ad7aa4fa11bbf5d544f74f2069fc9826b13eaee22436f1de510c51804c'; // Replace with your API key
    $api_secret = 'ef9a519903560cc96f1fc1dbf7937e637e2e1fb6010c9c567e8b9283da0578be'; // Replace with your API secret

    $post_data = array(
        'version' => 1,
        'key' => $api_key,
        'amount' => $amount,
        'currency1' => $currency,
        'currency2' => 'BTC', // Crypto you want to accept
        'item_name' => 'Unlock Email Logs Feature',
        'invoice' => uniqid(), // Unique invoice number
        'success_url' => 'http://127.0.0.1:8000/success', // Success URL
        'cancel_url' => 'http://127.0.0.1:8000/cancel', // Cancel URL
        'ipn_url' => 'http://127.0.0.1:8000/ipn', // Instant Payment Notification URL
    );

    // Create a signature
    $post_data['sign'] = hash_hmac('sha512', json_encode($post_data), $api_secret);

    // Initialize cURL
    $ch = curl_init('https://api.coinpayments.net/api.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));

    // Execute the request
    $response = curl_exec($ch);
    curl_close($ch);

    // Decode the response
    $result = json_decode($response, true);

    if ($result['error'] === 'ok') {
        return $result['result']['payment_url'];
    } else {
        return 'Error creating payment: ' . $result['error'];
    }
}

// Example usage
$paymentUrl = createPayment(14.00); // Amount in USD
if (strpos($paymentUrl, 'Error') === false) {
    echo "Redirect to: " . $paymentUrl; // Redirect user to payment URL
} else {
    echo $paymentUrl; // Show error message
}