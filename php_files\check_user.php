<?php
require 'db.php'; // Ensure this file contains the correct PDO database connection code


// Start the session to access session variables
session_start();

// Check if the user is signed in (assuming you store user_id in the session)
if (!isset($_SESSION['user_id'])) {
    // Redirect to Google if the user is not signed in
    header("Location: https://www.google.com");  // Redirects to Google
    exit;  // Ensure the script stops executing after redirect
}


// Send a JSON response
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = filter_var($_POST['username'], FILTER_SANITIZE_SPECIAL_CHARS);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

    // Prepare and execute SQL statement to check for existing usernames or emails
    $stmt = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = :username OR email = :email");
    $stmt->execute(['username' => $username, 'email' => $email]);

    if ($stmt->rowCount() > 0) {
        // Determine which field caused the issue
        if ($username) {
            echo json_encode(['status' => 'error', 'field' => 'username', 'message' => 'Username already exists.']);
        } else {
            echo json_encode(['status' => 'error', 'field' => 'email', 'message' => 'Email already exists.']);
        }
    } else {
        echo json_encode(['status' => 'success']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method.']);
}
