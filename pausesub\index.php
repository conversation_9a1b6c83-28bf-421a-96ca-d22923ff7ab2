
<?php

// Start session to manage user authentication
session_start();
// Set session cookie parameters first
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict'   // Mitigate CSRF attacks
]);


// Check if the user is signed in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not authenticated
    header("Location: ../logout.php");  // Redirects to Google login or similar page
    exit;
}
?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Control</title>

    <!-- SweetAlert CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom CSS -->
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            text-align: center;
            margin-top: 50px;
        }

        h1 {
            color: #333;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #45a049;
        }

        button:active {
            background-color: #3e8e41;
        }

        .container {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-direction: column;
        }
    </style>
    
     <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 50px;
        }
        #subscriptionStatus {
            font-size: 18px;
            margin-top: 20px;
            padding: 10px;
            display: inline-block;
            border-radius: 5px;
        }
        .active {
            color: green;
            font-weight: bold;
        }
        .paused {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>

<h1>Subscription Control</h1>

<div class="container">
    <button id="pauseSubscription">Pause Subscription</button>
    <button id="resumeSubscription">Resume Subscription</button>
    
    <h2>RaccoonO365 Subscription Status</h2>
<p id="subscriptionStatus">Checking status...</p>

</div>

<script>
$(document).ready(function() {
    $("#pauseSubscription").click(function() {
        $.ajax({
            url: "pause_subscription.php",
            type: "POST",
            success: function(response) {
                var data = JSON.parse(response);
                // Using SweetAlert for success/failure
                Swal.fire({
                    icon: data.status === "success" ? 'success' : 'error',
                    title: data.status === "success" ? 'Success!' : 'Error!',
                    text: data.message,
                });
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'There was an issue with the request.',
                });
            }
        });
    });

    $("#resumeSubscription").click(function() {
        $.ajax({
            url: "resume_subscription.php",
            type: "POST",
            success: function(response) {
                var data = JSON.parse(response);
                // Using SweetAlert for success/failure
                Swal.fire({
                    icon: data.status === "success" ? 'success' : 'error',
                    title: data.status === "success" ? 'Success!' : 'Error!',
                    text: data.message,
                });
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'There was an issue with the request.',
                });
            }
        });
    });
});
</script>



<script>
$(document).ready(function() {
    function checkSubscriptionStatus() {
        $.ajax({
            url: "check_subscription.php", // API that returns subscription status
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.status === "paused") {
                    $("#subscriptionStatus").text(response.message).addClass("paused").removeClass("active");
                    Swal.fire({
                        icon: "warning",
                        title: "Subscription Paused",
                        text: response.message,
                        confirmButtonText: "OK"
                    });
                } else if (response.status === "active") {
                    $("#subscriptionStatus").text(response.message).addClass("active").removeClass("paused");
                    Swal.fire({
                        icon: "success",
                        title: "Subscription Active",
                        text: response.message,
                        confirmButtonText: "OK"
                    });
                } else {
                    $("#subscriptionStatus").text("Error fetching subscription").addClass("paused");
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: response.message,
                        confirmButtonText: "OK"
                    });
                }
            },
            error: function() {
                $("#subscriptionStatus").text("Failed to check status").addClass("paused");
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Could not connect to the server.",
                    confirmButtonText: "Retry"
                }).then(() => {
                    checkSubscriptionStatus();
                });
            }
        });
    }

    checkSubscriptionStatus(); // Check status on page load
});
</script>
</body>
</html>
