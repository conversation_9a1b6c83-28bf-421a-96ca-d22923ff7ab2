/* RaccoonO365 Suite Pro Dashboard Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme (Default) */
    --primary-color: #dc3545;
    --secondary-color: #212529;
    --accent-color: #495057;
    --text-light: #ffffff;
    --text-dark: #cccccc;
    --border-color: #343a40;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --sidebar-bg: #1a1a1a;
    --main-bg: #121212;
    --card-bg: #1e1e1e;
    --input-bg: #2a2a2a;
    --modal-overlay-bg: rgba(0, 0, 0, 0.5);
}

/* Light Theme Variables */
.light-theme {
    --primary-color: #dc3545;
    --secondary-color: #f8f9fa;
    --accent-color: #e9ecef;
    --text-light: #212529;
    --text-dark: #495057;
    --border-color: #dee2e6;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --sidebar-bg: #f8f9fa;
    --main-bg: #ffffff;
    --card-bg: #ffffff;
    --input-bg: #f1f3f5;
    --modal-overlay-bg: rgba(0, 0, 0, 0.3);
}

html, body {
    height: 100%;
    width: 100%;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--main-bg);
    color: var(--text-light);
    line-height: 1.5;
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* Page Layout */
.page-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
#sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    width: 250px;
    background-color: var(--sidebar-bg);
    color: var(--text-light);
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
    z-index: 100;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.raccoon-logo {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    display: flex;
    flex-direction: column;
}

.suite-pro {
    color: var(--primary-color);
    font-size: 20px;
}

.slogan {
    font-size: 12px;
    color: var(--text-dark);
    margin-top: 10px;
}

.slogan p {
    margin: 0;
    line-height: 1.2;
}

.sidebar-content {
    padding: 15px 0;
}

.theme-settings {
    padding: 0 15px 15px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.theme-toggle-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
    padding: 0 15px;
}

.theme-label {
    font-size: 14px;
    color: var(--text-light);
}

.theme-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.theme-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.sidebar-link {
    color: var(--text-light);
    display: block;
    padding: 8px 15px;
}

.sidebar-menu {
    padding: 0;
    margin: 0;
}

.menu-item {
    margin: 2px 0;
}

.menu-link {
    color: var(--text-light);
    padding: 10px 15px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--primary-color);
}

.menu-link.active {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.menu-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Submenu Styles */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    padding-left: 20px;
    background-color: rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease-out;
}

.has-submenu.active .submenu {
    display: block;
    max-height: 500px;
}

.submenu-item {
    margin: 0;
}

.submenu-link {
    padding: 8px 15px;
    display: flex;
    align-items: center;
    color: var(--text-light);
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.submenu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--primary-color);
}

.submenu-link.active {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.submenu-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 0.9em;
}

/* Main Content Styles */
.main-content {
    margin-left: 250px;
    padding: 20px;
    background-color: var(--main-bg);
    min-height: 100vh;
    width: calc(100% - 250px);
    transition: background-color 0.3s ease;
}

/* Page Content Styles */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.dashboard-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-light);
    margin: 0;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-block;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.primary-button {
    background-color: #0d6efd;
    color: white;
}

.primary-button:hover {
    background-color: #0b5ed7;
}

.secondary-button {
    background-color: #6c757d;
    color: white;
}

.secondary-button:hover {
    background-color: #5a6268;
}

.success-button {
    background-color: var(--success-color);
    color: white;
}

.success-button:hover {
    background-color: #218838;
}

.danger-button {
    background-color: var(--danger-color);
    color: white;
}

.danger-button:hover {
    background-color: #c82333;
}

.info-button {
    background-color: var(--info-color);
    color: white;
}

.info-button:hover {
    background-color: #138496;
}

/* Stats Cards */
.stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    background-color: var(--card-bg);
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.stat-header {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: 10px;
    text-align: center;
    font-weight: 500;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    padding: 20px 10px;
}

/* Subscription Container */
.subscription-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.subscription-card {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 30px;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #0a3740, #0d4b56, #165e6b, #1a6e7d);
}

.dollar-sign {
    font-size: 60px;
    font-weight: bold;
    color: var(--text-light);
    margin-right: 20px;
    opacity: 0.8;
}

.subscription-content {
    flex: 1;
}

.subscription-content h2 {
    font-size: 22px;
    margin-bottom: 10px;
    color: var(--text-light);
}

.subscription-content p {
    color: var(--text-dark);
    margin: 0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.show {
    display: block;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-overlay-bg);
}

.modal-container {
    position: relative;
    width: 90%;
    max-width: 500px;
    margin: 50px auto;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.modal-header {
    padding: 15px;
    background-color: var(--secondary-color);
    color: var(--text-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.close-button {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.modal-body {
    padding: 15px;
}

.modal-footer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 15px;
}

.input-wrapper {
    margin-bottom: 10px;
}

.input-wrapper label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea,
.custom-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-light);
    font-size: 14px;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
.custom-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.checkbox-group {
    margin: 10px 0;
}

.checkbox-item {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 8px;
}

.form-description {
    font-size: 0.9rem;
    color: var(--text-dark);
    margin-top: 0.5rem;
}

.spacer {
    height: 15px;
}

.error-label {
    color: var(--danger-color);
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.success-message {
    color: var(--success-color);
}

/* Profile Page Styles */
.profile-container {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.profile-picture-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.profile-picture {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    object-fit: cover;
    margin-bottom: 10px;
}

.wallet-card {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 15px;
    min-width: 200px;
}

.wallet-header {
    font-size: 16px;
    margin-bottom: 10px;
}

.wallet-balance {
    font-size: 24px;
    font-weight: bold;
}

.profile-details {
    margin-top: 20px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    min-width: 250px;
    padding: 0 10px;
}

/* Scanner Evasion Styles */
.scanner-evasion-container {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.scanner-evasion-container h2 {
    margin-bottom: 15px;
    font-size: 22px;
    color: var(--text-light);
}

.scanner-evasion-container h3 {
    margin: 20px 0 10px;
    font-size: 18px;
    color: var(--text-light);
}

.scanner-evasion-container p {
    margin-bottom: 15px;
    color: var(--text-dark);
}

.info-box {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-box p {
    margin: 0;
    color: var(--text-light);
}

.status-active {
    color: var(--success-color);
    font-weight: bold;
}

.status-paused {
    color: var(--warning-color);
    font-weight: bold;
}

.notification {
    color: var(--info-color);
    margin: 10px 0;
    font-weight: 500;
}

.table-container {
    margin: 15px 0;
    overflow-x: auto;
}

.scanner-evasion-container .results-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.scanner-evasion-container .results-table th,
.scanner-evasion-container .results-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.scanner-evasion-container .results-table th {
    background-color: var(--secondary-color);
    color: var(--text-light);
    font-weight: 500;
}

.scanner-evasion-container .results-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.toggle-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--secondary-color);
    border-radius: 5px;
}

.toggle-label {
    font-size: 16px;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Subscription Control Styles */
.subscription-control-container {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.subscription-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.subscription-status {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--secondary-color);
    border-radius: 5px;
}

.subscription-status h2 {
    margin-bottom: 10px;
    font-size: 18px;
}

.status-message {
    font-size: 16px;
}

.status-message.active {
    color: var(--success-color);
}

.status-message.paused {
    color: var(--danger-color);
}

.subscription-plans h2 {
    margin-bottom: 15px;
    font-size: 18px;
}

.plans-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

/* Settings Card */
.settings-card {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

.settings-card h2 {
    margin-bottom: 15px;
    font-size: 20px;
    color: var(--text-light);
}

.settings-description {
    margin-bottom: 20px;
    color: var(--text-dark);
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.input-with-button {
    display: flex;
    gap: 10px;
}

.input-with-button input {
    flex: 1;
}

.help-text {
    font-size: 12px;
    color: var(--text-dark);
    margin-top: 5px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Info Box */
.info-box {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.info-box p {
    margin: 0;
}

.info-box.warning {
    border-left: 4px solid var(--warning-color);
}

/* Status Box */
.status-box {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 15px;
    margin: 10px 0;
}

/* Domain Status */
.domain-status, .nameserver-info {
    margin-top: 20px;
}

.domain-status h3, .nameserver-info h3 {
    margin-bottom: 10px;
    font-size: 16px;
}

.nameserver-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.nameserver-info li {
    margin-bottom: 5px;
}

/* Results Table */
.results-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 10px 15px;
    flex: 1;
    min-width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 14px;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
}

.results-table-container {
    margin: 20px 0;
    overflow-x: auto;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th, .results-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.results-table th {
    background-color: var(--secondary-color);
    font-weight: 500;
}

.results-table .no-results td {
    text-align: center;
    padding: 20px;
    color: var(--text-dark);
}

/* Preview Containers */
.preview-container {
    margin-top: 20px;
}

.preview-container h3 {
    margin-bottom: 10px;
    font-size: 16px;
}

.welcome-page-preview, .title-preview {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
}

.preview-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.preview-logo {
    width: 100px;
    height: auto;
    margin-bottom: 10px;
}

.preview-title {
    font-size: 20px;
    margin: 0;
}

.preview-body {
    text-align: center;
}

.preview-message {
    margin-bottom: 20px;
}

.preview-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 300px;
    margin: 0 auto;
}

.preview-form input, .preview-form button {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.preview-form button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: not-allowed;
}

/* Browser Tab Preview */
.browser-tab {
    background-color: #f0f0f0;
    border-radius: 5px 5px 0 0;
    padding: 8px 15px;
    display: inline-block;
    margin-bottom: 10px;
}

.tab-title {
    color: #333;
    font-size: 14px;
}

/* Icon and Logo Previews */
.icon-preview, .logo-preview {
    margin-bottom: 20px;
}

.favicon-preview, .signin-logo-preview {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
}

.favicon-preview img {
    width: 32px;
    height: 32px;
}

.signin-logo-preview img {
    max-width: 200px;
    max-height: 60px;
}

/* Cookies Domain Settings and NameServer Config Styles */
.cookies-domain-settings-container,
.nameserver-config-container {
    margin-bottom: 30px;
}

.domain-info {
    margin-top: 20px;
}

.nameserver-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.nameserver-list li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.nameserver-value {
    font-weight: bold;
    color: var(--primary-color);
}

.domain-status-info {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
}

.status-message {
    font-weight: bold;
    color: var(--success-color);
    margin-bottom: 10px;
}

.status-detail {
    margin-bottom: 0;
}

.status-value {
    color: var(--text-light);
}

.nameserver-current {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.current-nameservers {
    margin-bottom: 20px;
}

.warning-message {
    color: var(--warning-color);
    font-weight: bold;
    margin-top: 10px;
}

.nameserver-actions {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 15px;
    border-radius: 5px;
}

.action-list {
    margin-top: 10px;
}

.delete-nameservers {
    list-style: none;
    padding: 0;
    margin: 10px 0;
    color: var(--danger-color);
}

.delete-nameservers li {
    padding: 5px 0;
}

.delete-nameservers li:before {
    content: "✕ ";
    color: var(--danger-color);
}

.add-nameservers {
    list-style: none;
    padding: 0;
    margin: 10px 0;
    color: var(--success-color);
}

.add-nameservers li {
    padding: 5px 0;
}

.add-nameservers li:before {
    content: "✓ ";
    color: var(--success-color);
}

#saveNameserverChanges {
    margin-top: 15px;
}

/* Postman Styles */
.postman-container,
.postman-subscription-container {
    margin-bottom: 30px;
}

.postman-header {
    margin-bottom: 20px;
    text-align: center;
}

.text-center {
    text-align: center;
}

.postman-notice {
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

.postman-form {
    margin-top: 20px;
}

.full-width {
    width: 100%;
}

.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-container input {
    margin-right: 5px;
}

.subscription-header {
    margin-bottom: 20px;
}

.subscription-warning {
    color: var(--danger-color);
    font-weight: bold;
    margin-top: 10px;
}

.postman-features {
    margin: 20px 0;
}

.feature-list {
    list-style-type: disc;
    padding-left: 20px;
    margin: 15px 0;
}

.feature-list li {
    margin-bottom: 10px;
}

.subscription-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    #sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .action-buttons {
        margin-top: 15px;
        width: 100%;
        flex-direction: column;
    }

    .button {
        width: 100%;
        margin-bottom: 5px;
    }

    .stats-row {
        flex-direction: column;
    }

    .modal-container {
        width: 95%;
        margin: 20px auto;
    }

    .profile-header {
        flex-direction: column;
    }

    .profile-picture-container {
        margin-bottom: 15px;
    }

    .wallet-card {
        width: 100%;
    }

    .form-row {
        flex-direction: column;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .subscription-actions {
        flex-direction: column;
    }

    .logs-table {
        font-size: 10px;
    }

    .logs-table th,
    .logs-table td {
        padding: 4px 3px;
    }

    .quick-access-buttons {
        flex-direction: column;
    }

    .results-stats {
        flex-direction: column;
    }
}

/* Logs Tables */
.logs-table-container {
    overflow-x: auto;
    margin: 20px 0;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-bg);
    color: var(--text-light);
    font-size: 12px;
}

.logs-table th {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: 8px 6px;
    text-align: left;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}

.logs-table td {
    padding: 8px 6px;
    border-bottom: 1px solid var(--border-color);
    word-break: break-word;
    max-width: 150px;
}

.logs-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.status-success {
    color: var(--success-color);
    font-weight: bold;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin: 15px 0;
}

.pagination-info {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.quick-access {
    margin-top: 20px;
}

.quick-access h3 {
    margin-bottom: 15px;
    color: var(--text-light);
}

.quick-access-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.no-results-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-dark);
    font-size: 16px;
}

.results-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-item {
    background-color: var(--secondary-color);
    padding: 15px;
    border-radius: 5px;
    flex: 1;
    min-width: 150px;
}

.stat-label {
    display: block;
    color: var(--text-dark);
    font-size: 14px;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    color: var(--text-light);
    font-size: 24px;
    font-weight: bold;
}

/* Traffic Analysis Styles */
.traffic-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.warning-button {
    background-color: #ffc107;
    color: #212529;
}

.warning-button:hover {
    background-color: #e0a800;
}

.map-placeholder {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 300px;
    position: relative;
    overflow: hidden;
}

.world-map {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: white;
    text-align: center;
}

.map-info p {
    margin: 5px 0;
    font-size: 16px;
    font-weight: 500;
}

.visitor-stats {
    text-align: center;
    padding: 20px;
}

.visitor-stats h2 {
    color: var(--text-light);
    margin-bottom: 15px;
}

.visitor-stats h3 {
    color: var(--text-dark);
    margin-bottom: 10px;
}

.no-visitors {
    color: var(--text-dark);
    font-style: italic;
}

.browser-stats, .country-stats {
    margin: 30px 0;
}

.browser-stats h2, .country-stats h2 {
    color: var(--text-light);
    margin-bottom: 15px;
    font-size: 18px;
}

.browser-table-container, .country-table-container {
    overflow-x: auto;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.browser-table, .country-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-bg);
    color: var(--text-light);
}

.browser-table th, .country-table th {
    background-color: #333;
    color: white;
    padding: 12px;
    text-align: left;
    font-weight: 500;
}

.browser-table td, .country-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
}

.browser-table tr:hover, .country-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.subscription-quote {
    font-style: italic;
    margin-top: 15px;
    color: var(--text-dark);
}

.subscription-author {
    text-align: right;
    margin-top: 10px;
    font-weight: 500;
    color: var(--text-dark);
}

/* Icon Grid Styles */
.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin: 30px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.icon-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.icon-item.selected {
    border-color: var(--primary-color);
    background-color: #e3f2fd;
}

.icon-item img {
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
}

.icon-item span {
    font-size: 12px;
    text-align: center;
    color: var(--text-dark);
    font-weight: 500;
}

.outlook-text-logo {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0078d4;
    color: white;
    font-size: 10px;
    font-weight: bold;
    border-radius: 4px;
    margin-bottom: 8px;
}

.preview-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.preview-section h3 {
    text-align: center;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.logo-preview-container {
    display: flex;
    justify-content: center;
}

.selected-logo-preview {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.selected-logo-preview img {
    width: 80px;
    height: 80px;
}

/* QRCode Config Styles */
.qrcode-config-container {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    color: white;
    border-radius: 8px;
    overflow: hidden;
}

.qrcode-header {
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    background-color: #ff5722;
    border-radius: 50%;
}

.status-text {
    font-weight: 500;
}

.qrcode-content {
    padding: 30px;
}

.qrcode-content h2 {
    margin-bottom: 15px;
    font-size: 24px;
}

.qrcode-content p {
    margin-bottom: 20px;
    line-height: 1.6;
}

.domain-connection-section {
    margin: 30px 0;
}

.domain-connection-section h3 {
    margin-bottom: 15px;
    font-size: 18px;
}

.domain-input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    margin-bottom: 15px;
}

.connect-button {
    background-color: #ff5722;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.connect-button:hover {
    background-color: #e64a19;
}

.nameserver-section {
    margin-top: 30px;
}

.nameserver-link {
    color: #ffeb3b;
    text-decoration: underline;
    font-weight: 500;
}

.nameserver-link:hover {
    color: #fff59d;
}

/* Link Generator Styles */
.cookies-link-container {
    max-width: 800px;
    margin: 0 auto;
}

.link-generator-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
}

.link-generator-card h2 {
    color: var(--text-light);
    margin-bottom: 30px;
    font-size: 24px;
}

.link-section {
    margin: 30px 0;
}

.generate-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 10px;
    transition: background-color 0.3s ease;
}

.generate-button:hover {
    background-color: #0056b3;
}

.link-instruction {
    color: var(--text-dark);
    margin: 10px 0;
    font-size: 14px;
}

.generated-link {
    margin-top: 15px;
}

.link-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #f8f9fa;
    color: var(--text-dark);
    font-family: monospace;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

.link-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
}

/* Landing Page Selector Styles */
.landing-page-selector {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.landing-page-selector h2 {
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 24px;
}

.landing-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
    background-color: white;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.landing-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Bot Defender Styles */
.bot-defender-container {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.bot-defender-card {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 40px;
    color: white;
    max-width: 500px;
    width: 100%;
    text-align: center;
}

.bot-defender-card h1 {
    font-size: 28px;
    margin-bottom: 30px;
    color: white;
}

.bot-settings-form {
    text-align: left;
}

.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 16px;
    color: white;
    position: relative;
    padding-left: 35px;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    left: 0;
    top: 0;
    height: 20px;
    width: 20px;
    background-color: transparent;
    border: 2px solid white;
    border-radius: 3px;
}

.checkbox-label:hover input ~ .checkmark {
    background-color: rgba(255, 255, 255, 0.1);
}

.checkbox-label input:checked ~ .checkmark {
    background-color: white;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-label input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-label .checkmark:after {
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid #00bcd4;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}

.save-settings-button {
    background-color: #00bcd4;
    color: white;
    border: none;
    padding: 12px 40px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    margin-top: 20px;
    width: 100%;
    transition: background-color 0.3s ease;
}

.save-settings-button:hover {
    background-color: #0097a7;
}

/* Password Page Settings Styles */
.pass-page-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 30px;
    max-width: 600px;
    margin: 0 auto;
}

.pass-page-card h2 {
    color: #007bff;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
}

.password-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    color: var(--text-dark);
    margin-bottom: 20px;
    max-height: 200px;
}

.password-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.custom-value-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    margin-right: 10px;
    transition: background-color 0.3s ease;
}

.custom-value-button:hover {
    background-color: #0056b3;
}

.update-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.update-button:hover {
    background-color: #0056b3;
}

/* Page Icon Settings Styles */
.page-icon-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
}

.page-icon-card h2 {
    color: #007bff;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
}

.page-icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
    margin: 30px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.page-icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 80px;
}

.page-icon-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.page-icon-item.selected {
    border-color: var(--primary-color);
    background-color: #e3f2fd;
}

.page-icon-item img {
    width: 48px;
    height: 48px;
}

.office-suite-icon {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    width: 48px;
    height: 48px;
}

.office-square {
    width: 22px;
    height: 22px;
    border-radius: 2px;
}

.office-square.red {
    background-color: #dc3545;
}

.office-square.green {
    background-color: #28a745;
}

.office-square.blue {
    background-color: #007bff;
}

.office-square.yellow {
    background-color: #ffc107;
}

.textmaker-icon {
    width: 48px;
    height: 48px;
    background-color: #dc3545;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.textmaker-text {
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.save-selection-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 40px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    margin-bottom: 20px;
    transition: background-color 0.3s ease;
}

.save-selection-button:hover {
    background-color: #0056b3;
}

.page-icon-preview-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.page-icon-preview-section h3 {
    text-align: center;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.page-icon-preview-container {
    display: flex;
    justify-content: center;
}

.selected-page-icon-preview {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.selected-page-icon-preview img {
    width: 80px;
    height: 80px;
}

/* Signin Page Settings Styles */
.signin-page-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 30px;
    max-width: 600px;
    margin: 0 auto;
}

.signin-page-card h2 {
    color: #007bff;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
}

.signin-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    color: var(--text-dark);
    margin-bottom: 20px;
    max-height: 200px;
}

.signin-select:focus {
    outline: none;
    border-color: var(--primary-color);
}
