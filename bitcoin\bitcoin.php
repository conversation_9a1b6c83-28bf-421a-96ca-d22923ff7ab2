<?php
session_start();

header('Content-Type: application/json');

$config = include 'sextractedconfig.php';
$userId = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

if (empty($userId)) {
    header("Location: ../logout"); // Redirect if user_id is not set
    exit();
}

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if 'user_id' column exists in the table
$check_column_sql = "SHOW COLUMNS FROM bitcoinwalletaddressmag LIKE 'user_id'";
$column_result = $conn->query($check_column_sql);

if ($column_result->num_rows === 0) {
    // Column does not exist, create it
    $add_column_sql = "ALTER TABLE bitcoinwalletaddressmag ADD COLUMN user_id INT NULL";
    if (!$conn->query($add_column_sql)) {
        die("Error adding user_id column: " . $conn->error);
    }
}

// Step 1: Select an unused wallet address
$sql_select_unused_wallet = "
    SELECT wallet_address 
    FROM bitcoinwalletaddressmag 
    WHERE wallet_address NOT IN (
        SELECT bitcoin_wallet FROM user_profiles
    ) AND user_id IS NULL 
    ORDER BY RAND() LIMIT 1
";

$result = $conn->query($sql_select_unused_wallet);

$response = [];

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $selected_wallet = $row['wallet_address'];

    // Step 2: Check if this wallet is already assigned
    $sql_check_user_assignment = "
        SELECT user_id FROM bitcoinwalletaddressmag 
        WHERE wallet_address = '$selected_wallet'
    ";
    
    $check_result = $conn->query($sql_check_user_assignment);
    
    if ($check_result->num_rows > 0) {
        // Wallet is already assigned, get the user_id
        $assignment = $check_result->fetch_assoc();
        $user_id_with_wallet = $assignment['user_id'];

        // Store user_id in bitcoinwalletaddressmag as this user has the wallet
        $sql_update_user_id = "
            UPDATE bitcoinwalletaddressmag 
            SET user_id = $user_id_with_wallet 
            WHERE wallet_address = '$selected_wallet'
        ";

        if ($conn->query($sql_update_user_id) === TRUE) {
            $response['status'] = 'success';
            $response['wallet_address'] = $selected_wallet;
        } else {
            $response['status'] = 'error';
            $response['message'] = "Error updating wallet address: " . $conn->error;
        }
    } else {
        // Wallet is not assigned, shuffle the remaining wallets and choose one
        $sql_get_unassigned_wallets = "
            SELECT wallet_address 
            FROM bitcoinwalletaddressmag 
            WHERE wallet_address NOT IN (
                SELECT bitcoin_wallet FROM user_profiles
            ) AND user_id IS NULL
            ORDER BY RAND()
        ";

        $unassigned_result = $conn->query($sql_get_unassigned_wallets);
        
        if ($unassigned_result->num_rows > 0) {
            $unassigned_wallets = $unassigned_result->fetch_all(MYSQLI_ASSOC);
            $random_index = array_rand($unassigned_wallets);
            $oneoftheunsigned_wallet = $unassigned_wallets[$random_index]['wallet_address'];
            $response['status'] = 'success';
            $response['wallet_address'] = $oneoftheunsigned_wallet;
        } else {
            $response['status'] = 'error';
            $response['message'] = "No unused wallet address found. Please add more unused wallet addresses to bitcoinwalletaddressmag to continue panel operation.";
        }
    }
} 

// Close the connection
$conn->close();

// Output JSON response
echo json_encode($response);
?>
