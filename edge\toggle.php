<?php

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
session_set_cookie_params([
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict',
]);

$config = include 'extractedconfig.php';

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

$user_id = $_SESSION['user_id'] ?? null;

$conn = new mysqli($host, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Ensure 'api' column exists as ENUM
$checkColumnSql = "SHOW COLUMNS FROM edgesettings LIKE 'api'";
$columnResult = $conn->query($checkColumnSql);
if ($columnResult->num_rows === 0) {
    $alterTableSql = "ALTER TABLE edgesettings ADD COLUMN api ENUM('true', 'false') NOT NULL DEFAULT 'false'";
    if ($conn->query($alterTableSql) !== TRUE) {
        die("Error adding 'api' column: " . $conn->error);
    }
}

$createTableSql = "
CREATE TABLE IF NOT EXISTS edgesettings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    edge_status ENUM('on', 'off') NOT NULL DEFAULT 'off',
    api ENUM('false', 'true') NOT NULL DEFAULT 'true',
    FOREIGN KEY (user_id) REFERENCES user_profiles(user_id) ON DELETE CASCADE
)";

if ($conn->query($createTableSql) !== TRUE) {
    die("Error creating table: " . $conn->error);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    $new_status = $_POST['status'];
    $api_status = ($new_status === 'on') ? 'false' : 'true';

    if ($user_id === null) {
        echo json_encode(['success' => false, 'error' => 'User not authenticated']);
        exit;
    }

    $checkRecordSql = "SELECT * FROM edgesettings WHERE user_id = $user_id";
    $result = $conn->query($checkRecordSql);

    if ($result->num_rows > 0) {
        $sql = "UPDATE edgesettings SET edge_status='$new_status', api='$api_status' WHERE user_id = $user_id";
    } else {
        $sql = "INSERT INTO edgesettings (user_id, edge_status, api) VALUES ($user_id, '$new_status', '$api_status')";
    }

    if ($conn->query($sql) === TRUE) {
        echo json_encode(['success' => true, 'status' => $new_status, 'api' => $api_status]);
    } else {
        echo json_encode(['success' => false, 'error' => $conn->error]);
    }
    exit;
}

$checkSql = "SELECT edge_status, api FROM edgesettings WHERE user_id = $user_id";
$result = $conn->query($checkSql);

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
         echo "Current status: " . $row['edge_status'];
} else {
    echo "No record found.";
}

$conn->close();
?>



