<?php
header('Content-Type: application/json');
$apiKey = '52063ecc3bb7a274';
$ipApiUrl = "https://api.ipapi.is/?key=$apiKey";
$ipAddress = isset($_GET['ip']) ? $_GET['ip'] : $_SERVER['REMOTE_ADDR'];
$response = file_get_contents("$ipApiUrl&ip=$ipAddress");

if ($response === false) {
    echo json_encode(['error' => 'Unable to fetch IP data.']);
    http_response_code(500);
    exit();
}

$responseArray = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode(['error' => 'Invalid JSON response from API.']);
    http_response_code(500);
    exit();
}

function replaceDomain(&$array, $oldDomain, $newDomain) {
    foreach ($array as &$value) {
        if (is_array($value)) {
            replaceDomain($value, $oldDomain, $newDomain);
        } elseif (is_string($value) && strpos($value, $oldDomain) !== false) {
            $value = str_replace($oldDomain, $newDomain, $value);
        }
    }
}

$proxyDomain = 'raccoonO365api.com';
replaceDomain($responseArray, 'api.ipapi.is', $proxyDomain);
$modifiedResponse = json_encode($responseArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

if ($modifiedResponse !== false) {
    echo $modifiedResponse;
} else {
    echo json_encode(['error' => 'Failed to encode JSON response.']);
    http_response_code(500);
}
?>