<?php
require('../assets/admin_authorize.php');
require('../php_files/db.php');
require('../php_files/functions.php');
error_reporting(0);
$user_id = $_SESSION['user_id'];

function fetchMessagesWithUsers() {
    global $pdo;

    $sql = "
    SELECT 
        c.id, 
        c.user_id, 
        c.last_message,
        up.username,
        up.profile_picture,
        MAX(c.created_at) AS created_at
    FROM 
        chats c
    JOIN 
        user_profiles up 
    ON 
        c.user_id = up.user_id
    WHERE up.username != ?  -- Filter by username instead of user_id
    GROUP BY up.username  -- Group by username to prevent duplicate chats
    ORDER BY created_at DESC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$_SESSION['username']]);  // Use username instead of user_id

    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $messages;
}

?>

<?php require '../assets/admin_header.php'; ?>
    <link href="<?= BASE_URL ?>/css/chat.css" rel="stylesheet">
      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!--	<script src="--><?php //= BASE_URL ?><!--/js/chatjs.js"></script>-->



    <main class="col-md-9 ms-sm-auto bg-dark-subtle col-lg-10 px-md-4 position-relative">
        <div id="chat-sidebar" class="bg-dark">

            <?php
            $messages = fetchMessagesWithUsers();
            foreach ($messages as $msg) {
                echo '<div id="sidebar-user-box" class="' . $msg['id'] . '">
              <img src="' . (!empty($msg['profile_picture']) ? $msg['profile_picture'] : BASE_URL . '/uploads/blankprofile.webp') . '" alt="Profile Picture">
              <span id="slider-username"> ' . $msg['username'] . ' </span>
          </div>';
            }
            ?>
        </div>
    </main>
    


<style>
  /* Style for the chat box */
.msg_box {
    position: fixed;
    bottom: 10px;
    right: 10px;
    width: 350px; /* Increased width */
    height: 518px; /* Reduced height */
    background-color: #fff;
    border-radius: 15px; /* More rounded corners for Facebook-style */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    font-family: Arial, sans-serif;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    visibility: visible; /* Ensure it's always visible unless closed */
}

/* Header styles */
.msg_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #4e7ccf; /* Facebook-style blue */
    color: white;
    padding: 10px;
    border-radius: 15px 15px 0 0;
    position: relative;
}

.msg_head .username {
    font-weight: bold;
    font-size: 16px;
    margin-right: 10px;
}

.msg_head .close,
.msg_head .minimize {
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
    color: white;
    transition: color 0.3s;
}

.msg_head .close {
    color: #f00;
}

.msg_head .minimize {
    color: #f39c12;
}

.msg_head i.bi-three-dots-vertical {
    font-size: 18px;
    cursor: pointer;
}

/* Dropdown menu */
#options-dropdown {
    position: absolute;
    top: 35px;
    right: 10px;
    background-color: #ddd; /* Grey background */
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 150px;
    display: none;
}

#options-dropdown .dropdown-item {
    padding: 8px;
    background-color: #ddd; /* Grey background for each item */
    border: none;
    text-align: left;
    width: 100%;
    cursor: pointer;
    font-size: 14px;
}

#options-dropdown .dropdown-item:hover {
    background-color: #ccc; /* Darker grey on hover */
}

/* Message wrap and body */
.msg_wrap {
    display: flex;
    flex-direction: column;
    padding: 10px; /* Reduced padding */
    background-color: #f1f1f1;
    flex-grow: 1; /* Grow to take up remaining space */
    overflow-y: auto;
    border-radius: 0 0 15px 15px;
}

.msg_body {
    flex: 1;
    overflow-y: auto;
    max-height: 380px; /* Reduced max height to fit better */
    height: 366px;
}

.msg_push {
    margin-bottom: 10px;
}

/* Message input */
.msg_footer {
    display: flex;
    align-items: center;
    padding-top: 5px; /* Reduced top padding */
    padding-bottom: 5px; /* Reduced bottom padding */
    padding-left: 10px; /* Reduced padding */
    padding-right: 10px; /* Reduced padding */
    border-top: 1px solid #e2e2e2;
    background-color: #f7f7f7;
}

.msg_input {
    width: 100%; /* Full width to use available space */
    padding: 8px 12px; /* Reduced padding */
    font-size: 14px;
    border-radius: 25px; /* Slightly larger radius for Facebook-like rounded corners */
    border: 1px solid #ddd;
    resize: none;
    outline: none;
    background-color: #f7f7f7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    transition: all 0.3s ease;
}

/* Focus state */
.msg_input:focus {
    border-color: #4e7ccf; /* Facebook-like focus color */
    box-shadow: 0 1px 5px rgba(72, 114, 207, 0.5); /* Slight shadow on focus */
}

/* Button styles */
.close, .minimize {
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    background: none;
    border: none;
    color: white;
    transition: color 0.3s;
}

.close:hover {
    color: #f00;
}

.minimize:hover {
    color: #f39c12;
}

    
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



<script>
    $(document).ready(function() {

        var arr = []; // List of users
        var intervals = {}; // To store intervals for each chat

        $(document).on('click', '.msg_head', function() {
            var chatbox = $(this).parents().attr("rel");
            $('[rel="'+chatbox+'"] .msg_wrap').slideToggle('slow');
            return false;
        });

        $(document).on('click', '.close', function() {
            var chatbox = $(this).parents().parents().attr("rel");
            $('[rel="'+chatbox+'"]').hide();
            arr.splice($.inArray(chatbox, arr), 1);
            clearInterval(intervals[chatbox]);
            delete intervals[chatbox];
            displayChatBox();
            return false;
        });

        $(document).on('click', '#sidebar-user-box', function() {
            var chatID = $(this).attr("class");
            var username = $(this).children().text();

            if ($.inArray(chatID, arr) != -1) {
                arr.splice($.inArray(chatID, arr), 1);
            }

            arr.unshift(chatID);

            chatPopup =  '<div class="msg_box" style="right:0px" rel="'+ chatID+'">'+
                '<div class="msg_head">'+
                    '<span class="username">'+username+'</span>'+
                    '<div class="close">×</div>' +
                    '<div class="minimize">_</div>' +
                    '<i class="bi bi-three-dots-vertical" id="options-toggle"></i>' +
                    '<div class="dropdown" id="options-dropdown" style="display:none;">'+
                        '<button class="dropdown-item" id="lock-account">Lock Account</button>'+
                        '<button class="dropdown-item" id="lock-transfer">Lock Transfer</button>'+
                        '<button class="dropdown-item" id="unlock-transfer">Unlock Transfer</button>'+
                    '</div>'+
                '</div>' +
                '<div class="msg_wrap" style="display:block !important;">'+
                    '<div class="msg_body"><div class="msg_push"></div></div>' +
                    '<div class="msg_footer"><textarea class="msg_input" rows="4" placeholder="Type a message..."></textarea></div>' +
                '</div>' +
            '</div>';

            $("body").append(chatPopup);
            displayChatBox();
            fetchMessages(chatID);
            startGettingMessage(chatID);

            // Toggle options menu
            $(document).on('click', '#options-toggle', function() {
                $(this).next('#options-dropdown').toggle();
            });

            // Options actions
            $(document).on('click', '#lock-account', function() {
                Swal.fire({
                    title: 'Are you sure?',
                    text: 'You are about to lock the account.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, lock it!',
                    cancelButtonText: 'No, keep it open'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Your logic to lock account
                        Swal.fire('Locked!', 'The account has been locked.', 'success');
                    }
                });
            });

            $(document).on('click', '#lock-transfer', function() {
                Swal.fire({
                    title: 'Lock Transfer?',
                    text: 'This will lock all transfer actions.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, lock transfer',
                    cancelButtonText: 'No, keep it open'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Your logic to lock transfer
                        Swal.fire('Locked!', 'Transfers have been locked.', 'success');
                    }
                });
            });

            $(document).on('click', '#unlock-transfer', function() {
                Swal.fire({
                    title: 'Unlock Transfer?',
                    text: 'This will allow transfer actions again.',
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, unlock transfer',
                    cancelButtonText: 'No, keep it locked'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Your logic to unlock transfer
                        Swal.fire('Unlocked!', 'Transfers have been unlocked.', 'success');
                    }
                });
            });
        });

        $(document).on('keypress', 'textarea', function(e) {
            if (e.keyCode == 13) {
                var msg = $(this).val();
                $(this).val('');

                if (msg.trim().length != 0) {
                    var chatID = $(this).closest('.msg_box').attr('rel');
                    
                    $.ajax({
                        url: '<?= BASE_URL ?>/php_files/agent_message.php',
                        type: 'POST',
                        data: {
                            chat_id: "<?php echo $msg['username']; ?>",
                            message: msg
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.status === 'success') {
                                fetchMessages(chatID);
                            } else {
                                Swal.fire('Error', response.message, 'error');
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire('Error', 'An error occurred: ' + error, 'error');
                        }
                    });
                }
            }
        });

        function displayChatBox() {
            let i = 650;
            const j = 260;

            $.each(arr, function(index, value) {
                if (index < 4) {
                    $('[rel="'+value+'"]').css("right", i + "px");
                    $('[rel="'+value+'"]').show();
                    i -= j;
                } else {
                    $('[rel="'+value+'"]').hide();
                }
            });
        }

        function fetchMessages(id) {
            $.ajax({
                url: '<?= BASE_URL ?>/php_files/agentfetch_messages.php?action=get_messages',
                method: 'GET',
                data: { chat_id: "<?php echo $msg['username']; ?>" },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        var chatbox = $('[rel="' + id + '"] .msg_push');

                        $('[rel="' + id + '"] .msg-right, [rel="' + id + '"] .msg-left').remove();

                        $.each(response.messages, function(index, message) {
                            if (message.support_agentmessage) {
                                $('<div class="msg-right">' + message.support_agentmessage + '</div>').insertBefore(chatbox);
                            } else if (message.last_message) {
                                $('<div class="msg-left">' + message.last_message + '</div>').insertBefore(chatbox);
                            }
                        });

                        $('.msg_body').scrollTop($('.msg_body')[0].scrollHeight);
                    } else {
                        console.error(response.message);
                    }
                },
                error: function(error) {
                    console.error('Error fetching messages:', error);
                }
            });
        }

        function startGettingMessage(id) {
            if (intervals[id]) {
                clearInterval(intervals[id]);
            }
            intervals[id] = setInterval(function() {
                fetchMessages(id);
            }, 5000);
        }

    });
</script>
