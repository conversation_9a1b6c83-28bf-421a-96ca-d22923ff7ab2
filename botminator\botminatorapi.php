<?php


session_start();
// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a connection to the MySQL database
$conn = new mysqli($host, $username, $password, $dbname);

// Check if the connection was successful
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set the character set to UTF-8 for proper handling of special characters
$conn->set_charset("utf8");

// Optionally, you can define a function to close the connection when done
function closeDbConnection() {
    global $conn;
    $conn->close();
}


$user_id = $_SESSION['user_id']; // Get signed-in user ID from session
$user_wallet_balance = getUserWalletBalance($user_id); // Get the user's wallet balance

// Ensure necessary tables exist
createTableIfNotExists();

// Show renewal or subscription button
showRenewalButton($user_id);

// Display available plans based on user's current plan
displayAvailablePlans($user_id);



error_log("Debug info: " . var_export($user_id, true));






// Define actions for AJAX calls
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'subscribe') {
        $plan_name = $_POST['plan_name'];
        $price = $_POST['price'];

        storeSubscription($user_id, $plan_name);
        // echo "Successfully subscribed to $plan_name.";
        
     
        
        exit;
    }

    if ($action === 'renew') {
        renewSubscription($user_id);
        exit;
    }
}



// Functions

/**
 * Check and display renewal/subscription buttons based on subscription status.
 */
function showRenewalButton($user_id) {
    global $conn;

    $query = "SELECT * 
              FROM subscribeSBBsaS 
              WHERE user_id = $user_id 
              ORDER BY subscription_end_date DESC LIMIT 1";
    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        // Check if the subscription is expired
        if ($row['subscription_end_date'] < date('Y-m-d')) {
        
        
        
        } else {
            echo "Your subscription is active until " . $row['subscription_end_date'] . ".";
        }
    } else {
        // No subscription found
        //echo "No subscription found. Please subscribe to access this feature.";
       
    }
}

/**
 * Create necessary tables if they do not exist.
 */
function createTableIfNotExists() {
    global $conn;

    // Create `subscribeSBBsaS` table
    $query = "CREATE TABLE IF NOT EXISTS subscribeSBBsaS (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        subscription_start_date DATE NOT NULL,
        subscription_end_date DATE NOT NULL,
        subscribeopenredirectplan VARCHAR(255) NOT NULL
    )";
    $conn->query($query);

    // Create `subscribeSBBsaSplans` table for subscription plans
    $query = "CREATE TABLE IF NOT EXISTS subscribeSBBsaSplans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_name VARCHAR(255) NOT NULL,
        duration INT NOT NULL,
        price DECIMAL(10, 2) NOT NULL
    )";
    $conn->query($query);

    // Only add unique constraint if it doesn't already exist
    $query = "SHOW INDEX FROM subscribeSBBsaSplans WHERE Key_name = 'plan_name'";
    $result = $conn->query($query);
    if ($result->num_rows == 0) {
        $query = "ALTER TABLE subscribeSBBsaSplans ADD UNIQUE (plan_name)";
        $conn->query($query);
    }

    // Seed `subscribeSBBsaSplans` table with plans if empty
    $query = "INSERT INTO subscribeSBBsaSplans (plan_name, duration, price)
              VALUES
                  ('Professional Plan', 25, 180.00),
                  ('Advanced Plan', 35, 200.00),
                  ('Elite Plan', 50, 250.00)
              ON DUPLICATE KEY UPDATE
                  plan_name=VALUES(plan_name), duration=VALUES(duration), price=VALUES(price)";
    $conn->query($query);
}



/**
 * Get the user's wallet balance.
 */
function getUserWalletBalance($user_id) {
    global $conn;
    $query = "SELECT balance FROM wallet WHERE user_id = $user_id";
    $result = $conn->query($query);
    $row = $result->fetch_assoc();
    return $row['balance'];
}

/**
 * Charge the user's wallet balance and record the transaction.
 */
function chargeUserBalance($user_id, $amount) {
    global $conn;
    $balance = getUserWalletBalance($user_id);
    if ($balance >= $amount) {
        $newBalance = $balance - $amount;
        $query = "UPDATE wallet SET balance = $newBalance WHERE user_id = $user_id";
        $conn->query($query);
        storeTransaction($user_id, $amount);
        return true;
    }
    return false;
}

/**
 * Store a wallet transaction record.
 */
function storeTransaction($user_id, $amount) {
    global $conn;
    $transactionId = uniqid();
    $query = "INSERT INTO wallet_transactions (user_id, crypto_type, amount, transaction_id, type, status) 
              VALUES ($user_id, 'subscription', $amount, '$transactionId', 'debit', 'completed')";
    $conn->query($query);
}

/**
 * Store a new subscription in the `subscribeSBBsaS` table.
 */
function storeSubscription($user_id, $plan_name) {
    global $conn;

    // Fetch the plan details
    $plan = getPlanDetails($plan_name);

    if ($plan) {
        // Check if the user already has an active subscription
        $query = "SELECT * FROM subscribeSBBsaS 
                  WHERE user_id = $user_id 
                  AND subscribeopenredirectplan = '{$plan['plan_name']}' 
                  AND subscription_end_date > CURDATE()";
        $result = $conn->query($query);

        // If a subscription already exists and is not expired, do not allow a new subscription
        if ($result->num_rows > 0) {
            echo "You are already subscribed to the {$plan['plan_name']} plan. Your subscription is still active.";
        } else {
            // If no active subscription exists, create a new subscription
            $startDate = date('Y-m-d');
            $endDate = calculateBusinessEndDate($startDate, $plan['duration']);

            $query = "INSERT INTO subscribeSBBsaS (user_id, price, subscription_start_date, subscription_end_date, subscribeopenredirectplan)
                      VALUES ($user_id, {$plan['price']}, '$startDate', '$endDate', '{$plan['plan_name']}')";
            if ($conn->query($query) === TRUE) {
                echo "Subscription successfully created!";
            } else {
                echo "Error: " . $conn->error;
            }
        }
    } else {
        echo "Failed to create subscription. Plan not found.";
    }
}


/**
 * Calculate the subscription end date based on business days.
 */
function calculateBusinessEndDate($startDate, $businessDays) {
    $currentDate = strtotime($startDate);
    $daysAdded = 0;

    while ($daysAdded < $businessDays) {
        $currentDate = strtotime("+1 day", $currentDate);

        // Skip Saturdays and Sundays
        $dayOfWeek = date('N', $currentDate); // 1 (Monday) to 7 (Sunday)
        if ($dayOfWeek < 6) { // Monday to Friday
            $daysAdded++;
        }
    }

    return date('Y-m-d', $currentDate);
}

/**
 * Fetch the details of a plan from `subscribeSBBsaSplans`.
 */
function getPlanDetails($plan_name) {
    global $conn;
    $query = "SELECT * FROM subscribeSBBsaSplans WHERE plan_name = '$plan_name'";
    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    return null;
}


function displayAvailablePlans($user_id) {
    global $conn;

    // Fetch the user's current subscription plan from `user_subscriptions`
    // Join with subscription_plans to get the correct plan name
    $query = "SELECT us.plan_id, sp.plan_name, sp.price, sp.duration_days
              FROM user_subscriptions us
              JOIN subscription_plans sp ON us.plan_id = sp.id
              WHERE us.user_id = $user_id
              ORDER BY us.subscription_end_date DESC LIMIT 1";

    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        // Fetch the plan details
        $row = $result->fetch_assoc();
        $currentPlan = $row['plan_name']; // Correct plan name from subscription_plans
        $price = $row['price'];
        $duration = $row['duration_days'];

        
        
         // Return the active subscription details as an array
        return [
            'plan_name' => $currentPlan,
            'price' => $price,
            'duration_days' => $duration
        ];
        
    } else {
        // No subscription found
        echo "No subscription found. Please subscribe to access plans.";
        displayAllPlans($user_id); // Show all available plans for the user to choose from
    }
}





// Query to retrieve all plans from the subscribeSBBsaSplans table
$query = "SELECT plan_name, duration, price FROM subscribeSBBsaSplans";
$result = $conn->query($query);

$currentPlanDetails = displayAvailablePlans($user_id);

// If no active subscription, return and exit
if (!$currentPlanDetails) {
    echo "No active subscription found.";
    return; // Exit if no active subscription is found
}

// Extract the current subscription details from the returned array
$currentPlanName = $currentPlanDetails['plan_name'];
$currentPlanDuration = $currentPlanDetails['duration_days'];


// Query to check if the user has an active subscription
$activeSubscriptionQuery = "SELECT subscribeopenredirectplan, subscription_end_date 
                            FROM subscribeSBBsaS 
                            WHERE user_id = $user_id AND subscription_end_date >= CURDATE() 
                            LIMIT 1";
$activeSubscriptionResult = $conn->query($activeSubscriptionQuery);

$activeSubscription = null;
if ($activeSubscriptionResult->num_rows > 0) {
    $activeSubscription = $activeSubscriptionResult->fetch_assoc();
}

// If there is no active subscription, show the specific plan format
if (!$activeSubscription) {
    


// Check if any rows are returned
if ($result->num_rows > 0) {
    // Loop through the results and store values in variables
    while ($row = $result->fetch_assoc()) {
        $planName = $row['plan_name'];
        $planDuration = $row['duration'];
        $planPrice = $row['price'];


        // Check if the plan name and duration match with the active subscription
        if (stripos($planName, $planName) !== false && $currentPlanDuration == $planDuration) {
            
        // Echo the plan details without 'id'
    echo "<script>";
    echo "var plan = {";
    echo "name: '" . $planName . "',";
    echo "duration: " . $planDuration . ",";
    echo "price: " . $planPrice;
    echo "};";
    echo "</script>";

            
            
            echo "<div>";
            echo "<strong>" . $planName . "</strong><br>";
            echo "Price: $" . $planPrice . "<br>";
            echo "Duration: " . $planDuration . " Business Days<br>";
                      echo "<button  class=btn onclick='subscribe(plan)'>Subscribe to Botminator</button>";
            echo "</div><br>";
        }
    }
} else {
    echo "No plans found.";
}

} else {
    // If an active subscription exists, display a message
    echo "You are currently subscribed to Botminator (Bot Blocker tool) <strong>" . $activeSubscription['subscribeopenredirectplan'] . "</strong> until " . $activeSubscription['subscription_end_date'] . ".";
    
    
    
}



?>



    
    
    
