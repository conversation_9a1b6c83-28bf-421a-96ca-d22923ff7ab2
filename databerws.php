<?php
// Database configuration
$host = 'localhost'; // Database host
$dbname = 'your_database_name'; // Your database name
$username = 'your_database_username'; // Your database username
$password = 'your_database_password'; // Your database password

// Create a connection to the MySQL database
$conn = new mysqli($host, $username, $password, $dbname);

// Check if the connection was successful
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set the character set to UTF-8 for proper handling of special characters
$conn->set_charset("utf8");

// Optionally, you can define a function to close the connection when done
function closeDbConnection() {
    global $conn;
    $conn->close();
}
?>
