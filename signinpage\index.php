<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update PSP Text</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

        .container {
            max-width: 600px;
            width: 100%;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            margin-bottom: 20px;
            color: #007bff;
            font-weight: 700;
        }

        label {
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
            color: #555;
            text-align: left;
        }

        select {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            background: #f9f9f9;
        }

        select:focus {
            border-color: #007bff;
            outline: none;
            background: #fff;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease-in-out;
        }

        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body> 
    <div class="container">
        <h1>Change Sign in Page Text</h1>
        <form id="updateForm">
            <label for="textSelection">Select a text:</label>
         <select id="textSelection" name="text">
<option value="One more step – To continue, you must sign in to proceed!">One more step – To continue, you must sign in to proceed!</option>
<option value="To continue, you must sign in to proceed!">To continue, you must sign in to proceed!</option>
<option value="One more step – You need to sign in to view this document!">One more step – You need to sign in to view this document!</option>
<option value="You need to sign in to view this document!">You need to sign in to view this document!</option>
<option value="One more step – To access this document, please sign in!">One more step – To access this document, please sign in!</option>
<option value="To access this document, please sign in!">To access this document, please sign in!</option>
<option value="One more step – Sign in to gain access to the document!">One more step – Sign in to gain access to the document!</option>
<option value="Sign in to gain access to the document!">Sign in to gain access to the document!</option>
<option value="One more step – Please sign in to proceed with document access!">One more step – Please sign in to proceed with document access!</option>
<option value="Please sign in to proceed with document access!">Please sign in to proceed with document access!</option>
<option value="One more step – To view the document, you must sign in!">One more step – To view the document, you must sign in!</option>
<option value="To view the document, you must sign in!">To view the document, you must sign in!</option>
<option value="One more step – Sign in to access the protected document!">One more step – Sign in to access the protected document!</option>
<option value="Sign in to access the protected document!">Sign in to access the protected document!</option>
<option value="One more step – Log in to unlock the document content!">One more step – Log in to unlock the document content!</option>
<option value="Log in to unlock the document content!">Log in to unlock the document content!</option>
<option value="One more step – Sign in to gain access to the requested document!">One more step – Sign in to gain access to the requested document!</option>
<option value="Sign in to gain access to the requested document!">Sign in to gain access to the requested document!</option>
<option value="One more step – You need to sign in to view this content.">One more step – You need to sign in to view this content.</option>
<option value="You need to sign in to view this content.">You need to sign in to view this content.</option>
<option value="One more step – Log in to view and download the document.">One more step – Log in to view and download the document.</option>
<option value="Log in to view and download the document.">Log in to view and download the document.</option>
<option value="One more step – Sign in to proceed with document access.">One more step – Sign in to proceed with document access.</option>
<option value="Sign in to proceed with document access.">Sign in to proceed with document access.</option>
<option value="One more step – To access the document, please sign in below.">One more step – To access the document, please sign in below.</option>
<option value="To access the document, please sign in below.">To access the document, please sign in below.</option>
<option value="One more step – Sign in to open the document">One more step – Sign in to open the document</option>
<option value="Sign in to open the document">Sign in to open the document</option>
<option value="One more step – You must sign in to access the document content.">One more step – You must sign in to access the document content.</option>
<option value="You must sign in to access the document content.">You must sign in to access the document content.</option>
<option value="One more step – Please sign in to view and edit the document.">One more step – Please sign in to view and edit the document.</option>
<option value="Please sign in to view and edit the document.">Please sign in to view and edit the document.</option>
<option value="One more step – To e-sign the document, please sign in.">One more step – To e-sign the document, please sign in.</option>
<option value="To e-sign the document, please sign in.">To e-sign the document, please sign in.</option>
<option value="One more step – Please sign in to access the document and apply your e-signature.">One more step – Please sign in to access the document and apply your e-signature.</option>
<option value="Please sign in to access the document and apply your e-signature.">Please sign in to access the document and apply your e-signature.</option>
<option value="One more step – Sign in to securely e-sign this protected document.">One more step – Sign in to securely e-sign this protected document.</option>
<option value="Sign in to securely e-sign this protected document.">Sign in to securely e-sign this protected document.</option>
<option value="One more step – Sign in to proceed with signing the document.">One more step – Sign in to proceed with signing the document.</option>
<option value="Sign in to proceed with signing the document.">Sign in to proceed with signing the document.</option>
<option value="One more step – Please sign in to access the document and sign it.">One more step – Please sign in to access the document and sign it.</option>
<option value="Please sign in to access the document and sign it.">Please sign in to access the document and sign it.</option>
<option value="One more step – Sign in to apply your electronic signature to the document.">One more step – Sign in to apply your electronic signature to the document.</option>
<option value="Sign in to apply your electronic signature to the document.">Sign in to apply your electronic signature to the document.</option>
<option value="One more step – You must sign in to e-sign this protected document.">One more step – You must sign in to e-sign this protected document.</option>
<option value="You must sign in to e-sign this protected document.">You must sign in to e-sign this protected document.</option>
<option value="One more step – Please sign in to review and e-sign the document.">One more step – Please sign in to review and e-sign the document.</option>
<option value="Please sign in to review and e-sign the document.">Please sign in to review and e-sign the document.</option>
<option value="One more step – To sign the document, please sign in using your credentials.">One more step – To sign the document, please sign in using your credentials.</option>
<option value="To sign the document, please sign in using your credentials.">To sign the document, please sign in using your credentials.</option>
<option value="One more step – Sign in to complete the document with your e-signature.">One more step – Sign in to complete the document with your e-signature.</option>
<option value="Sign in to complete the document with your e-signature.">Sign in to complete the document with your e-signature.</option>
<option value="One more step – Please sign in to access and e-sign the protected document.">One more step – Please sign in to access and e-sign the protected document.</option>
<option value="Please sign in to access and e-sign the protected document.">Please sign in to access and e-sign the protected document.</option>
<option value="One more step – Sign in to add your digital signature.">One more step – Sign in to add your digital signature.</option>
<option value="Sign in to add your digital signature.">Sign in to add your digital signature.</option>
<option value="One more step – You need to sign in to e-sign this document.">One more step – You need to sign in to e-sign this document.</option>
<option value="You need to sign in to e-sign this document.">You need to sign in to e-sign this document.</option>
<option value="One more step – To e-sign the document, please sign in with your account.">One more step – To e-sign the document, please sign in with your account.</option>
<option value="To e-sign the document, please sign in with your account.">To e-sign the document, please sign in with your account.</option>
<option value="One more step – Sign in to download this protected document.">One more step – Sign in to download this protected document.</option>
<option value="Sign in to download this protected document.">Sign in to download this protected document.</option>
<option value="One more step – To download the document, please sign in.">One more step – To download the document, please sign in.</option>
<option value="To download the document, please sign in.">To download the document, please sign in.</option>
<option value="One more step – Please sign in to access and download the document.">One more step – Please sign in to access and download the document.</option>
<option value="Please sign in to access and download the document.">Please sign in to access and download the document.</option>
<option value="One more step – Sign in to download the secure document.">One more step – Sign in to download the secure document.</option>
<option value="Sign in to download the secure document.">Sign in to download the secure document.</option>
<option value="One more step – To proceed with downloading, sign in with your account.">One more step – To proceed with downloading, sign in with your account.</option>
<option value="To proceed with downloading, sign in with your account.">To proceed with downloading, sign in with your account.</option>
<option value="One more step – Sign in to unlock and download the document.">One more step – Sign in to unlock and download the document.</option>
<option value="Sign in to unlock and download the document.">Sign in to unlock and download the document.</option>
<option value="One more step – Please sign in to download this protected file.">One more step – Please sign in to download this protected file.</option>
<option value="Please sign in to download this protected file.">Please sign in to download this protected file.</option>
<option value="One more step – Log in to access and download the protected document.">One more step – Log in to access and download the protected document.</option>
<option value="Log in to access and download the protected document.">Log in to access and download the protected document.</option>
<option value="One more step – Sign in to complete the download of the document.">One more step – Sign in to complete the download of the document.</option>
<option value="Sign in to complete the download of the document.">Sign in to complete the download of the document.</option>
<option value="One more step – You need to sign in to download the document.">One more step – You need to sign in to download the document.</option>
<option value="You need to sign in to download the document.">You need to sign in to download the document.</option>
<option value="One more step – Sign in to view and download the document.">One more step – Sign in to view and download the document.</option>
<option value="Sign in to view and download the document.">Sign in to view and download the document.</option>
<option value="One more step – Please sign in to access and download the file.">One more step – Please sign in to access and download the file.</option>
<option value="Please sign in to access and download the file.">Please sign in to access and download the file.</option>
<option value="One more step – To download this document, please sign in.">One more step – To download this document, please sign in.</option>
<option value="To download this document, please sign in.">To download this document, please sign in.</option>
<option value="One more step – Log in to download the document.">One more step – Log in to download the document.</option>
<option value="Log in to download the document.">Log in to download the document.</option>
<option value="One more step – Sign in to access and download the protected file.">One more step – Sign in to access and download the protected file.</option>
<option value="Sign in to access and download the protected file.">Sign in to access and download the protected file.</option>
<option value="One more step – Please sign in to unlock and retrieve the document.">One more step – Please sign in to unlock and retrieve the document.</option>
<option value="Please sign in to unlock and retrieve the document.">Please sign in to unlock and retrieve the document.</option>
<option value="One more step – Sign in to proceed with the document download.">One more step – Sign in to proceed with the document download.</option>
<option value="Sign in to proceed with the document download.">Sign in to proceed with the document download.</option>
<option value="One more step – You must sign in to download this document.">One more step – You must sign in to download this document.</option>
<option value="You must sign in to download this document.">You must sign in to download this document.</option>
<option value="One more step – Sign in to access and download the file.">One more step – Sign in to access and download the file.</option>
<option value="Sign in to access and download the file.">Sign in to access and download the file.</option>
<option value="One more step – Sign in to view the blurred document.">One more step – Sign in to view the blurred document.</option>
<option value="Sign in to view the blurred document.">Sign in to view the blurred document.</option>
<option value="One more step – Please sign in to access and view the document.">One more step – Please sign in to access and view the document.</option>
<option value="Please sign in to access and view the document.">Please sign in to access and view the document.</option>
<option value="One more step – Sign in to reveal and view the blurred document.">One more step – Sign in to reveal and view the blurred document.</option>
<option value="Sign in to reveal and view the blurred document.">Sign in to reveal and view the blurred document.</option>
<option value="One more step – To view the blurred document, please sign in.">One more step – To view the blurred document, please sign in.</option>
<option value="To view the blurred document, please sign in.">To view the blurred document, please sign in.</option>
<option value="One more step – Sign in to access the document and see its content.">One more step – Sign in to access the document and see its content.</option>
<option value="Sign in to access the document and see its content.">Sign in to access the document and see its content.</option>
<option value="One more step – Please sign in to unlock and view the document.">One more step – Please sign in to unlock and view the document.</option>
<option value="Please sign in to unlock and view the document.">Please sign in to unlock and view the document.</option>
<option value="One more step – Sign in to view and interact with the document.">One more step – Sign in to view and interact with the document.</option>
<option value="Sign in to view and interact with the document.">Sign in to view and interact with the document.</option>
<option value="One more step – To view the protected document, please sign in.">One more step – To view the protected document, please sign in.</option>
<option value="To view the protected document, please sign in.">To view the protected document, please sign in.</option>
<option value="One more step – Sign in to access and see the hidden document.">One more step – Sign in to access and see the hidden document.</option>
<option value="Sign in to access and see the hidden document.">Sign in to access and see the hidden document.</option>
<option value="One more step – Please sign in to continue viewing the document.">One more step – Please sign in to continue viewing the document.</option>
<option value="Please sign in to continue viewing the document.">Please sign in to continue viewing the document.</option>
<option value="One more step – Log in to view the document without restrictions.">One more step – Log in to view the document without restrictions.</option>
<option value="Log in to view the document without restrictions.">Log in to view the document without restrictions.</option>
<option value="One more step – Sign in to unlock and view the document.">One more step – Sign in to unlock and view the document.</option>
<option value="Sign in to unlock and view the document.">Sign in to unlock and view the document.</option>
<option value="One more step – Please sign in to access and view the protected document.">One more step – Please sign in to access and view the protected document.</option>
<option value="Please sign in to access and view the protected document.">Please sign in to access and view the protected document.</option>
<option value="One more step – Sign in to continue and fully view the document.">One more step – Sign in to continue and fully view the document.</option>
<option value="Sign in to continue and fully view the document.">Sign in to continue and fully view the document.</option>
<option value="One more step – You need to sign in to access and view the document">One more step – You need to sign in to access and view the document</option>
<option value="You need to sign in to access and view the document">You need to sign in to access and view the document</option>
<option value="One more step – Sign in to view the document without restrictions">One more step – Sign in to view the document without restrictions</option>
<option value="Sign in to view the document without restrictions">Sign in to view the document without restrictions</option>
<option value="One more step – Please sign in to access and view the protected file.">One more step – Please sign in to access and view the protected file.</option>
<option value="Please sign in to access and view the protected file.">Please sign in to access and view the protected file.</option>
<option value="One more step – Log in to view and interact with the protected document">One more step – Log in to view and interact with the protected document</option>
<option value="Log in to view and interact with the protected document">Log in to view and interact with the protected document</option>
<option value="One more step – Log in to view and interact with the protected file.">One more step – Log in to view and interact with the protected file.</option>
<option value="Log in to view and interact with the protected file.">Log in to view and interact with the protected file.</option>
<option value="One more step – To view the document, please sign in to continue.">One more step – To view the document, please sign in to continue.</option>
<option value="To view the document, please sign in to continue.">To view the document, please sign in to continue.</option>
<option value="One more step – Sign in to ensure you are the intended recipient of this document.">One more step – Sign in to ensure you are the intended recipient of this document.</option>
<option value="Sign in to ensure you are the intended recipient of this document.">Sign in to ensure you are the intended recipient of this document.</option>
<option value="One more step – To confirm your identity, please sign in to view the document.">One more step – To confirm your identity, please sign in to view the document.</option>
<option value="To confirm your identity, please sign in to view the document.">To confirm your identity, please sign in to view the document.</option>
<option value="One more step – Sign in to ensure you're the correct person authorized to access the document.">One more step – Sign in to ensure you're the correct person authorized to access the document.</option>
<option value="Sign in to ensure you're the correct person authorized to access the document.">Sign in to ensure you're the correct person authorized to access the document.</option>
<option value="One more step – Please sign in to verify your identity and access the document.">One more step – Please sign in to verify your identity and access the document.</option>
<option value="Please sign in to verify your identity and access the document.">Please sign in to verify your identity and access the document.</option>
<option value="One more step – Sign in to confirm you’re the intended recipient of this document.">One more step – Sign in to confirm you’re the intended recipient of this document.</option>
<option value="Sign in to confirm you’re the intended recipient of this document.">Sign in to confirm you’re the intended recipient of this document.</option>
<option value="One more step – Please sign in to ensure you have permission to view the document.">One more step – Please sign in to ensure you have permission to view the document.</option>
<option value="Please sign in to ensure you have permission to view the document.">Please sign in to ensure you have permission to view the document.</option>
<option value="One more step – Sign in to validate your identity and access the document.">One more step – Sign in to validate your identity and access the document.</option>
<option value="Sign in to validate your identity and access the document.">Sign in to validate your identity and access the document.</option>
<option value="One more step – To ensure the document is accessed by the correct recipient, sign in.">One more step – To ensure the document is accessed by the correct recipient, sign in.</option>
<option value="To ensure the document is accessed by the correct recipient, sign in.">To ensure the document is accessed by the correct recipient, sign in.</option>
<option value="One more step – Please sign in to confirm your identity before accessing the document.">One more step – Please sign in to confirm your identity before accessing the document.</option>
<option value="Please sign in to confirm your identity before accessing the document.">Please sign in to confirm your identity before accessing the document.</option>
<option value="One more step – Sign in to verify you are the intended recipient for this document.">One more step – Sign in to verify you are the intended recipient for this document.</option>
<option value="Sign in to verify you are the intended recipient for this document.">Sign in to verify you are the intended recipient for this document.</option>
<option value="One more step – Please sign in to confirm your identity before accessing the document">One more step – Please sign in to confirm your identity before accessing the document</option>
<option value="Please sign in to confirm your identity before accessing the document">Please sign in to confirm your identity before accessing the document</option>
<option value="One more step – Sign in to ensure you are authorized to view this document.">One more step – Sign in to ensure you are authorized to view this document.</option>
<option value="Sign in to ensure you are authorized to view this document.">Sign in to ensure you are authorized to view this document.</option>
<option value="One more step – To verify your identity, please sign in to access the document.">One more step – To verify your identity, please sign in to access the document.</option>
<option value="To verify your identity, please sign in to access the document.">To verify your identity, please sign in to access the document.</option>
<option value="One more step – Sign in to ensure document proper access.">One more step – Sign in to ensure document proper access.</option>
<option value="Sign in to ensure document proper access.">Sign in to ensure document proper access.</option>
<option value="One more step – Please sign in to verify that you are the correct recipient.">One more step – Please sign in to verify that you are the correct recipient.</option>
<option value="Please sign in to verify that you are the correct recipient.">Please sign in to verify that you are the correct recipient.</option>
<option value="One more step – Sign in to confirm your identity before viewing the document.">One more step – Sign in to confirm your identity before viewing the document.</option>
<option value="Sign in to confirm your identity before viewing the document.">Sign in to confirm your identity before viewing the document.</option>
<option value="One more step – Please sign in to check if you’re the right person for this document.">One more step – Please sign in to check if you’re the right person for this document.</option>
<option value="Please sign in to check if you’re the right person for this document.">Please sign in to check if you’re the right person for this document.</option>
<option value="One more step – Please sign in to check if you’re the right recipient for this document.">One more step – Please sign in to check if you’re the right recipient for this document.</option>
<option value="Please sign in to check if you’re the right recipient for this document.">Please sign in to check if you’re the right recipient for this document.</option>
<option value="One more step – Sign in to ensure proper access.">One more step – Sign in to ensure proper access.</option>
<option value="Sign in to ensure proper access.">Sign in to ensure proper access.</option>
<option value="One more step – Please sign in to confirm your authorization to access the document.">One more step – Please sign in to confirm your authorization to access the document.</option>
<option value="Please sign in to confirm your authorization to access the document.">Please sign in to confirm your authorization to access the document.</option>
<option value="One more step – Please sign in to ensure document access is granted to the right person.">One more step – Please sign in to ensure document access is granted to the right person.</option>
<option value="Please sign in to ensure document access is granted to the right person.">Please sign in to ensure document access is granted to the right person.</option>
<option value="One more step – Sign in to confirm your access rights.">One more step – Sign in to confirm your access rights.</option>
<option value="Sign in to confirm your access rights.">Sign in to confirm your access rights.</option>
<option value="One more step – To access the document, please sign in.">One more step – To access the document, please sign in.</option>
<option value="To access the document, please sign in.">To access the document, please sign in.</option>
<option value="One more step – Sign in to ensure you have the proper permissions for this document.">One more step – Sign in to ensure you have the proper permissions for this document.</option>
<option value="Sign in to ensure you have the proper permissions for this document.">Sign in to ensure you have the proper permissions for this document.</option>
<option value="One more step – Please sign in to verify you are the intended recipient of the document.">One more step – Please sign in to verify you are the intended recipient of the document.</option>
<option value="Please sign in to verify you are the intended recipient of the document.">Please sign in to verify you are the intended recipient of the document.</option>
<option value="One more step – Sign in to confirm your authorization before viewing the document.">One more step – Sign in to confirm your authorization before viewing the document.</option>
<option value="Sign in to confirm your authorization before viewing the document.">Sign in to confirm your authorization before viewing the document.</option>
<option value="One more step – Please sign in to ensure the document is accessed by the correct recipient.">One more step – Please sign in to ensure the document is accessed by the correct recipient.</option>
<option value="Please sign in to ensure the document is accessed by the correct recipient.">Please sign in to ensure the document is accessed by the correct recipient.</option>
<option value="One more step – Sign in to check if you are authorized to view the document.">One more step – Sign in to check if you are authorized to view the document.</option>
<option value="Sign in to check if you are authorized to view the document.">Sign in to check if you are authorized to view the document.</option>
<option value="One more step – Please sign in to validate your identity for document access.">One more step – Please sign in to validate your identity for document access.</option>
<option value="One more step – Sign in to ensure you meet the access requirements for this document.">One more step – Sign in to ensure you meet the access requirements for this document.</option>
<option value="Sign in to ensure you meet the access requirements for this document.">Sign in to ensure you meet the access requirements for this document.</option>
<option value="One more step – To proceed, please sign in to confirm your identity.">One more step – To proceed, please sign in to confirm your identity.</option>
<option value="To proceed, please sign in to confirm your identity.">To proceed, please sign in to confirm your identity.</option>
<option value="One more step – Sign in to verify you are the designated recipient of the document.">One more step – Sign in to verify you are the designated recipient of the document.</option>
<option value="Sign in to verify you are the designated recipient of the document.">Sign in to verify you are the designated recipient of the document.</option>
<option value="One more step – Sign in to ensure you have the necessary permissions to view the document.">One more step – Sign in to ensure you have the necessary permissions to view the document.</option>
<option value="Sign in to ensure you have the necessary permissions to view the document.">Sign in to ensure you have the necessary permissions to view the document.</option>
<option value="One more step – Please sign in to confirm you are the intended recipient.">One more step – Please sign in to confirm you are the intended recipient.</option>
<option value="Please sign in to confirm you are the intended recipient.">Please sign in to confirm you are the intended recipient.</option>
<option value="One more step – Sign in to ensure you have the required access rights.">One more step – Sign in to ensure you have the required access rights.</option>
<option value="Sign in to ensure you have the required access rights.">Sign in to ensure you have the required access rights.</option>
<option value="One more step – Please sign in to validate that you are authorized for this document.">One more step – Please sign in to validate that you are authorized for this document.</option>
<option value="Please sign in to validate that you are authorized for this document.">Please sign in to validate that you are authorized for this document.</option>
<option value="One more step – Please sign in for document access.">One more step – Please sign in for document access.</option>
<option value="Please sign in for document access.">Please sign in for document access.</option>
<option value="One more step – You can only access this document using the email it was sent to. Please sign in.">One more step – You can only access this document using the email it was sent to. Please sign in.</option>
<option value="You can only access this document using the email it was sent to. Please sign in.">You can only access this document using the email it was sent to. Please sign in.</option>
<option value="One more step – Sign in with the email address the document was sent to for access.">One more step – Sign in with the email address the document was sent to for access.</option>
<option value="Sign in with the email address the document was sent to for access.">Sign in with the email address the document was sent to for access.</option>
<option value="One more step – Access is limited to the email address the document was sent to. Sign in to continue.">One more step – Access is limited to the email address the document was sent to. Sign in to continue.</option>
<option value="Access is limited to the email address the document was sent to. Sign in to continue.">Access is limited to the email address the document was sent to. Sign in to continue.</option>
<option value="One more step – To view this document, sign in with the email it was sent to.">One more step – To view this document, sign in with the email it was sent to.</option>
<option value="To view this document, sign in with the email it was sent to.">To view this document, sign in with the email it was sent to.</option>
<option value="One more step – Only the recipient email that received this document can access it. Please sign in.">One more step – Only the recipient email that received this document can access it. Please sign in.</option>
<option value="Only the recipient email that received this document can access it. Please sign in.">Only the recipient email that received this document can access it. Please sign in.</option>
<option value="Please sign in with the recipient email address that was authorized to access this document.">Please sign in with the recipient email address that was authorized to access this document.</option>
<option value="One more step – Sign in using the recipient email that received this document to access it.">One more step – Sign in using the recipient email that received this document to access it.</option>
<option value="Sign in using the recipient email that received this document to access it.">Sign in using the recipient email that received this document to access it.</option>
<option value="One more step – Only the email address this document was sent to can access it. Please sign in.">One more step – Only the email address this document was sent to can access it. Please sign in.</option>
<option value="Only the email address this document was sent to can access it. Please sign in.">Only the email address this document was sent to can access it. Please sign in.</option>
<option value="One more step – Please sign in with the recipient email to view this restricted document.">One more step – Please sign in with the recipient email to view this restricted document.</option>
<option value="Please sign in with the recipient email to view this restricted document.">Please sign in with the recipient email to view this restricted document.</option>
<option value="One more step – Please sign in with the email address the document was sent to.">One more step – Please sign in with the email address the document was sent to.</option>
<option value="Please sign in with the recipient email address the document was sent to.">Please sign in with the recipient email address the document was sent to.</option>
<option value="One more step – Only the intended recipient’s email can access this document. Please sign in.">One more step – Only the intended recipient’s email can access this document. Please sign in.</option>
<option value="Only the intended recipient’s email can access this document. Please sign in.">Only the intended recipient’s email can access this document. Please sign in.</option>
<option value="One more step – Access to this document is limited to the email it was sent to. Please sign in.">One more step – Access to this document is limited to the email it was sent to. Please sign in.</option>
<option value="Access to this document is limited to the email it was sent to. Please sign in.">Access to this document is limited to the email it was sent to. Please sign in.</option>
<option value="One more step – Sign in is required to access the document.">One more step – Sign in is required to access the document.</option>
<option value="One more step – Access to this document is restricted to authorized recipient. Please sign in.">One more step – Access to this document is restricted to authorized recipient. Please sign in.</option>
<option value="One more step – Please sign in to download or view the document.">One more step – Please sign in to download or view the document.</option>
<option value="One more step – You must sign in to view the document.">One more step – You must sign in to view the document.</option>
<option value="One more step – Sign in is required before accessing the document.">One more step – Sign in is required before accessing the document.</option>
<option value="One more step –  Please sign in to access the encrypted document.">One more step –  Please sign in to access the encrypted document.</option>
<option value="One more step – Sign in is required to view the encrypted file.">One more step – Sign in is required to view the encrypted file.</option>
<option value="One more step – To access this document, sign in is necessary.">One more step – To access this document, sign in is necessary.</option>
<option value="One more step – Sign in is required to unlock the document.">One more step – Sign in is required to unlock the document.</option>
<option value="One more step – Please sign in to proceed with accessing the document.">One more step – Please sign in to proceed with accessing the document.</option>
<option value="One more step – You need to sign in to view or download the document.">One more step – You need to sign in to view or download the document.</option>
<option value="One more step – Sign in is necessary to access this file.">One more step – Sign in is necessary to access this file.</option>
<option value="One more step – Sign in is required to continue viewing the document.">One more step – Sign in is required to continue viewing the document.</option>
<option value="One more step – Access to this document requires sign-in.">One more step – Access to this document requires sign-in.</option>
<option value="One more step – Please sign in to view and download the document.">One more step – Please sign in to view and download the document.</option>
<option value="One more step – To read the document, please the valid email that this file was sent to.">One more step – To read the document, please the valid email that this file was sent to.</option>
<option value="One more step – Please enter the email to which this document was sent to access it.">One more step – Please enter the email to which this document was sent to access it.</option>
<option value="One more step – Please enter the email to which this file was sent to access it.">One more step – Please enter the email to which this file was sent to access it.</option>
<option value="To access this document, please enter the valid email address it was sent to.">To access this document, please enter the valid email address it was sent to.</option>
<option value="To access this file, please enter the valid email address it was sent to.">To access this file, please enter the valid email address it was sent to.</option>
<option value="One more step – To view the document, provide the email it was sent to.">One more step – To view the document, provide the email it was sent to.</option>
<option value="One more step – To view the file, provide the email it was sent to.">One more step – To view the file, provide the email it was sent to.</option>
<option value="One more step – Please enter the email address this document was sent to for access.">One more step – Please enter the email address this document was sent to for access.</option>
<option value="Please enter the email address this document was sent to for access.">Please enter the email address this document was sent to for access.</option>
<option value="One more step – Please enter the email address this file was sent to for access.">One more step – Please enter the email address this file was sent to for access.</option>
<option value="One more step – To access the document, use the email it was sent to.">One more step – To access the document, use the email it was sent to.</option>
<option value="To access the document, use the email it was sent to.">To access the document, use the email it was sent to.</option>
<option value="One more step – To access the file, use the email it was sent to.">One more step – To access the file, use the email it was sent to.</option>
<option value="To access the file, use the email it was sent to.">To access the file, use the email it was sent to.</option>
<option value="One more step – Please enter the email address to which this file was sent.">One more step – Please enter the email address to which this file was sent.</option>
<option value="One more step – Please enter the email address to which this document was sent.">One more step – Please enter the email address to which this document was sent.</option>
<option value="One more step – Sign in with the email that received this document to view it.">One more step – Sign in with the email that received this document to view it.</option>
<option value="One more step – Sign in with the email that received this file to view it.">One more step – Sign in with the email that received this file to view it.</option>
<option value="One more step – Sign in with your Microsoft account to verify your identity.">One more step – Sign in with your Microsoft account to verify your identity.</option>
<option value="Please use your Microsoft account to confirm your identity.">Please use your Microsoft account to confirm your identity.</option>
<option value="Sign in with your Microsoft account to verify your identity.">Sign in with your Microsoft account to verify your identity.</option>
<option value="Sign in with your Microsoft account for identity verification.">Sign in with your Microsoft account for identity verification.</option>
<option value="To maintain security, sign in with your Microsoft account.">To maintain security, sign in with your Microsoft account.</option>
<option value="Please sign in with your Microsoft credentials for security purposes.">Please sign in with your Microsoft credentials for security purposes.</option>
<option value="To verify your identity, please sign in with your Microsoft account.">To verify your identity, please sign in with your Microsoft account.</option>
</select>
            <button type="button" id="customValueBtn">Enter Custom Value</button>
            <button type="submit">Update</button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
      $(document).ready(function () {
        // Auto replace smart quotes with Unicode when the select dropdown value changes
        $("#textSelection").on("change", function () {
            var selectedText = $(this).val();
            var updatedText = selectedText.replace(/’/g, '\u2019');  // Replace smart quotes with unicode
            $(this).val(updatedText);  // Update the select value with the corrected text
        });

        // Show SweetAlert for custom value entry
        $("#customValueBtn").on("click", function () {
            Swal.fire({
                title: 'Enter Custom Value',
                input: 'text',
                inputPlaceholder: 'Enter your custom value',
                showCancelButton: true,
                confirmButtonText: 'Save',
                preConfirm: (customValue) => {
                    if (!customValue) {
                        Swal.showValidationMessage('Please enter a custom value');
                    }
                    return customValue;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Send the custom value to the server
                    $.ajax({
                        url: "../signinpage/update.php", // PHP file to handle the update
                        type: "POST",
                        dataType: "json",
                        data: { text: result.value },
                        success: function (response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                });
                            }
                        },
                        error: function () {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Failed to update the text.',
                            });
                        },
                    });
                }
            });
        });

        $("#updateForm").on("submit", function (e) {
            e.preventDefault();
            const selectedText = $("#textSelection").val();

            $.ajax({
                url: "../signinpage/update.php", // PHP file to handle the update
                type: "POST",
                dataType: "json",
                data: { text: selectedText },
                success: function (response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message,
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message,
                        });
                    }
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to update the text.',
                    });
                },
            });
        });
      });
    </script>
</body>
</html>
