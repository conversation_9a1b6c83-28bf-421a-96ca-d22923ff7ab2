
<?php require '../assets/admin_header.php';
require('../php_files/db.php');
require('../php_files/functions.php');
  ?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Wallet Address List</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .url-list {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .url-list h3 {
            margin-top: 0;
        }
        .wallet-input {
            margin-bottom: 10px;
        }
        button {
            margin-top: 10px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }
        button.add-field {
            background-color: #28a745;
            color: white;
            border: none;
        }
        button.submit {
            background-color: #007bff;
            color: white;
            border: none;
        }
    </style>
</head>
<body>

    <h1>Sign in Page url Manager</h1>
    <form id="walletForm">
        <div id="walletLists">
            <div class="url-list">
                <h3>Sign url List 1</h3>
                <div class="wallet-input">
                    <input type="text" name="siginpagrul[]" placeholder="Enter signin page url" required>
                </div>
            </div>
        </div>
        <button type="button" class="add-field" onclick="addWalletList()">+ Add More url List</button>
        <button type="button" class="submit" id="submitWallets">Submit</button>
    </form>

    <script>
        let walletListCounter = 1;

        function addWalletList() {
            walletListCounter++;
            const walletLists = document.getElementById('walletLists');
            const newList = document.createElement('div');
            newList.classList.add('url-list');
            newList.innerHTML = `
                <h3>Wallet List ${walletListCounter}</h3>
                <div class="wallet-input">
                    <input type="text" name="siginpagrul[]" placeholder="Enter Signin url" required>
                </div>
            `;
            walletLists.appendChild(newList);
        }

        $(document).ready(function () {
            $('#submitWallets').on('click', function () {
                const formData = $('#walletForm').serialize();

                $.ajax({
                    url: 'submitsigninpage.php',
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        Swal.fire({
                            title: 'Success!',
                            text: response.message + ' ' + response.successful_inserts + ' ' + response.skipped_inserts,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                        $('#walletLists').html(`
                            <div class="url-list">
                                <h3>Wallet List 1</h3>
                                <div class="wallet-input">
                                    <input type="text" name="siginpagrul[]" placeholder="Enter sign page url" required>
                                </div>
                            </div>
                        `);
                        walletListCounter = 1;
                    },
                    error: function () {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
        });
    </script>

</body>
</html>
