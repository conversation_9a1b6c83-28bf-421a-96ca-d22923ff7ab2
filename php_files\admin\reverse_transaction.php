<?php
// reverse_transaction.php

// Include database connection or other necessary files
include '../db.php';
include '../functions.php';

// Get the transaction ID from the POST request
$transactionId = $_POST['transaction_id'];

// Check if the transaction ID is valid
if (isset($transactionId)) {
    
    global $pdo;
    
    // Fetch both the debit and credit transactions
    $sql = "SELECT * FROM wallet_transactions WHERE transaction_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$transactionId]);
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($transactions) === 2) { // Expecting two transactions: one debit, one credit
        // Find the debit and credit transactions
        $debitTransaction = null;
        $creditTransaction = null;

        foreach ($transactions as $transaction) {
            if ($transaction['type'] === 'debit') {
                $debitTransaction = $transaction;
            } else if ($transaction['type'] === 'credit') {
                $creditTransaction = $transaction;
            }
        }

        if ($debitTransaction && $creditTransaction) {
            // Update the sender's wallet (add the amount back)
            $sqlUpdateSender = "UPDATE wallet SET balance = balance + ? WHERE user_id = ?";
            $stmtSender = $pdo->prepare($sqlUpdateSender);
            $stmtSender->execute([$debitTransaction['amount'], $debitTransaction['user_id']]);

            // Check if receiver's balance would go negative after subtraction
            $sqlCheckReceiverBalance = "SELECT balance FROM wallet WHERE user_id = ?";
            $stmtCheckReceiver = $pdo->prepare($sqlCheckReceiverBalance);
            $stmtCheckReceiver->execute([$creditTransaction['user_id']]);
            $receiverWallet = $stmtCheckReceiver->fetch(PDO::FETCH_ASSOC);
            $newReceiverBalance = $receiverWallet['balance'] - $creditTransaction['amount'];

            if ($newReceiverBalance < 0) {
                // If the balance would go negative, set it to zero
                $newReceiverBalance = 0;
            }

            // Update the receiver's wallet (set to zero if insufficient balance)
            $sqlUpdateReceiver = "UPDATE wallet SET balance = ? WHERE user_id = ?";
            $stmtReceiver = $pdo->prepare($sqlUpdateReceiver);
            $stmtReceiver->execute([$newReceiverBalance, $creditTransaction['user_id']]);

            // Delete both the debit and credit transactions from the wallet_transactions table
            $sqlDelete = "DELETE FROM wallet_transactions WHERE transaction_id = ?";
            $stmtDelete = $pdo->prepare($sqlDelete);
            $stmtDelete->execute([$transactionId]);

            // Respond with success
            echo json_encode(['success' => true, 'message' => 'Transaction reversed successfully!']);
        } else {
            // Respond with error if the debit/credit transaction is not found
            echo json_encode(['success' => false, 'message' => 'Error processing the transaction.']);
        }
    } else {
        // Respond with error if the transaction was not found
        echo json_encode(['success' => false, 'message' => 'Transaction not found or does not exist.']);
    }
} else {
    // Respond with error if transaction ID is missing
    echo json_encode(['success' => false, 'message' => 'Transaction ID is required.']);
}
?>
