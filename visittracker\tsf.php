<?php

// Enable error reporting
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

// Set JSON content type
header("Content-Type: application/json");

// Include the configuration file to get database credentials
$config = include 'extractedconfig.php';




// Validate and initialize dbName
$dbName = $config['dbname'];

// Function to initialize the database
function initializeDatabase($config) {
    $host = $config['host'];
    $username = $config['username'];
    $password = $config['password'];
    global $dbName; // Use global $dbName

    $mysqli = new mysqli($host, $username, $password);

    if ($mysqli->connect_error) {
        die(json_encode(['status' => 'error', 'message' => 'Database connection failed: ' . $mysqli->connect_error]));
    }

    // Check and escape dbName
    $dbName = $mysqli->real_escape_string($dbName);

    $result = $mysqli->query("SHOW DATABASES LIKE '$dbName'");

    if ($result->num_rows == 0) {
        $mysqli->query("CREATE DATABASE `$dbName`");
    }

    $mysqli->select_db($dbName);

    return $mysqli;
}

// Initialize the database connection
$mysqli = initializeDatabase($config);





// Create database if not exists
$mysqli->query("CREATE DATABASE IF NOT EXISTS $dbName");
$mysqli->select_db($dbName);

// Create visitors table
$mysqli->query("
    CREATE TABLE IF NOT EXISTS visitors (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        fingerprint VARCHAR(255) NOT NULL,
        ip VARCHAR(45) NOT NULL,
        user_agent TEXT NOT NULL,
        region VARCHAR(100) NOT NULL,
        city VARCHAR(100) NOT NULL,
        country VARCHAR(100) NOT NULL,
        countrycode VARCHAR(100) NOT NULL,
        zip VARCHAR(100) NOT NULL,
        timezone VARCHAR(100) NOT NULL,
        session_start DATETIME NOT NULL,
        session_end DATETIME NOT NULL,
        pages_visited TEXT NOT NULL,
        session_duration INT NOT NULL,
        visit_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        is_returning BOOLEAN NOT NULL DEFAULT FALSE,
        os VARCHAR(100) NOT NULL,
        browser VARCHAR(100) NOT NULL,
        browser_engine VARCHAR(100) NOT NULL,
        device_type VARCHAR(50) NOT NULL,
        device_model VARCHAR(100) NOT NULL,
         flag_url VARCHAR(255) NOT NULL,
        device_manufacturer VARCHAR(100) NOT NULL
    )
");

// Create alerts table
$mysqli->query("
    CREATE TABLE IF NOT EXISTS alerts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        ip VARCHAR(45) NOT NULL,
        alert_time DATETIME NOT NULL,
        message VARCHAR(255) NOT NULL
    )
");

return $mysqli;



// Get the POST data
$inputData = file_get_contents('php://input');
$data = json_decode($inputData, true);

    $fingerprint = $data['fingerprint'] ?? '';
    $ip = $data['ip'] ?? '';
    $userAgent = $data['user_agent'] ?? '';
    $pagesVisited = implode(', ', $data['pages_visited'] ?? []);
    $username = $data['username'] ?? '';
    $region = $data['Region'] ?? '';
    $city = $data['City'] ?? '';
    $countryCode = $data['ecountryCode'] ?? '';
    $zip = $data['Zip'] ?? '';
    $timezone = $data['Timezone'] ?? '';

    // Log the data (for example, save to a file or database)
    $logEntry = sprintf(
        "Fingerprint: %s | IP: %s | User Agent: %s | Pages Visited: %s | Username: %s | Region: %s | City: %s | Country Code: %s | Zip: %s | Timezone: %s\n",
        $fingerprint, $ip, $userAgent, $pagesVisited, $username, $region, $city, $countryCode, $zip, $timezone
    );

    // Save the log to a file or database
    file_put_contents('visit_logs.txt', $logEntry, FILE_APPEND);

 


// Log POST data for debugging
file_put_contents('debug_log.txt', print_r($data, true), FILE_APPEND);

// Log the received data
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'fingerprint' => $fingerprint,
    'ip' => $ip,
    'user_agent' => $userAgent,
    'region' => $region,
    'city' => $city,
    'ecountry_code' => $ecountryCode,
    'zip' => $zip,
    'timezone' => $timezone,
    'pages_visited' => json_encode($pagesVisited),
];

$logMessage = json_encode($logData) . PHP_EOL;

// Append to log file
if (!file_put_contents('post_data_log.txt', $logMessage, FILE_APPEND)) {
    echo json_encode(['status' => 'error', 'message' => 'Failed to write to log file']);
    exit;
}

// Respond with success or process further if needed
echo json_encode(['status' => 'success', 'message' => 'Data logged successfully']);




$username = $_POST['username'] ?? '';
error_log('username: ' . $username);


$country =  getCountryName($countryCode);

// Log the result
$countryName = getCountryName($countryCode);
error_log('Country Name: ' . $countryName);


// Validate and sanitize the username
$username = filter_var($username, FILTER_SANITIZE_STRING);


$userId = $username;

// Check if the user exists based on the username
$query = "SELECT id FROM user_profiles WHERE username = ?";
$stmt = $mysqli->prepare($query);
$stmt->bind_param('s', $username);
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows > 0) {
    // User exists, proceed to log the visit
    $stmt->bind_result($userId);
    $stmt->fetch();

    // Log the visitor details
    logVisitor($mysqli, $userId, $fingerprint, $ip, $userAgent, $region, $city, $country, $zip, $timezone, $pagesVisited);

    monitorSecurity($mysqli, $userId, $ip);

    echo json_encode(['status' => 'success', 'message' => 'Visitor data logged.']);
} else {
    // User not found
    echo json_encode(['status' => 'error', 'message' => 'User not found.']);
}

// Log the visitor details
function logVisitor($mysqli, $userId, $fingerprint, $ip, $userAgent, $region, $city, $country, $zip, $timezone, $pagesVisited) {
    $deviceDetails = getDeviceDetails($userAgent);
  
    $sessionStart = date('Y-m-d H:i:s');
    $sessionEnd = date('Y-m-d H:i:s');
    $pagesVisitedStr = implode(',', $pagesVisited);

    // Calculate session duration
    $duration = (strtotime($sessionEnd) - strtotime($sessionStart));

    // Check if the visitor is returning
   $isReturning = isReturningVisitor($mysqli, $userId, $ip);


    $query = "INSERT INTO visitors (user_id, fingerprint, ip, user_agent, region, city, country, zip, timezone, session_start, session_end, pages_visited, session_duration, visit_time, is_returning, os, browser, browser_engine, device_type, device_model,flag_url, device_manufacturer) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $mysqli->prepare($query);
$stmt->bind_param('issssssssssiissssss', $userId, $fingerprint, $ip, $userAgent, $region, $city, $country, $zip, $timezone, $sessionStart, $sessionEnd, $pagesVisitedStr, $duration, date('Y-m-d H:i:s'), $isReturning, $deviceDetails['os'], $deviceDetails['browser'], $deviceDetails['browser_engine'], $deviceDetails['device_type'], $deviceDetails['device_model'], $details['flagUrl'], $deviceDetails['device_manufacturer']);
$stmt->execute();
    
    $stmt->execute();
    $stmt->close();
}

// Monitor security
function monitorSecurity($mysqli, $userId, $ip) {
    $query = "SELECT COUNT(*) AS visit_count FROM visitors WHERE ip = ? AND user_id = ? AND visit_time > NOW() - INTERVAL 1 HOUR";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("si", $ip, $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    if ($row['visit_count'] > 20) {
        $stmt = $mysqli->prepare("INSERT INTO alerts (user_id, ip, alert_time, message) VALUES (?, ?, NOW(), ?)");
        $message = "Unusual traffic detected from IP $ip";
        $stmt->bind_param("iss", $userId, $ip, $message);
        $stmt->execute();
        $stmt->close();
    }
}

// Function to detect device details
function getDeviceDetails($userAgent) {
    if (empty($userAgent)) {
        return [
            'os' => 'Bot',
            'browser' => 'Bot',
            'browser_engine' => 'Bot',
            'device_type' => 'Bot',
            'device_model' => 'Bot',
            'device_manufacturer' => 'Bot'
        ];
    }

    $osPlatform = "Unknown OS";
    $browser = "Unknown Browser";
    $browserEngine = "Unknown Engine";
    $deviceType = "Unknown Device";
    $deviceManufacturer = "Unknown Manufacturer";

    $osArray = [
        '/windows nt 10/i'     => 'Windows 10',
        '/windows nt 6.3/i'    => 'Windows 8.1',
        '/macintosh|mac os x/i'=> 'Mac OS X',
        '/linux/i'             => 'Linux',
        '/iphone/i'            => 'iPhone',
       '/windows nt 10/i' => 'Windows 10',
                '/windows nt 6.2/i' => 'Windows 8',
                '/windows nt 6.1/i' => 'Windows 7',
                '/windows nt 6.0/i' => 'Windows Vista',
                '/windows nt 5.2/i' => 'Windows Server 2003/XP x64',
                '/windows nt 5.1/i' => 'Windows XP',
                '/windows xp/i' => 'Windows XP',
                '/windows nt 5.0/i' => 'Windows 2000',
                '/windows me/i' => 'Windows ME',
                '/win98/i' => 'Windows 98',
                '/win95/i' => 'Windows 95',
                '/win16/i' => 'Windows 3.11',
                '/macintosh|mac os x/i' => 'Mac OS X',
                '/mac_powerpc/i' => 'Mac OS 9',
                '/linux/i' => 'Linux',
                '/ubuntu/i' => 'Ubuntu',
                '/iphone/i' => 'iPhone',
                '/ipod/i' => 'iPod',
                '/ipad/i' => 'iPad',
                '/android/i' => 'Android',
                '/blackberry/i' => 'BlackBerry',
                '/webos/i' => 'Mobile'
    ];

    foreach ($osArray as $regex => $value) {
        if (preg_match($regex, $userAgent)) {
            $osPlatform = $value;
            break;
        }
    }

    $browserArray = [
        '/firefox/i'   => 'Firefox',
        '/chrome/i'    => 'Chrome',
        '/safari/i'    => 'Safari',
        '/edge/i'      => 'Edge',
         '/netscape/i' => 'Netscape',
      '/msie/i' => 'Internet Explorer',
                '/maxthon/i' => 'Maxthon',
                '/konqueror/i' => 'Konqueror',
        '/opera/i'     => 'Opera',
    ];

    foreach ($browserArray as $regex => $value) {
        if (preg_match($regex, $userAgent)) {
            $browser = $value;
            break;
        }
    }

    // Browser engine detection
    if (preg_match('/Trident/i', $userAgent)) {
        $browserEngine = 'Trident (Internet Explorer)';
    } elseif (preg_match('/Edg/i', $userAgent)) {
        $browserEngine = 'Blink (Edge)';
    } elseif (preg_match('/Gecko/i', $userAgent)) {
        $browserEngine = 'Gecko (Firefox)';
    } elseif (preg_match('/WebKit/i', $userAgent)) {
        $browserEngine = 'WebKit (Safari/Chrome)';
    } elseif (preg_match('/Blink/i', $userAgent)) {
        $browserEngine = 'Blink (Chrome/Edge)';
    }

    if (preg_match('/mobile/i', $userAgent)) {
        $deviceType = 'Mobile';
    } elseif (preg_match('/tablet/i', $userAgent)) {
        $deviceType = 'Tablet';
    } else {
        $deviceType = 'Desktop';
    }

    // Extract device manufacturer
    if (preg_match('/(Apple|Samsung|Xiaomi|Huawei|Oppo|OnePlus|LG|Nokia|Sony|Asus|Motorola|Realme|Vivo)/i', $userAgent, $matches)) {
        $deviceManufacturer = $matches[1];
    }

    return [
        'os' => $osPlatform,
        'browser' => $browser,
        'browser_engine' => $browserEngine,
        'device_type' => $deviceType,
        'device_model' => getDeviceModel($userAgent),
        'device_manufacturer' => $deviceManufacturer
    ];
}

// Helper function to extract device model
function getDeviceModel($userAgent) {
    if (preg_match('/\b(iPhone|iPad|iPod|Android|Windows Phone)\b/i', $userAgent, $matches)) {
        return ucfirst($matches[0]);
    }
    return 'Unknown';
}

// Function to check if the visitor is returning
function isReturningVisitor($mysqli, $userId, $ip) {
    $query = "SELECT COUNT(*) AS visit_count FROM visitors WHERE ip = ? AND user_id = ? AND DATE(visit_time) = CURDATE()";
    $stmt = $mysqli->prepare($query);
    $stmt->bind_param("si", $ip, $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    return $row['visit_count'] > 1;
}



// Array of all country codes
$countryCodes = [
    'af' => 'Afghanistan',
    'al' => 'Albania',
    'dz' => 'Algeria',
    'as' => 'American Samoa',
    'ad' => 'Andorra',
    'ao' => 'Angola',
    'ai' => 'Anguilla',
    'aq' => 'Antarctica',
    'ag' => 'Antigua and Barbuda',
    'ar' => 'Argentina',
    'am' => 'Armenia',
    'aw' => 'Aruba',
    'au' => 'Australia',
    'at' => 'Austria',
    'az' => 'Azerbaijan',
    'bs' => 'Bahamas',
    'bh' => 'Bahrain',
    'bd' => 'Bangladesh',
    'bb' => 'Barbados',
    'by' => 'Belarus',
    'be' => 'Belgium',
    'bz' => 'Belize',
    'bj' => 'Benin',
    'bm' => 'Bermuda',
    'bt' => 'Bhutan',
    'bo' => 'Bolivia',
    'ba' => 'Bosnia and Herzegovina',
    'bw' => 'Botswana',
    'br' => 'Brazil',
    'bn' => 'Brunei',
    'bg' => 'Bulgaria',
    'bf' => 'Burkina Faso',
    'bi' => 'Burundi',
    'kh' => 'Cambodia',
    'cm' => 'Cameroon',
    'ca' => 'Canada',
    'cv' => 'Cape Verde',
    'ky' => 'Cayman Islands',
    'cf' => 'Central African Republic',
    'td' => 'Chad',
    'cl' => 'Chile',
    'cn' => 'China',
    'co' => 'Colombia',
    'km' => 'Comoros',
    'cg' => 'Congo',
    'cd' => 'Congo (Democratic Republic)',
    'cr' => 'Costa Rica',
    'ci' => 'C么te d\'Ivoire',
    'hr' => 'Croatia',
    'cu' => 'Cuba',
    'cy' => 'Cyprus',
    'cz' => 'Czech Republic',
    'dk' => 'Denmark',
    'dj' => 'Djibouti',
    'dm' => 'Dominica',
    'do' => 'Dominican Republic',
    'ec' => 'Ecuador',
    'eg' => 'Egypt',
    'sv' => 'El Salvador',
    'gq' => 'Equatorial Guinea',
    'er' => 'Eritrea',
    'ee' => 'Estonia',
    'sz' => 'Eswatini',
    'et' => 'Ethiopia',
    'fj' => 'Fiji',
    'fi' => 'Finland',
    'fr' => 'France',
    'ga' => 'Gabon',
    'gm' => 'Gambia',
    'ge' => 'Georgia',
    'de' => 'Germany',
    'gh' => 'Ghana',
    'gi' => 'Gibraltar',
    'gr' => 'Greece',
    'gl' => 'Greenland',
    'gd' => 'Grenada',
    'gu' => 'Guam',
    'gt' => 'Guatemala',
    'gn' => 'Guinea',
    'gw' => 'Guinea-Bissau',
    'gy' => 'Guyana',
    'ht' => 'Haiti',
    'hn' => 'Honduras',
    'hk' => 'Hong Kong',
    'hu' => 'Hungary',
    'is' => 'Iceland',
    'in' => 'India',
    'id' => 'Indonesia',
    'ir' => 'Iran',
    'iq' => 'Iraq',
    'ie' => 'Ireland',
    'il' => 'Israel',
    'it' => 'Italy',
    'jm' => 'Jamaica',
    'jp' => 'Japan',
    'jo' => 'Jordan',
    'kz' => 'Kazakhstan',
    'ke' => 'Kenya',
    'ki' => 'Kiribati',
    'kp' => 'North Korea',
    'kr' => 'South Korea',
    'kw' => 'Kuwait',
    'kg' => 'Kyrgyzstan',
    'la' => 'Laos',
    'lv' => 'Latvia',
    'lb' => 'Lebanon',
    'ls' => 'Lesotho',
    'lr' => 'Liberia',
    'ly' => 'Libya',
    'li' => 'Liechtenstein',
    'lt' => 'Lithuania',
    'lu' => 'Luxembourg',
    'mo' => 'Macao',
    'mk' => 'North Macedonia',
    'mg' => 'Madagascar',
    'mw' => 'Malawi',
    'my' => 'Malaysia',
    'mv' => 'Maldives',
    'ml' => 'Mali',
    'mt' => 'Malta',
    'mh' => 'Marshall Islands',
    'mr' => 'Mauritania',
    'mu' => 'Mauritius',
    'mx' => 'Mexico',
    'fm' => 'Micronesia',
    'md' => 'Moldova',
    'mc' => 'Monaco',
    'mn' => 'Mongolia',
    'me' => 'Montenegro',
    'ma' => 'Morocco',
    'mz' => 'Mozambique',
    'mm' => 'Myanmar',
    'na' => 'Namibia',
    'nr' => 'Nauru',
    'np' => 'Nepal',
    'nl' => 'Netherlands',
    'nz' => 'New Zealand',
    'ni' => 'Nicaragua',
    'ne' => 'Niger',
    'ng' => 'Nigeria',
    'no' => 'Norway',
    'om' => 'Oman',
    'pk' => 'Pakistan',
    'pw' => 'Palau',
    'pa' => 'Panama',
    'pg' => 'Papua New Guinea',
    'py' => 'Paraguay',
    'pe' => 'Peru',
    'ph' => 'Philippines',
    'pl' => 'Poland',
    'pt' => 'Portugal',
    'qa' => 'Qatar',
    'ro' => 'Romania',
    'ru' => 'Russia',
    'rw' => 'Rwanda',
    'ws' => 'Samoa',
    'sm' => 'San Marino',
    'sa' => 'Saudi Arabia',
    'sn' => 'Senegal',
    'rs' => 'Serbia',
    'sc' => 'Seychelles',
    'sl' => 'Sierra Leone',
    'sg' => 'Singapore',
    'sk' => 'Slovakia',
    'si' => 'Slovenia',
    'so' => 'Somalia',
    'za' => 'South Africa',
    'es' => 'Spain',
    'lk' => 'Sri Lanka',
    'sd' => 'Sudan',
    'sr' => 'Suriname',
    'se' => 'Sweden',
    'ch' => 'Switzerland',
    'sy' => 'Syria',
    'tw' => 'Taiwan',
    'tj' => 'Tajikistan',
    'tz' => 'Tanzania',
    'th' => 'Thailand',
    'tg' => 'Togo',
    'tk' => 'Tokelau',
    'to' => 'Tonga',
    'tn' => 'Tunisia',
    'tr' => 'Turkey',
    'tm' => 'Turkmenistan',
    'tv' => 'Tuvalu',
    'ug' => 'Uganda',
    'ua' => 'Ukraine',
    'ae' => 'United Arab Emirates',
    'gb' => 'United Kingdom',
    'us' => 'United States',
    'uy' => 'Uruguay',
    'uz' => 'Uzbekistan',
    'vu' => 'Vanuatu',
    've' => 'Venezuela',
    'vn' => 'Vietnam',
    'ye' => 'Yemen',
    'zm' => 'Zambia',
    'zw' => 'Zimbabwe'
];


function getCountryName($countryCode) {
    global $countryNames;
    
    // Ensure country code is lowercase
    $countryCode = strtolower(trim($countryCode));
    
    // Return country name if found, otherwise return null or handle accordingly
    return $countryNames[$countryCode] ?? null;
}



// Function to get country name and flag URL
function getCountryDetails($ecountryCode) {
    global $countryCodes;
    
    // Ensure country code is lowercase
    $countryCode = strtolower(trim($ecountryCode));
    
    // Auto-detect the base path
    $basePath = __DIR__; // Current directory path
    $flagUrl = isset($countryCodes[$countryCode]) 
        ? sprintf("%s/flags/%s.png", $basePath, $countryCode)
        : null; // Provide a fallback or default value
    
    return ['flagUrl' => $flagUrl];
}


$details = getCountryDetails($ecountryCode, $basePath);



?>