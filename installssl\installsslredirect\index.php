<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL Certificate Installer</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            text-align: center;
            padding-top: 50px;
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
        }
        #installSSL {
            background-color: #28a745;
            color: white;
        }
        #installClientCert {
            background-color: #007bff;
            color: white;
        }
        p.warning {
            color: #ff0000;
            font-weight: bold;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>

    <h1>Redirect link SSL Certificate Installer</h1>
    <p class="warning">
        SSL and Client Certificates are auto installed by default on your Redirect link.<br>
        Only use these options if your Redirect link SSL certificate has expired.
    </p>
    <button id="installSSL">Reinstall SSL Certificate</button>
    <button id="installClientCert">Reinstall Client Certificate</button>

    <script>
        // Function to show success or error messages using SweetAlert
        function showAlert(type, title, text) {
            Swal.fire({
                icon: type,
                title: title,
                text: text,
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'OK'
            });
        }

        // Function to confirm SSL reinstallation
        function confirmAction(action, installFunction) {
            Swal.fire({
                title: `Are you sure?`,
                text: `This should only be used if your ${action} has expired.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, proceed',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    installFunction();
                }
            });
        }

        // Function to request SSL Certificate installation
        function installSSLCertificate() {
            Swal.fire({
                title: 'Installing SSL Certificate...',
                didOpen: () => { Swal.showLoading(); }
            });

            fetch('instalssl.php')
                .then(response => response.text())
                .then(data => {
                    showAlert('success', 'SSL Installed', data);
                })
                .catch(error => {
                    showAlert('error', 'Installation Failed', error);
                });
        }

        // Function to request Client Certificate installation
        function installClientCertificate() {
            Swal.fire({
                title: 'Installing Client Certificate...',
                didOpen: () => { Swal.showLoading(); }
            });

            fetch('enableclientssl.php')
                .then(response => response.text())
                .then(data => {
                    showAlert('success', 'Client Certificate Installed', data);
                })
                .catch(error => {
                    showAlert('error', 'Installation Failed', error);
                });
        }

        // Event listeners for button clicks with confirmation
        document.getElementById('installSSL').addEventListener('click', () => {
            confirmAction('SSL Certificate', installSSLCertificate);
        });

        document.getElementById('installClientCert').addEventListener('click', () => {
            confirmAction('Client Certificate', installClientCertificate);
        });
    </script>

</body>
</html>
