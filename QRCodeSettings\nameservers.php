<?php
// Start session to access session variables
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 


// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];



// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ../logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}




// Get the signed-in user's ID from the session
$signedInUserId = $_SESSION['user_id'];

try {
    // Connect to the database using PDO
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set PDO attributes for better error handling
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Ensure `cloudflareemail` column exists in `user_profiles` table
$checkColumnQuery = $pdo->query("
    SHOW COLUMNS FROM `user_profiles` LIKE 'cloudflareemail'
");
if ($checkColumnQuery->rowCount() === 0) {
    $pdo->exec("ALTER TABLE `user_profiles` ADD `cloudflareemail` VARCHAR(255) NULL");
}

// Ensure `cloudflareapikey` column exists in `user_profiles` table
$checkApiKeyColumnQuery = $pdo->query("
    SHOW COLUMNS FROM `user_profiles` LIKE 'cloudflareapikey'
");
if ($checkApiKeyColumnQuery->rowCount() === 0) {
    $pdo->exec("ALTER TABLE `user_profiles` ADD `cloudflareapikey` VARCHAR(255) NULL");
}

// Fetch the user's Cloudflare email and API key
$query = $pdo->prepare("
    SELECT cloudflareemail, cloudflareapikey
    FROM user_profiles 
    WHERE user_id = ?
");
$query->execute([$signedInUserId]);
$user = $query->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    die('User not found in the database.');
}

// Ensure Cloudflare email and API key are set
if (empty($user['cloudflareemail'])) {
    die('Cloudflare email is not set for this user.');
}

if (empty($user['cloudflareapikey'])) {
    die('Cloudflare API key is not set for this user.');
}

$signedInEmail = $user['cloudflareemail'];
$apiKey = $user['cloudflareapikey']; // Fetch the API key from the database






// Fetch the domain name for the signed-in user from `QRCodeSettingsdnsdomain_requests`
$query = $pdo->prepare("
    SELECT domain_name 
    FROM QRCodeSettingsdnsdomain_requests 
    WHERE user_id = ?
");
$query->execute([$signedInUserId]);
$domainRow = $query->fetch(PDO::FETCH_ASSOC);

// if (!$domainRow) {
//     die('No domain found for this user.');
// }


$sdhdsd = $domainRow['domain_name']; // Dynamic domain value

// Cloudflare API URL
$apiUrl = 'https://api.cloudflare.com/client/v4/zones';

// Ensure the `QRCodeattachnameservers` table exists
$pdo->exec("
    CREATE TABLE IF NOT EXISTS `QRCodeattachnameservers` (
        `id` INT AUTO_INCREMENT PRIMARY KEY,
        `user_id` INT NOT NULL,
        `domain` VARCHAR(255) NOT NULL,
        `nameserver` VARCHAR(255) NOT NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY `unique_entry` (`user_id`, `domain`, `nameserver`),
        FOREIGN KEY (`user_id`) REFERENCES `user_profiles`(`user_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
");

// Initialize cURL session
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
   'X-Auth-Email: ' . $signedInEmail,
    'X-Auth-Key: ' . $apiKey,
    'Content-Type: application/json',
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['name' => $sdhdsd]));

// Execute cURL request and fetch response
$response = curl_exec($ch);

function clearSensitiveData(&$token, &$email) {
    if (isset($token, $email)) {
        unset($token, $email);
    }
}
clearSensitiveData($apiKey, $signedInEmail);


// Check if cURL request was successful
if ($response === false) {
    $error = 'Error: ' . curl_error($ch);
} else {
    // Decode the JSON response
    $json = json_decode($response, true);
    if ($json['success']) {
        $successMessage = "Domain added successfully!";

        // Retrieve the nameservers from the response
        $nameservers = $json['result']['name_servers']; // Nameservers array
        
        // Store nameservers in the database (avoid duplicates)
        $stmt = $pdo->prepare("
            INSERT INTO `QRCodeattachnameservers` (user_id, domain, nameserver) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE nameserver = VALUES(nameserver)
        ");

        foreach ($nameservers as $nameserver) {
            $stmt->execute([$signedInUserId, $sdhdsd, $nameserver]);
        }
    } else {
        // $error = "Error adding domain: " . $json['errors'][0]['message'];
    }
}

// Close cURL session
curl_close($ch);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare API Response</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .message {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f4f4f4;
            border: 1px solid #ddd;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .nameservers {
            margin-top: 10px;
        }
    </style>
</head>
<body>

<!--<?php if (isset($successMessage)): ?>-->
<!--    <div class="message success">-->
<!--        <p><?php echo $successMessage; ?></p>-->
<!--        <p>Cloudflare Nameservers for <?php echo htmlspecialchars($sdhdsd); ?>:</p>-->
<!--        <ul class="nameservers">-->
<!--            <?php foreach ($nameservers as $nameserver): ?>-->
<!--                <li><?php echo htmlspecialchars($nameserver); ?></li>-->
<!--            <?php endforeach; ?>-->
<!--        </ul>-->
<!--    </div>-->
<!--<?php endif; ?>-->

<!--<?php if (isset($error)): ?>-->
<!--    <div class="message error">-->
<!--        <p><?php echo $error; ?></p>-->
<!--    </div>-->
<!--<?php endif; ?>-->

</body>
</html>
