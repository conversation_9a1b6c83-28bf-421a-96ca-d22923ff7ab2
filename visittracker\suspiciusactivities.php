<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION['user_id'])) {
    die("Access denied. Please log in.");
}

$config = include 'extractedconfig.php';

$host = $config['host']; // Change this to your database host
$dbname = $config['dbname']; // Change this to your database name
$username = $config['username']; // Change this to your database username
$password = $config['password']; // Change this to your database password

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get the logged-in user's ID
$loggedInUserId = $_SESSION['user_id'];

// Fetch alerts for suspicious activities
$queryAlerts = "SELECT a.id, a.user_id, a.ip, a.alert_time, a.message, a.fingerprint, 
                        MAX(v.country) AS country, MAX(v.flag_url) AS flag_url, MAX(v.user_agent) AS user_agent,
                        MAX(v.region) AS region, MAX(v.city) AS city, MAX(v.countrycode) AS countrycode, 
                        MAX(v.zip) AS zip, MAX(v.timezone) AS timezone, MAX(v.os) AS os, 
                        MAX(v.browser) AS browser, MAX(v.browser_engine) AS browser_engine, 
                        MAX(v.device_type) AS device_type, MAX(v.device_model) AS device_model, 
                        MAX(v.is_returning) AS is_returning
                FROM alerts a
                LEFT JOIN visitors v ON a.fingerprint = v.fingerprint 
                WHERE a.user_id = :user_id
                GROUP BY a.id
                ORDER BY a.alert_time DESC";





$stmtAlerts = $pdo->prepare($queryAlerts);
$stmtAlerts->bindParam(':user_id', $loggedInUserId, PDO::PARAM_INT);
$stmtAlerts->execute();
$alerts = $stmtAlerts->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suspicious Activities & Alerts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #333;
            color: white;
        }
        img {
            width: 30px;
            height: auto;
        }
    </style>
</head>
<body>

    <h2>Suspicious Activities & Alerts</h2>

    <?php if (empty($alerts)): ?>
        <p>No suspicious activities detected.</p>
    <?php else: ?>
    <table>
        <tr>
            <th>IP Address</th>
            <th>Alert Time</th>
            <th>Message</th>
            <th>Fingerprint</th>
            <th>User Agent</th>
            <th>Region</th>
            <th>City</th>
            <th>Country</th>
            <th>Country Code</th>
            <th>ZIP</th>
            <th>Timezone</th>
            <th>Returning Visitor?</th>
            <th>OS</th>
            <th>Browser</th>
            <th>Browser Engine</th>
            <th>Device Type</th>
            <th>Device Model</th>
            <th>Flag</th>
        </tr>
        <?php foreach ($alerts as $alert): ?>
        <tr>
            <td><?= htmlspecialchars($alert['ip']) ?></td>
            <td><?= htmlspecialchars($alert['alert_time']) ?></td>
            <td><?= htmlspecialchars($alert['message']) ?></td>
            <td><?= htmlspecialchars($alert['fingerprint']) ?></td>
            <td><?= htmlspecialchars($alert['user_agent']) ?></td>
            <td><?= htmlspecialchars($alert['region']) ?></td>
            <td><?= htmlspecialchars($alert['city']) ?></td>
            <td><?= htmlspecialchars($alert['country']) ?></td>
            <td><?= htmlspecialchars($alert['countrycode']) ?></td>
            <td><?= htmlspecialchars($alert['zip']) ?></td>
            <td><?= htmlspecialchars($alert['timezone']) ?></td>
            <td><?= htmlspecialchars($alert['is_returning'] ? 'Yes' : 'No') ?></td>
            <td><?= htmlspecialchars($alert['os']) ?></td>
            <td><?= htmlspecialchars($alert['browser']) ?></td>
            <td><?= htmlspecialchars($alert['browser_engine']) ?></td>
            <td><?= htmlspecialchars($alert['device_type']) ?></td>
            <td><?= htmlspecialchars($alert['device_model']) ?></td>
            <td><img src="<?= htmlspecialchars($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $alert['flag_url']) ?>" alt="Flag"></td>
        </tr>
        <?php endforeach; ?>
    </table>
    <?php endif; ?>

</body>
</html>
