<?php
header('Content-Type: application/json');



// Set CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Load configuration
$config = include 'extractedconfig.php';

$host = $config['host'] ?? '';
$dbname = $config['dbname'] ?? '';
$username = $config['username'] ?? '';
$password = $config['password'] ?? '';



try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
    exit();
}

// Check if username is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['error' => 'Username parameter is missing']);
    exit();
}

$user_input = $_GET['id'];

// Query to get user_id from user_profiles
$query = $pdo->prepare("SELECT user_id FROM user_profiles WHERE username = :username");
$query->bindParam(':username', $user_input);
$query->execute();
$user = $query->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    exit();
}

$user_id = $user['user_id'];

// Query to get api value from edgesettings
$query = $pdo->prepare("SELECT api FROM edgesettings WHERE user_id = :user_id");
$query->bindParam(':user_id', $user_id);
$query->execute();
$edge = $query->fetch(PDO::FETCH_ASSOC);

if (!$edge) {
    echo json_encode(['error' => 'Edge settings not found for this user']);
    exit();
}

// Output the API value in JSON
echo json_encode(['api' => $edge['api']]);
?>
