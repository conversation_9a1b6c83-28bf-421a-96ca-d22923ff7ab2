body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

.container {
    width: 70%;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h2, h3 {
    text-align: center;
}

textarea, input[type="text"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    cursor: pointer;
    border-radius: 4px;
}

button:hover {
    background-color: #45a049;
}

.preview {
    border: 1px solid #ddd;
    padding: 15px;
    margin-top: 15px;
    background-color: #f9f9f9;
}

.history-entry {
    padding: 10px;
    background-color: #f1f1f1;
    margin-bottom: 10px;
    border-radius: 4px;
}

button.resend {
    background-color: #ff9800;
}

button.resend:hover {
    background-color: #e68900;
}

#next-button {
    margin-top: 20px;
    display: block;
    width: 100%;
    background-color: #2196F3;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
}

#next-button:hover {
    background-color: #1e88e5;
}