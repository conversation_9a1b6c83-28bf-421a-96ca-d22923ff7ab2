<?php
session_start();
// Include the extracted config.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Assuming you have a connection to your database using PDO
try {
  $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Step 1: Fetch subscription plans
$query = "SELECT * FROM openredirectplans";
$stmt = $pdo->query($query);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Step 2: Process the form submission
if (isset($_POST['update'])) {
    $planId = $_POST['update'];
    $newPrice = $_POST['price_' . $planId]; // Get the price from the input field
    
    // Validate the new price to ensure it's a valid number
    if (is_numeric($newPrice) && $newPrice >= 0) {
        // Step 3: Update the price for the selected plan
        $query = "UPDATE openredirectplans SET price = :price WHERE id = :id";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(":price", $newPrice, PDO::PARAM_STR);
        $stmt->bindParam(":id", $planId, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            // Set success flag in the session
            $_SESSION['price_update_success'] = true;
        } else {
          //  $_SESSION['price_update_success'] = false;
        }
    } else {
        //$_SESSION['price_update_success'] = false;
    }
}

if (count($result) > 0):
?>

<!-- Add some CSS for styling -->
<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f4f7fc;
        margin: 0;
        padding: 0;
    }
    h2 {
        text-align: center;
        color: #333;
        padding: 20px;
        background-color: #4CAF50;
        color: white;
    }
    table {
        width: 80%;
        margin: 20px auto;
        border-collapse: collapse;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    th, td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    th {
        background-color: #4CAF50;
        color: white;
    }
    tr:hover {
        background-color: #f1f1f1;
    }
    input[type="text"] {
        width: 100px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    button {
        padding: 8px 16px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    button:hover {
        background-color: #45a049;
    }
    .message {
        text-align: center;
        margin-top: 20px;
        font-size: 16px;
        color: green;
    }
    .error {
        color: red;
    }
</style>

<h2>Redirect Subscription Plans</h2>

<form action="" method="POST">
    <table>
        <thead>
            <tr>
                <th>Redirect Plan Name</th>
                <th>Duration (Days)</th>
                <th>Price $</th>
                <th>Update</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($result as $row): ?>
                <tr>
                    <td><?php echo htmlspecialchars($row['plan_name']); ?></td>
                    <td><?php echo htmlspecialchars($row['duration']); ?></td>
                    <td>
                        <input type="text" name="price_<?php echo $row['id']; ?>" value="<?php echo number_format($row['price'], 2); ?>" />
                    </td>
                    <td>
                        <button type="submit" name="update" value="<?php echo $row['id']; ?>">Update</button>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</form>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    if (typeof jQuery == 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }
</script>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Hidden input to trigger JS based on PHP session value -->
<input type="hidden" id="price-update-success" value="<?php echo isset($_SESSION['price_update_success']) ? (int)$_SESSION['price_update_success'] : 0; ?>" />

<!-- jQuery script for SweetAlert trigger -->
<script>
    $(document).ready(function() {
        // Check if the session value exists and trigger SweetAlert using jQuery
        var success = $('#price-update-success').val();

        if (success == 1) {
            Swal.fire({
                title: 'Success',
                text: 'Price updated successfully!',
                icon: 'success',
                confirmButtonText: 'Okay'
            }).then(function() {
            window.location.href = window.location.href; 
            });
        } 
    });
</script>

<?php
// Close the PDO connection
$pdo = null;

// Reset session variable
unset($_SESSION['price_update_success']);
?>

<?php else: ?>
    <p>No Redirect subscription plans found.</p>
<?php endif; ?>
