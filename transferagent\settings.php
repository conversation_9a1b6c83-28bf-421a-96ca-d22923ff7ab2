<?php
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Create a connection to the database
$mysqli = new mysqli($host, $username, $password, $dbname);

// Check for database connection errors
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

// Create the required table if it doesn't exist
$tableCreationQuery = "
    CREATE TABLE IF NOT EXISTS transferagent (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        bot_token VARCHAR(255) NOT NULL,
        allowed_chat_id VARCHAR(255) NOT NULL
    )
";

if ($mysqli->query($tableCreationQuery) !== TRUE) {
    die("Error creating transferagent table: " . $mysqli->error);
}

// Get the user_id of the logged-in user (assuming it's stored in the session)
$user_id = $_SESSION['user_id']; // Ensure you have user authentication implemented

// Handle the form submission
$response = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the data from the form
    $botToken = $_POST['botToken'];
    $allowedChatId = $_POST['allowedChatId'];

    // First, check if the user already has settings in the database
    $checkQuery = "SELECT * FROM transferagent WHERE user_id = ?";
    $checkStmt = $mysqli->prepare($checkQuery);
    $checkStmt->bind_param("i", $user_id);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    // Check if the user has a telegram bot associated with the same bot token and chat id
    $telegramCheckQuery = "SELECT * FROM telegram_settings WHERE user_id = ? AND telegram_token = ? AND telegram_id = ?";
    $telegramCheckStmt = $mysqli->prepare($telegramCheckQuery);
    $telegramCheckStmt->bind_param("iss", $user_id, $botToken, $allowedChatId);
    $telegramCheckStmt->execute();
    $telegramCheckResult = $telegramCheckStmt->get_result();

    // If the user has the same bot_token and allowed_chat_id, ask them to create a new Telegram bot
    if ($telegramCheckResult->num_rows > 0) {
        $response = ["success" => false, "message" => "The bot token and chat ID are already in use for your result sending. Please create a new Telegram bot for your transfer agent operation."];
    } else {
        // If user exists, update the settings, otherwise insert a new row
        if ($checkResult->num_rows > 0) {
            // User found, update settings
            $query = "UPDATE transferagent SET bot_token = ?, allowed_chat_id = ? WHERE user_id = ?";
            $stmt = $mysqli->prepare($query);
            $stmt->bind_param("ssi", $botToken, $allowedChatId, $user_id);
        } else {
            // User not found, insert new row
            $query = "INSERT INTO transferagent (user_id, bot_token, allowed_chat_id) VALUES (?, ?, ?)";
            $stmt = $mysqli->prepare($query);
            $stmt->bind_param("iss", $user_id, $botToken, $allowedChatId);
        }

        if ($stmt->execute()) {
            $response = ["success" => true, "message" => "Settings updated successfully!"];
        } else {
            $response = ["success" => false, "message" => "Failed to update settings."];
        }

        $stmt->close();
    }

    // Close the statement
    $checkStmt->close();
    $telegramCheckStmt->close();
}

// Close the database connection
$mysqli->close();

// Return the response as JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
