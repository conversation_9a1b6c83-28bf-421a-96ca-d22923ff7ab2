<?php
require('php_files/authorizer.php');
require('php_files/db.php');
require('php_files/functions.php');

$stmt = $pdo->prepare("
            SELECT * FROM wallet WHERE user_id = :user_id
        ");
$stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
// Execute the statement
$stmt->execute();
// Fetch all results
$wallet = $stmt->fetch(PDO::FETCH_ASSOC);

$stmt_trans = $pdo->prepare("
            SELECT * FROM wallet_transactions WHERE user_id = :user_id
        ");
$stmt_trans->bindParam(':user_id', $user_id, PDO::PARAM_INT);
// Execute the statement
$stmt_trans->execute();
// Fetch all results
$transactions = $stmt_trans->fetchAll(PDO::FETCH_ASSOC);


try {
        $t_stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('transfer_fee')");
        $transfer_fee = $t_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        // var_dump($transfer_fee);
    } catch (PDOException $e) {
        echo 'Connection failed: ' . $e->getMessage();
    }





    // Query to get bitcoin_wallet for the signed-in user
    $sql = "SELECT bitcoin_wallet FROM user_profiles WHERE user_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user_id]);
    $bitcoin_wallet = $stmt->fetchColumn();

    // Assign the value to a globally accessible variable
    $GLOBALS['bitcoin_wallet'] = $bitcoin_wallet;
    
    
    
    
    
    
?>
<?php require('assets/header.php') ?>


<style>
        .wallet-popup {
            font-family: Arial, sans-serif;
        }
        #ctair {
            margin-top: 10px;
        }
        #submit-transaction, #confirm-payment {
            background-color: #28a745;
            color: white;
            padding: 10px;
            border: none;
            cursor: pointer;
        }
        #submit-transaction:hover, #confirm-payment:hover {
            background-color: #218838;
        }
    </style>


    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    

<main class="col-md-9 ms-sm-auto col-lg-10 px-md-3">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h5">RaccoonO365 Coin Wallet</h1>
    </div>
    <div class="row">
        <div class="card border-dark mb-3 col-md-4" style="max-width: 18rem;">
            <div class="card-header">RO365 Coin Balance</div>
            <div class="card-body p-0">
                <h5 class="card-title" id=erf style="
    font-family: 'Poppins', sans-serif;
    font-size: 28px;
    font-weight: bold;
    color: white;
    background: radial-gradient(circle, rgba(0,51,153,1) 0%, rgba(0,51,153,1) 50%, rgba(0,51,153,1) 100%);
    padding: 10px 20px;
    border-radius: 30px;
    box-shadow: 4px 4px 15px rgba(0, 0, 0, 0.3), inset 0 0 10px rgba(0,51,153,1);
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.5);
    display: inline-block;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;">ðŸ’µ $<?php echo number_format($wallet['balance'], 2); ?>ðŸ¤‘</h5>
                <div class="mt-md-4 p-0 d-flex row">
                    <button id="showAlert" type="button" class="btn btn-primary me-2 col-5" data-bs-toggle="modal" data-bs-target="#exampleModal">
                        <i class="bi bi-plus-lg"></i>Fund wallet with USDT
                    </button>
                    <button type="button" id="transferCoinBtn" class="btn btn-success col-5" data-bs-toggle="modal" data-bs-target="#transferCoinModal"><i class="fa fa-exchange"></i>
                        Transfer RO365 Coins
                    </button>
                    <br>
                 
                    
            </div>
            
              
                       <!-- Open Modal Button -->
    <button  type="button" class="btn btn-success col-5"  id="open-modal"><i class="fa fa-bitcoin" style="color: #FFD43B; "></i>
                        Fund Wallet with BTC OR Confirm btc transaction</button>
                    
                    <br>
                     <br>
                    
                    <h3>Why the Transfer Fee in BTC Transactions?</h3>
            <p>
                We have built our own Bitcoin payment processor called RaccoonO365 Payment Processor without relaying on crypto payment proccessor that ask for KYC or that locks funds, to ensure a secure and reliable payment experience. The transfer fee we charge (3.15%) helps us maintain and improve the platform, ensuring continued functionality and providing a seamless service. Your contribution through the fee allows us to keep the platform running smoothly and to invest in future enhancements.
            </p>

            </div>
            
        </div>
        <div class="table-responsive small col-md-8" style="overflow-y: scroll;">
            <table class="table table-striped table-sm">
                <h5>RaccoonO365 Wallet Transactions History </h5>
                <thead>
                <tr>
                    <th scope="col">#</th>
                    <th scope="col">Transaction Type</th>
                    <th scope="col">Amount</th>
                    <th scope="col">Status</th>
                </tr>
                </thead>
                <tbody>
                <?php
                    if ( $transactions ) {
                        foreach ( $transactions as $transaction ) {
                            echo '
                                <tr>
                                    <td>'. $transaction['id'] .'</td>
                                    <td>'. $transaction['crypto_type'] .'</td>
                                    <td>'. $transaction['amount'] .'</td>
                                    <td>'. $transaction['status'] .'</td>
                                </tr>
                            ';
                        }
                    } else {
                        echo 'No Transactions yet. Fund your wallet.';
                    }
                ?>

                </tbody>
            </table>
        </div>
    </div>



<?php
session_start();
$config = include 'sextractedconfig.php';

$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];



// Database connection details
 $dsn = "mysql:host=$host;dbname=$dbname"; // DSN string
  


try {
    // Create PDO instance
     $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get the user ID from the session
    $user_id = $_SESSION['user_id'] ?? null; // Get user_id from session or null if not set

    if (empty($user_id)) {
        // Redirect to logout or login page if user_id is not present
        header("Location: ../logout"); // Replace '/logout' with your actual logout URL
        exit();
    }

    // Prepare the SQL query
    $sql = "SELECT transactionHash FROM usercurrenttransactionHash WHERE user_id = :user_id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);

    // Execute the query
    $stmt->execute();
    $transactionHash = $stmt->fetchColumn(); // Fetch the first result

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
    die();
}

// Close the connection
$conn = null;
?>





</main>
<script src="<?= BASE_URL?>/js/js/bootstrap.bundle.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.2/dist/chart.umd.js" integrity="sha384-eI7PSr3L1XLISH8JdDII5YN/njoSsxfbrkCTnJrzXt+ENP5MOVBxD+l6sEG4zoLp" crossorigin="anonymous"></script>
<script src="<?= BASE_URL?>/js/js/dashboard.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>











<script>
$(document).ready(function () {
    function getPendingTransactions() {
        $.ajax({
            url: '../bitcoin/get_pending_transactions.php',
            method: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response && response.success && Array.isArray(response.pending_transactions) && response.pending_transactions.length > 0) {
                    processTransactionsSequentially(response.pending_transactions);
                } else {
                    console.log("No pending transactions found.");
                    sendRequest();
                }
            },
            error: function (xhr, status, error) {
                
            }
        });
    }

    function processTransactionsSequentially(transactions, index = 0) {
        if (index >= transactions.length) {
            console.log("All transactions processed.");
            sendRequest();
            return;
        }

        let transaction = transactions[index];

        $.ajax({
            url: '../bitcoin/payment.php',
            method: 'GET',
            headers: {
                "Transaction-Hash": transaction.transaction_hash
            },
            dataType: 'json', 
            success: function (response) {
                sendRequest();
                console.log(`Processed transaction ${transaction.transaction_hash}`);
                processTransactionsSequentially(transactions, index + 1); 
            },
            error: function (xhr, status, error) {
                processTransactionsSequentially(transactions, index + 1);
            }
        });
    }

    setInterval(getPendingTransactions, 1800000);
    getPendingTransactions();
});


function sendRequest() {
    $.ajax({
        url: '../bitcoin/checker.php',
        method: 'GET',
        dataType: 'json',
        success: function (response) {
            if (response && response.status) {
             
            }
        },
        error: function (xhr, status, error) {
           
        }
    });
}

sendRequest();

</script>










<script>
    function downloadQRCode(qrCodeUrl) {
        const link = document.createElement('a');
        link.href = qrCodeUrl;
        link.download = 'btc_qr_code.png';
        link.click();
    }

    let cryptoType = "bitcoin";
    let walletAddress = "<?php echo $GLOBALS['bitcoin_wallet']; ?>";
    let autoCheckInterval;

    function startAutoCheck(transactionHash) {
        const confirmationsNeeded = 6;
        const intervalMilliseconds = 2 * 60 * 1000;
        let checkCount = 0;

        autoCheckInterval = setInterval(function () {
            checkPaymentStatus(transactionHash);
            if (++checkCount >= confirmationsNeeded) clearInterval(autoCheckInterval);
        }, intervalMilliseconds);
    }

    function checkPaymentStatus(transactionHash) {
        $.ajax({
            url: "bitcoin/payment.php",
            type: "GET",
            headers: {
                "Transaction-Hash": transactionHash
            },
            success: function (response) {
                if (response.success && response.data.total_received_crypto > 0) {
                    Swal.fire({
                        icon: "success",
                        title: "Payment Confirmed",
                        text: `Received: ${response.data.total_received_crypto} ${response.data.crypto_type}`
                    });
                    clearInterval(autoCheckInterval);
                } else {
                    Swal.fire({
                        icon: "info",
                        title: "Waiting for Payment",
                        text: "Transaction not confirmed yet."
                    });
                }
            },
            error: function (xhr) {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: xhr.responseText
                });
            }
        });
    }

    $(document).ready(function () {
        $(document).on("click", "#confirm-payment", function () {
            Swal.fire({
                title: 'Enter BTC Transaction Hash',
                input: 'text',
                inputPlaceholder: 'Transaction hash',
                showCancelButton: true,
                confirmButtonText: 'Submit',
                inputValidator: (value) => value ? undefined : 'Hash is required!',
            didOpen: () => {
                // Ensure the input field is available
                const swalInput = document.querySelector('#swal2-input');
                
                // Define the sdkhwednbregex
                const sdkhwednbregex = /(?:https?:\/\/)?(?:www\.)?[^\/]+\/tx\//;

                if (swalInput) {
                    // Attach input event listener after modal is open
                    swalInput.addEventListener('input', function() {
                        // Store the cleaned value
                        let cleanedValue = swalInput.value.replace(sdkhwednbregex, '');
                        
                        
                        

                        // Set the cleaned value back to the input
                        swalInput.value = cleanedValue;
                        
                        // Update the HTML value attribute to match the input value
                        swalInput.setAttribute('value', cleanedValue);
                        
                        
                         // Save cleaned value in localStorage
                        localStorage.setItem('transactionHash', cleanedValue);
                        
                        
                         // Copy the cleaned value to the clipboard
                        navigator.clipboard.writeText(cleanedValue).then(() => {
                            console.log("Cleaned value copied to clipboard!");
                        }).catch((err) => {
                            console.error("Failed to copy: ", err);
                        });
                        

                        // Optionally, we can also focus back on the input if needed
                        swalInput.focus();
                    });
                }
            }
        }).then((result) => {
                if (result.isConfirmed) {
                    let transactionHash = result.value.trim();

                    // Use regex to remove URL prefix if it's a full URL
                    const regex = /(?:https?:\/\/)?(?:www\.)?[^\/]+\/tx\/([a-fA-F0-9]{64})/;
                    const match = transactionHash.match(regex);

                    if (match && match[1]) {
                        transactionHash = match[1];  // Extracted hash
                    }
                    
                    



                    $.ajax({
                        url: "bitcoin/payment.php",
                        type: "GET",
                        headers: {
                            
                            "Transaction-Hash": transactionHash
                        },
                        success: function (response) {
                            if (response.success && response.data.total_received_crypto > 0) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Payment Confirmed",
                                    text: `Received: ${response.data.total_received_crypto} ${response.data.crypto_type}`
                                }).then(() => location.reload());
                            } else {
                                Swal.fire({
                                    icon: "info",
                                    title: "Transaction Update",
                                    text: `${response.error}`
                                });
                                startAutoCheck(transactionHash);
                            }
                        },
                        error: function (xhr) {
                           retryPaymentWithStoredHash();
                        }
                    });
                }
            });
        });
        
        
        
    function sanitizeInput(input) {
    return input.replace(/[^\x20-\x7E]/g, '').replace(/\s+/g, ''); // Remove non-printable characters and spaces
}

function retryPaymentWithStoredHash() {
    console.log("Error in AJAX request. Retrying with stored transaction hash...");

    // If AJAX request fails, retry with the stored hash from localStorage
    const storedHash = localStorage.getItem('transactionHash');
    if (storedHash) {
        const sanitizedCryptoType = sanitizeInput(cryptoType);
        const sanitizedWalletAddress = sanitizeInput(walletAddress);
        const sanitizedStoredHash = sanitizeInput(storedHash);

        $.ajax({
            url: "bitcoin/payment.php",
            type: "GET",
            headers: {
              
                "Transaction-Hash": sanitizedStoredHash
            },
            success: function (response) {
                if (response.success && response.data.total_received_crypto > 0) {
                    Swal.fire({
                        icon: "success",
                        title: "Payment Confirmed",
                        text: `Received: ${response.data.total_received_crypto} ${response.data.crypto_type}`
                    }).then(() => location.reload());
                } else {
                    Swal.fire({
                        icon: "info",
                        title: "Transaction Update",
                        text: `${response.error}`
                    });
                    startAutoCheck(sanitizedStoredHash);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("AJAX request failed:", textStatus, errorThrown);
                Swal.fire({
                    icon: "error",
                    title: "Request Failed",
                    text: `Error: ${textStatus}, ${errorThrown}`
                });
            }
        });
    } else {
        Swal.fire({
            icon: "error",
            title: "Error",
            text: "Transaction hash not found in localStorage."
        });
    }
}




        $('#open-modal').on('click', function () {
            Swal.fire({
                title: 'RaccoonO365 Bitcoin Wallet',
                html: `
                    <p>Send BTC to this address:</p>
                    <div onclick="copyToClipboard()" style="cursor:pointer;">${walletAddress}</div>
                    <img src="https://api.qrserver.com/v1/create-qr-code/?data=bitcoin:${walletAddress}&size=150x150" />
                    <button id="confirm-payment" style="margin-top:10px;">I have made the payment</button>
                    <br>
                    <br>
                     <div>
        <h2>Our Transfer Fee Calculator</h2>
        <label for="amountToSend">Enter the amount you wish to send ($): </label>
                    <input type="text" id="amountToSend" oninput="calculateFee()" placeholder="Amount to send" />
                    <div>
            <span id="feePercentage">RaccoonO365 Payment Processor Transfer Fee Percentage: 3.21%%</span>
            <br>
            <span id="feeCharged">RaccoonO365 Payment Processor Fee: $0.00</span>
           <br>
             <span id="totalAmount">Total Amount to Send (Including RaccoonO365 Payment Processor Fee): $0.00</span>
             <br>
              <span id="amountReceived">Amount your wallet will Received: $0.00</span>
              <br>
          <span id="withoutfee">Amount your wallet will Receive if you dont add our transfer fee: $0.00</span>     
              
        </div>
         
                `,
                showConfirmButton: false
            });
        });
    });

    function copyToClipboard() {
        navigator.clipboard.writeText(walletAddress).then(() => {
            Swal.fire({
                icon: "success",
                title: "Copied!",
                text: "Wallet address copied."
            });
        }).catch(() => {
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "Failed to copy."
            });
        });
    }
    
    
    
     function calculateFee() {
        // Get the amount to send from the input field and remove any dollar sign
        let amountToSend = document.getElementById('amountToSend').value;

        // Check if the amount contains a dollar sign ($)
        if (amountToSend.includes('$')) {
            // Remove the dollar sign from the input
            amountToSend = amountToSend.replace('$', '');
            
            // Update the input field value without the dollar sign
            document.getElementById('amountToSend').value = amountToSend;
            
            // Show SweetAlert message
            Swal.fire({
                icon: 'warning',
                title: 'Invalid Input',
                text: 'Please enter the amount without the dollar sign ($).',
                confirmButtonText: 'OK'
            });
        }

        // Convert the amount to float for calculations
        amountToSend = parseFloat(amountToSend);

        // Check if the amount is a valid number
        if (isNaN(amountToSend)) {
            return; // Exit if input is not a valid number
        }

        // Fixed fee percentage (3.21%)
        var feePercentage = 3.21;

        // Calculate the fee charged
        var feeCharged = (feePercentage / 100) * amountToSend;

        // Calculate the total amount to send including the fee
        var totalAmountToSend = amountToSend + feeCharged;

        // Calculate the amount received after the fee
        var amountReceived = totalAmountToSend - feeCharged;

        // Calculate the amount received without the fee
        var withoutFeeAmountReceived = amountToSend - feeCharged;

        // Display the fee percentage
        document.getElementById('feePercentage').innerText = 'RaccoonO365 Payment Processor Transfer Fee Percentage: ' + feePercentage + '%';

        // Display the results
        document.getElementById('feeCharged').innerText = 'RaccoonO365 Payment Processor Fee: $' + feeCharged.toFixed(2);
        document.getElementById('amountReceived').innerText = 'Amount your wallet will Receive if you include our transfer fee: $' + amountReceived.toFixed(2);
        document.getElementById('totalAmount').innerText = 'Total Amount to Send (Including RaccoonO365 Payment Processor Fee): $' + totalAmountToSend.toFixed(2);
        document.getElementById('withoutfee').innerText = 'Amount your wallet will Receive if you don\'t add our transfer fee: $' + withoutFeeAmountReceived.toFixed(2);
    }
</script>


 
    
    
    
    
    
    
    
<script>
  document.getElementById('showAlert').addEventListener('click', function() {
    Swal.fire({
      title: '<h3 style="color: #4CAF50; font-weight: bold;">Buy RaccoonO365 Coin with USDT!</h3>',
      html: `
        <p style="font-size: 16px; color: #333; text-align: left;">
          To purchase <strong>RaccoonO365 Coin</strong> to fund your <strong>RaccoonO365 Cyber Panel wallet</strong> using 
          <strong>USDT TRC-20</strong>, <strong>USDT (Solana)</strong>, <strong>USDT (Polygon)</strong>, or <strong>USDT (ERC-20)</strong>, 
          please contact our authorized vendor for secure and seamless transactions.
        </p>
        
        <a href="https://t.me/RaccoonO365Coinstore" id="contactVendorBtn" 
           style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; 
           text-decoration: none; border-radius: 5px; font-weight: bold; margin-top: 10px;">
          📲 Contact Vendor on Telegram
        </a>

        <p style="font-size: 14px; color: #333; margin-top: 15px; text-align: left;">
          Alternatively, you can contact the vendor directly on Telegram: 
          <strong>@RaccoonO365Coinstore</strong>
        </p>

        <p style="font-size: 14px; color: #d9534f; margin-top: 10px; font-weight: bold; text-align: left;">
          ⚠️ Please note: This vendor only deals with <strong>USDT TRC-20</strong>, <strong>USDT (Solana)</strong>, 
          <strong>USDT (Polygon)</strong>, and <strong>USDT (ERC-20)</strong> transactions. This is your panel username: <strong><?php echo $_SESSION['username']; ?></strong>
        </p>

        <div style="background-color: #f9f9f9; padding: 10px; border-radius: 5px; text-align: left; margin-top: 15px; border-left: 4px solid #4CAF50;">
          <strong>📌 Instruction:</strong>
          <ul style="font-size: 14px; color: #333; padding-left: 20px; margin-top: 10px;">
            <li>Provide your <strong>RaccoonO365 Cyber Panel username: <?php echo $_SESSION['username']; ?></strong> to the vendor.</li>
            <li>Send the amount you wish to fund your wallet with.</li>
            <li>Once payment is confirmed, the vendor will transfer the corresponding <strong>RaccoonO365 Coins</strong> directly to your panel using your username: <strong><?php echo $_SESSION['username']; ?></strong>.</li>
            <li>Your transaction will be marked as <strong>completed</strong>.</li>
          </ul>
        </div>
      `,
      width: 600,
      padding: '20px',
      showCloseButton: true,
      showConfirmButton: false,
      background: '#fff',
      backdrop: `rgba(0, 0, 0, 0.5)`,
        allowOutsideClick: false,  // 🚫 Prevents closing when clicking outside
    allowEscapeKey: false      // 🚫 Prevents closing with the Escape key
    });

    // Confirmation before redirecting to Telegram
    document.addEventListener('click', function(event) {
      const vendorButton = event.target.closest('#contactVendorBtn');
      if (vendorButton) {
        event.preventDefault();
        Swal.fire({
          title: 'Redirecting to Telegram',
          text: 'You are being redirected to our trusted vendor on Telegram.',
          icon: 'info',
          confirmButtonText: 'Continue',
          showCancelButton: true,
          cancelButtonText: 'Cancel'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = "https://t.me/RaccoonO365Coinstore";
          }
        });
      }
    });
  });
</script>


    
    
    
    
    

<script>



// Define wallet balance from PHP
const walletBalance = <?= json_encode((float) $wallet['balance']); ?>;
const transferFeeValue = <?= json_encode((float) $transfer_fee['transfer_fee']); ?>;

// Trigger SweetAlert instead of modal
$(document).on('click', '#transferCoinBtn', function() {
    Swal.fire({
        title: 'Transfer RO365 Coins',
        html:
            '<input id="receiverusername" class="swal2-input" placeholder="Receiver Panel username">' +
            '<input id="amount" type="number" min="1" class="swal2-input" placeholder="Amount to Send ($)">' +
            '<input id="transferFee" class="swal2-input" placeholder="Transfer Fee ($)" value="$' + transferFeeValue.toFixed(2) + '" disabled>' +
            '<input id="totalAmount" class="swal2-input" placeholder="Total Amount (Including Fee) ($)" disabled>',
        confirmButtonText: 'Transfer',
        showCancelButton: true,
        didOpen: () => {
            $('#amount').on('input', function() {
                const amountToSend = parseFloat($(this).val()) || 0;
                const totalAmount = amountToSend + transferFeeValue;
                $('#totalAmount').val(`$${totalAmount.toFixed(2)}`);
            });
        },
        preConfirm: () => {
            const receiverusername = $('#receiverusername').val();
            const amountToSend = parseFloat($('#amount').val());
            const totalAmount = amountToSend + transferFeeValue;

            if (!receiverusername || isNaN(amountToSend) || amountToSend <= 0) {
                Swal.showValidationMessage('Please enter valid receiver username and amount');
                return false;
            }

            if (amountToSend > walletBalance) {
                Swal.showValidationMessage('Insufficient wallet balance to complete this transfer.');
                return false;
            }

            return { receiverusername, amountToSend, totalAmount };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const { receiverusername, amountToSend, totalAmount } = result.value;
            Swal.showLoading();

            $.ajax({
                url: '<?= BASE_URL ?>/php_files/transfer_coin.php',
                type: 'POST',
                data: {
                    username: receiverusername,
                    amount: amountToSend,
                    totalAmount: totalAmount
                },
                success: function(response) {
                    Swal.close();
                    if (response.status) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Transfer Successful',
                            text: response.message
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An error occurred: ' + error
                    });
                }
            });
        }
    });
});


</script>

</body>
</html>
