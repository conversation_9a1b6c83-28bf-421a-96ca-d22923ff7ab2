<?php
session_start();

// Include the extractedconfig.php which has the values from config.php
$config = include 'sextractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

try {
    // Create a PDO connection to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Function to get botToken, allowedChatId, and user_id from transferagent table
function getBotDetails($pdo, $chatId) {
    $query = "SELECT bot_token, allowed_chat_id, user_id FROM transferagent WHERE allowed_chat_id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$chatId]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row) {
        return [
            'botToken' => $row['bot_token'],
            'allowedChatId' => $row['allowed_chat_id'],
            'userId' => $row['user_id'] // Make sure the user_id is fetched
        ];
    }
    return null; // Return null if no data found
}

// Ensure the `pending_payments` table exists using PDO
$createTableQuery = "
CREATE TABLE IF NOT EXISTS pending_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    status ENUM('pending', 'confirmed', 'failed') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(transaction_hash)
)";
$pdo->exec($createTableQuery);  // Using PDO's exec method for creating table

// Function to send a message to the chat
function sendMessage($chatId, $message, $botToken) {
    $url = "https://api.telegram.org/bot$botToken/sendMessage";
    $params = [
        'chat_id' => $chatId,
        'text' => $message,
        'parse_mode' => 'Markdown',
    ];

    file_get_contents($url . '?' . http_build_query($params));
}

// Get the incoming message
$content = file_get_contents("php://input");
$update = json_decode($content, true);

// Check if message is from a valid chat ID
if (isset($update['message'])) {
    $chatId = $update['message']['chat']['id'];
    $text = $update['message']['text'];

    // Debug: log received message
    file_put_contents('debug_log.txt', "Received: $text | Chat ID: $chatId\n", FILE_APPEND);
    
    // Check if the incoming message is "cancel"
    if (strtolower($text) == 'cancel') {
        sendMessage($chatId, "All functions have been canceled.", $botToken);
        exit; // Exit the script to cancel all further processing
    }

    $text = str_replace('$', '', $text);

    // Get bot details from transferagent table for the chat_id
    $botDetails = getBotDetails($pdo, $chatId);
    
    if (!$botDetails) {
        sendMessage($chatId, "You are not authorized to use this bot.", $botDetails['botToken']);
        exit;
    }

    $botToken = $botDetails['botToken'];
    $allowedChatId = $botDetails['allowedChatId'];
    $userId = $botDetails['userId']; // Get the user_id from the bot details

    // Log the user_id for debugging purposes
    file_put_contents('debug_log.txt', "User ID: $userId\n", FILE_APPEND);
    
     // Create a session if it's not already set
    if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != $userId) {
        $_SESSION['user_id'] = $userId;
        file_put_contents('debug_log.txt', "Session started for User ID: $userId\n", FILE_APPEND);
    }
    
    // Check if the message is from the allowed chat ID
    if ($chatId == $allowedChatId) {
         // Removed the "paid" command check to allow processing transaction hash immediately
        if (preg_match('/^(https?:\/\/)?(www\.)?[^\/]+\/tx\/([a-fA-F0-9]{64})/', $text, $matches)) {
            $transactionHash = $matches[3];
        } elseif (preg_match('/^[a-fA-F0-9]{64}$/', $text)) {
            $transactionHash = $text;
        } else {
            sendMessage($chatId, "Invalid input. Please enter a valid BTC transaction hash or link.", $botToken);
            exit;
        }

        // Store transaction hash for monitoring using the user_id from the transferagent table
        $stmt = $pdo->prepare("INSERT INTO pending_payments (user_id, transaction_hash, status) 
                               VALUES (:user_id, :transaction_hash, 'pending') 
                               ON DUPLICATE KEY UPDATE transaction_hash = :transaction_hash");
        $stmt->execute([
            'user_id' => $userId, // Use user_id from the transferagent table
            'transaction_hash' => $transactionHash
        ]);

        sendMessage($chatId, "⌛ Payment is being monitored. You will be notified once confirmed.", $botToken);

        // Check if the transaction is confirmed using an external API
        $confirmationStatus = checkPaymentStatus($chatId, $transactionHash, $pdo, $botToken, $userId);

        if ($confirmationStatus['confirmed']) {
            sendMessage($chatId, "✅ Your transaction has been confirmed: $transactionHash", $botToken);
        } else {
            sendMessage($chatId, "❌ Your transaction is still pending. Please try again later.", $botToken);
        }
    }
}

function checkPaymentStatus($chatId, $transactionHash, $pdo, $botToken, $userId)
{
    // Auto-detect BASE_URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . "://" . $host;

    // Log base URL and start of function
    file_put_contents('debug_log.txt', "Start checking payment status for hash: $transactionHash at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

    // Use the user_id fetched from transferagent table, not the chatId
    $stmt = $pdo->prepare("SELECT bitcoin_wallet FROM user_profiles WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);  // Use user_id here
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    
     // Create a session if it's not already set
if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != $user['user_id']) {
    $_SESSION['user_id'] = $user['user_id'];
    file_put_contents('debug_log.txt', "checkPaymentStatus Session started for User ID: {$user['user_id']}\n", FILE_APPEND);
}

    if ($user && isset($user['bitcoin_wallet'])) {
        $walletAddress = $user['bitcoin_wallet'];
        // Log wallet address
        file_put_contents('debug_log.txt', "Found wallet address for user: $walletAddress\n", FILE_APPEND);

        $apiUrl = $baseUrl . "/bitcoin/botpayment.php";
        $headers = [
            "crypto-type: BTC",
            "Wallet-Address: $walletAddress",
            "transaction-hash: $transactionHash"
        ];

        $maxRetries = 1;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            // Log that we are about to make the API call
            file_put_contents('debug_log.txt', "Making API call to: $apiUrl\n", FILE_APPEND);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // Log the response and HTTP status code
            file_put_contents('debug_log.txt', "API Response: $response\n", FILE_APPEND);
            file_put_contents('debug_log.txt', "HTTP Status Code: $httpStatus\n", FILE_APPEND);

            if ($httpStatus == 200) {
                $result = json_decode($response, true);

                // Log the result from the API response
                file_put_contents('debug_log.txt', "Decoded API result: " . print_r($result, true) . "\n", FILE_APPEND);

                if (isset($result['success']) && $result['success'] && $result['data']['total_received_crypto'] > 0) {
                    sendMessage($chatId, "✅ Payment Confirmed!\nReceived: " . $result['data']['total_received_crypto'] . " BTC", $botToken);

                    // Update DB to mark payment as confirmed
                    $updateStmt = $pdo->prepare("UPDATE pending_payments SET status = 'confirmed' WHERE transaction_hash = :transaction_hash");
                    $updateStmt->execute(['transaction_hash' => $transactionHash]);

                    // Log successful payment confirmation
                    file_put_contents('debug_log.txt', "Payment confirmed for transaction hash: $transactionHash\n", FILE_APPEND);
                    break; // Exit the loop once payment is confirmed
                } else {
                    // Log that payment is still pending
                    file_put_contents('debug_log.txt', "Payment not confirmed yet for transaction hash: $transactionHash\n", FILE_APPEND);
                    sendMessage($chatId, "⌛ Still waiting for payment confirmation. Checking again in 2 minutes...", $botToken);
                    sleep(120); // Wait for 2 minutes before rechecking
                }
            } else {
                // Log that there was an error verifying the payment
                file_put_contents('debug_log.txt', "Error verifying payment. HTTP status: $httpStatus\n", FILE_APPEND);
                sendMessage($chatId, "❌ Error verifying payment. Retrying in 2 minutes...", $botToken);
                sleep(120);
            }

            // Increment retry count
            $retryCount++;
        }

        // If retry limit is reached
        if ($retryCount >= $maxRetries) {
            file_put_contents('debug_log.txt', "Max retry attempts reached for transaction hash: $transactionHash\n", FILE_APPEND);
            sendMessage($chatId, "❌ Max retries reached. Payment still not confirmed. Please try again later.", $botToken);
        }
    } else {
        // Log that the wallet address was not found
        file_put_contents('debug_log.txt', "Wallet address not found for user_id: $userId\n", FILE_APPEND);
        sendMessage($chatId, "❌ Wallet address not found. Please contact support.", $botToken);
    }
}
?>
