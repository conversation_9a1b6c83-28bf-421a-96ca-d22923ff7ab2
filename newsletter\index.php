


<?php
// Database connection settings
// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Database connection parameters
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];
 

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// SQL to create the newsletter_history table if it does not exist
$tableCreationQuery = "
    CREATE TABLE IF NOT EXISTS newsletter_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        status ENUM('sent', 'failed') NOT NULL DEFAULT 'sent',
        email VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
";

// Execute the query to create the table
try {
    $pdo->exec($tableCreationQuery);
   
} catch (PDOException $e) {
    
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Newsletter</title>
    <link rel="stylesheet" href="styles.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.29/dist/sweetalert2.min.css">
</head>
<body>
    <div class="container">
        <h2>Send Newsletter</h2>
        <form id="newsletterForm" method="POST">
            <label for="subject">Email Subject:</label>
            <input type="text" id="subject" name="subject" placeholder="Enter email subject" required>

            <label for="email_content">HTML Email Content:</label>
            <textarea id="email_content" name="email_content" rows="10" placeholder="Paste your HTML email content here" required></textarea>

            <button type="submit">Send Newsletter</button>
        </form>

        <h3>Preview:</h3>
        <div id="preview" class="preview">
            <!-- Preview of email content will show here -->
        </div>

        <h3>Email History:</h3>
        <div id="history-container"></div>

        <!-- Pagination Controls -->
        <button id="previous-button" disabled>Previous</button>
        <button id="next-button">Next</button>

        <!-- Clear All History Button -->
        <button id="clear-history-button">Clear All History</button>
    </div>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.29/dist/sweetalert2.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Preview email content
        $('#email_content').on('input', function() {
            var content = $(this).val();
            $('#preview').html(content);
        });

        // Handle form submission
        $('#newsletterForm').submit(function(e) {
            e.preventDefault();
            var formData = $(this).serialize();  // Serialize the form data
            $.ajax({
                url: 'send_newsletter.php',
                type: 'POST',
                data: formData,  // Send serialized data
                success: function(response) {
                    // Use SweetAlert to display the message
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    loadHistory(); // Reload history after sending
                },
                error: function(xhr, status, error) {
                    // Use SweetAlert for errors
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred while sending the newsletter.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    console.log('Error:', error); // Log any errors for debugging
                }
            });
        });

        // Load email history with pagination
        let currentPage = 1;
        let totalPages = 1;

        function loadHistory(page = 1) {
            $.ajax({
                url: 'fetch_history.php',
                type: 'GET',
                data: {page: page},
                success: function(response) {
                    // Update history container with new data
                    $('#history-container').html(response);
                    
                    // Get the total pages from the response
                    totalPages = $('#history-container').data('total-pages') || 1;

                    // Enable/disable pagination buttons based on the current page
                    $('#previous-button').prop('disabled', currentPage === 1);
                    $('#next-button').prop('disabled', currentPage === totalPages);
                }
            });
        }

        // Handle next button click for pagination
        $('#next-button').click(function() {
            if (currentPage < totalPages) {
                currentPage++;
                loadHistory(currentPage);
            }
        });

        // Handle previous button click for pagination
        $('#previous-button').click(function() {
            if (currentPage > 1) {
                currentPage--;
                loadHistory(currentPage);
            }
        });

        // Function to resend a failed email from history
        $(document).on('click', '.resendBtn', function() {
            var id = $(this).data('id');
            $.ajax({
                url: 'resend_newsletter.php',
                type: 'POST',
                data: { id: id },
                success: function(response) {
                    // Use SweetAlert to notify the user
                    Swal.fire({
                        title: 'Success!',
                        text: 'Newsletter resent successfully!',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    loadHistory(currentPage);  // Reload history to show the updated status
                },
                error: function(xhr, status, error) {
                    // Use SweetAlert for errors
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to resend the newsletter.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    console.log('Error:', error); // Log any errors for debugging
                }
            });
        });
        
        // Clear all email history
        $('#clear-history-button').click(function() {
            Swal.fire({
                title: 'Are you sure?',
                text: "This action will delete all the email history!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, clear it!',
                cancelButtonText: 'No, cancel!',
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: 'clear_history.php',
                        type: 'POST',
                        success: function(response) {
                            Swal.fire({
                                title: 'Cleared!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                            loadHistory();  // Reload history after clearing
                        },
                        error: function(xhr, status, error) {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to clear the history.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                            console.log('Error:', error); // Log any errors for debugging
                        }
                    });
                }
            });
        });
        
        // Initial load of history
        loadHistory();
    </script>
</body>
</html>


